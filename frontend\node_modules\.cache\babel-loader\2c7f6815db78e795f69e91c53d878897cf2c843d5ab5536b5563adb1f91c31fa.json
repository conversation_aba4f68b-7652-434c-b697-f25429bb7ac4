{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.keydownBehavior = void 0;\nvar _utils = require(\"../../utils\");\n\n/**\n * This file should contain behavior for arrow keys as described here:\n * https://w3c.github.io/uievents-code/#key-arrowpad-section\n */\nconst keydownBehavior = [{\n  // TODO: implement for contentEditable\n  matches: (keyDef, element) => (keyDef.key === 'ArrowLeft' || keyDef.key === 'ArrowRight') && (0, _utils.isElementType)(element, ['input', 'textarea']),\n  handle: (keyDef, element) => {\n    var _ref;\n    const {\n      selectionStart,\n      selectionEnd\n    } = (0, _utils.getSelectionRange)(element);\n    const direction = keyDef.key === 'ArrowLeft' ? -1 : 1;\n    const newPos = (_ref = selectionStart === selectionEnd ? (selectionStart != null ? selectionStart : /* istanbul ignore next */\n    0) + direction : direction < 0 ? selectionStart : selectionEnd) != null ? _ref : /* istanbul ignore next */\n    0;\n    (0, _utils.setSelectionRange)(element, newPos, newPos);\n  }\n}];\nexports.keydownBehavior = keydownBehavior;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "keydownBehavior", "_utils", "require", "matches", "keyDef", "element", "key", "isElementType", "handle", "_ref", "selectionStart", "selectionEnd", "getSelectionRange", "direction", "newPos", "setSelectionRange"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/user-event/dist/keyboard/plugins/arrow.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.keydownBehavior = void 0;\n\nvar _utils = require(\"../../utils\");\n\n/**\n * This file should contain behavior for arrow keys as described here:\n * https://w3c.github.io/uievents-code/#key-arrowpad-section\n */\nconst keydownBehavior = [{\n  // TODO: implement for contentEditable\n  matches: (keyDef, element) => (keyDef.key === 'ArrowLeft' || keyDef.key === 'ArrowRight') && (0, _utils.isElementType)(element, ['input', 'textarea']),\n  handle: (keyDef, element) => {\n    var _ref;\n\n    const {\n      selectionStart,\n      selectionEnd\n    } = (0, _utils.getSelectionRange)(element);\n    const direction = keyDef.key === 'ArrowLeft' ? -1 : 1;\n    const newPos = (_ref = selectionStart === selectionEnd ? (selectionStart != null ? selectionStart :\n    /* istanbul ignore next */\n    0) + direction : direction < 0 ? selectionStart : selectionEnd) != null ? _ref :\n    /* istanbul ignore next */\n    0;\n    (0, _utils.setSelectionRange)(element, newPos, newPos);\n  }\n}];\nexports.keydownBehavior = keydownBehavior;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,eAAe,GAAG,KAAK,CAAC;AAEhC,IAAIC,MAAM,GAAGC,OAAO,CAAC,aAAa,CAAC;;AAEnC;AACA;AACA;AACA;AACA,MAAMF,eAAe,GAAG,CAAC;EACvB;EACAG,OAAO,EAAEA,CAACC,MAAM,EAAEC,OAAO,KAAK,CAACD,MAAM,CAACE,GAAG,KAAK,WAAW,IAAIF,MAAM,CAACE,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,EAAEL,MAAM,CAACM,aAAa,EAAEF,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;EACtJG,MAAM,EAAEA,CAACJ,MAAM,EAAEC,OAAO,KAAK;IAC3B,IAAII,IAAI;IAER,MAAM;MACJC,cAAc;MACdC;IACF,CAAC,GAAG,CAAC,CAAC,EAAEV,MAAM,CAACW,iBAAiB,EAAEP,OAAO,CAAC;IAC1C,MAAMQ,SAAS,GAAGT,MAAM,CAACE,GAAG,KAAK,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC;IACrD,MAAMQ,MAAM,GAAG,CAACL,IAAI,GAAGC,cAAc,KAAKC,YAAY,GAAG,CAACD,cAAc,IAAI,IAAI,GAAGA,cAAc,GACjG;IACA,CAAC,IAAIG,SAAS,GAAGA,SAAS,GAAG,CAAC,GAAGH,cAAc,GAAGC,YAAY,KAAK,IAAI,GAAGF,IAAI,GAC9E;IACA,CAAC;IACD,CAAC,CAAC,EAAER,MAAM,CAACc,iBAAiB,EAAEV,OAAO,EAAES,MAAM,EAAEA,MAAM,CAAC;EACxD;AACF,CAAC,CAAC;AACFhB,OAAO,CAACE,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}