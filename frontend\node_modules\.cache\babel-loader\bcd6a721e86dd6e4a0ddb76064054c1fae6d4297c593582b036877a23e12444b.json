{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"enableAccessibleFieldDOMStructure\"],\n  _excluded2 = [\"InputProps\", \"readOnly\"],\n  _excluded3 = [\"onPaste\", \"onKeyDown\", \"inputMode\", \"readOnly\", \"InputProps\", \"inputProps\", \"inputRef\"];\nexport const convertFieldResponseIntoMuiTextFieldProps = _ref => {\n  let {\n      enableAccessibleFieldDOMStructure\n    } = _ref,\n    fieldResponse = _objectWithoutPropertiesLoose(_ref, _excluded);\n  if (enableAccessibleFieldDOMStructure) {\n    const {\n        InputProps,\n        readOnly\n      } = fieldResponse,\n      other = _objectWithoutPropertiesLoose(fieldResponse, _excluded2);\n    return _extends({}, other, {\n      InputProps: _extends({}, InputProps ?? {}, {\n        readOnly\n      })\n    });\n  }\n  const {\n      onPaste,\n      onKeyDown,\n      inputMode,\n      readOnly,\n      InputProps,\n      inputProps,\n      inputRef\n    } = fieldResponse,\n    other = _objectWithoutPropertiesLoose(fieldResponse, _excluded3);\n  return _extends({}, other, {\n    InputProps: _extends({}, InputProps ?? {}, {\n      readOnly\n    }),\n    inputProps: _extends({}, inputProps ?? {}, {\n      inputMode,\n      onPaste,\n      onKeyDown,\n      ref: inputRef\n    })\n  });\n};", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "_excluded3", "convertFieldResponseIntoMuiTextFieldProps", "_ref", "enableAccessibleFieldDOMStructure", "fieldResponse", "InputProps", "readOnly", "other", "onPaste", "onKeyDown", "inputMode", "inputProps", "inputRef", "ref"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/internals/utils/convertFieldResponseIntoMuiTextFieldProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"enableAccessibleFieldDOMStructure\"],\n  _excluded2 = [\"InputProps\", \"readOnly\"],\n  _excluded3 = [\"onPaste\", \"onKeyDown\", \"inputMode\", \"readOnly\", \"InputProps\", \"inputProps\", \"inputRef\"];\nexport const convertFieldResponseIntoMuiTextFieldProps = _ref => {\n  let {\n      enableAccessibleFieldDOMStructure\n    } = _ref,\n    fieldResponse = _objectWithoutPropertiesLoose(_ref, _excluded);\n  if (enableAccessibleFieldDOMStructure) {\n    const {\n        InputProps,\n        readOnly\n      } = fieldResponse,\n      other = _objectWithoutPropertiesLoose(fieldResponse, _excluded2);\n    return _extends({}, other, {\n      InputProps: _extends({}, InputProps ?? {}, {\n        readOnly\n      })\n    });\n  }\n  const {\n      onPaste,\n      onKeyDown,\n      inputMode,\n      readOnly,\n      InputProps,\n      inputProps,\n      inputRef\n    } = fieldResponse,\n    other = _objectWithoutPropertiesLoose(fieldResponse, _excluded3);\n  return _extends({}, other, {\n    InputProps: _extends({}, InputProps ?? {}, {\n      readOnly\n    }),\n    inputProps: _extends({}, inputProps ?? {}, {\n      inputMode,\n      onPaste,\n      onKeyDown,\n      ref: inputRef\n    })\n  });\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,mCAAmC,CAAC;EACrDC,UAAU,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC;EACvCC,UAAU,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,CAAC;AACxG,OAAO,MAAMC,yCAAyC,GAAGC,IAAI,IAAI;EAC/D,IAAI;MACAC;IACF,CAAC,GAAGD,IAAI;IACRE,aAAa,GAAGP,6BAA6B,CAACK,IAAI,EAAEJ,SAAS,CAAC;EAChE,IAAIK,iCAAiC,EAAE;IACrC,MAAM;QACFE,UAAU;QACVC;MACF,CAAC,GAAGF,aAAa;MACjBG,KAAK,GAAGV,6BAA6B,CAACO,aAAa,EAAEL,UAAU,CAAC;IAClE,OAAOH,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;MACzBF,UAAU,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAES,UAAU,IAAI,CAAC,CAAC,EAAE;QACzCC;MACF,CAAC;IACH,CAAC,CAAC;EACJ;EACA,MAAM;MACFE,OAAO;MACPC,SAAS;MACTC,SAAS;MACTJ,QAAQ;MACRD,UAAU;MACVM,UAAU;MACVC;IACF,CAAC,GAAGR,aAAa;IACjBG,KAAK,GAAGV,6BAA6B,CAACO,aAAa,EAAEJ,UAAU,CAAC;EAClE,OAAOJ,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;IACzBF,UAAU,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAES,UAAU,IAAI,CAAC,CAAC,EAAE;MACzCC;IACF,CAAC,CAAC;IACFK,UAAU,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAEe,UAAU,IAAI,CAAC,CAAC,EAAE;MACzCD,SAAS;MACTF,OAAO;MACPC,SAAS;MACTI,GAAG,EAAED;IACP,CAAC;EACH,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}