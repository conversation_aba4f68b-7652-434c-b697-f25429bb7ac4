{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_localizedFormat = t();\n}(this, function () {\n  \"use strict\";\n\n  var e = {\n    LTS: \"h:mm:ss A\",\n    LT: \"h:mm A\",\n    L: \"MM/DD/YYYY\",\n    LL: \"MMMM D, YYYY\",\n    LLL: \"MMMM D, YYYY h:mm A\",\n    LLLL: \"dddd, MMMM D, YYYY h:mm A\"\n  };\n  return function (t, o, n) {\n    var r = o.prototype,\n      i = r.format;\n    n.en.formats = e, r.format = function (t) {\n      void 0 === t && (t = \"YYYY-MM-DDTHH:mm:ssZ\");\n      var o = this.$locale().formats,\n        n = function (t, o) {\n          return t.replace(/(\\[[^\\]]+])|(LTS?|l{1,4}|L{1,4})/g, function (t, n, r) {\n            var i = r && r.toUpperCase();\n            return n || o[r] || e[r] || o[i].replace(/(\\[[^\\]]+])|(MMMM|MM|DD|dddd)/g, function (e, t, o) {\n              return t || o.slice(1);\n            });\n          });\n        }(t, void 0 === o ? {} : o);\n      return i.call(this, n);\n    };\n  };\n});", "map": {"version": 3, "names": ["e", "t", "exports", "module", "define", "amd", "globalThis", "self", "dayjs_plugin_localizedFormat", "LTS", "LT", "L", "LL", "LLL", "LLLL", "o", "n", "r", "prototype", "i", "format", "en", "formats", "$locale", "replace", "toUpperCase", "slice", "call"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/dayjs/plugin/localizedFormat.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_localizedFormat=t()}(this,(function(){\"use strict\";var e={LTS:\"h:mm:ss A\",LT:\"h:mm A\",L:\"MM/DD/YYYY\",LL:\"MMMM D, YYYY\",LLL:\"MMMM D, YYYY h:mm A\",LLLL:\"dddd, MMMM D, YYYY h:mm A\"};return function(t,o,n){var r=o.prototype,i=r.format;n.en.formats=e,r.format=function(t){void 0===t&&(t=\"YYYY-MM-DDTHH:mm:ssZ\");var o=this.$locale().formats,n=function(t,o){return t.replace(/(\\[[^\\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(t,n,r){var i=r&&r.toUpperCase();return n||o[r]||e[r]||o[i].replace(/(\\[[^\\]]+])|(MMMM|MM|DD|dddd)/g,(function(e,t,o){return t||o.slice(1)}))}))}(t,void 0===o?{}:o);return i.call(this,n)}}}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAACH,CAAC,CAAC,GAAC,CAACD,CAAC,GAAC,WAAW,IAAE,OAAOM,UAAU,GAACA,UAAU,GAACN,CAAC,IAAEO,IAAI,EAAEC,4BAA4B,GAACP,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC,IAAI,EAAE,YAAU;EAAC,YAAY;;EAAC,IAAID,CAAC,GAAC;IAACS,GAAG,EAAC,WAAW;IAACC,EAAE,EAAC,QAAQ;IAACC,CAAC,EAAC,YAAY;IAACC,EAAE,EAAC,cAAc;IAACC,GAAG,EAAC,qBAAqB;IAACC,IAAI,EAAC;EAA2B,CAAC;EAAC,OAAO,UAASb,CAAC,EAACc,CAAC,EAACC,CAAC,EAAC;IAAC,IAAIC,CAAC,GAACF,CAAC,CAACG,SAAS;MAACC,CAAC,GAACF,CAAC,CAACG,MAAM;IAACJ,CAAC,CAACK,EAAE,CAACC,OAAO,GAACtB,CAAC,EAACiB,CAAC,CAACG,MAAM,GAAC,UAASnB,CAAC,EAAC;MAAC,KAAK,CAAC,KAAGA,CAAC,KAAGA,CAAC,GAAC,sBAAsB,CAAC;MAAC,IAAIc,CAAC,GAAC,IAAI,CAACQ,OAAO,CAAC,CAAC,CAACD,OAAO;QAACN,CAAC,GAAC,UAASf,CAAC,EAACc,CAAC,EAAC;UAAC,OAAOd,CAAC,CAACuB,OAAO,CAAC,mCAAmC,EAAE,UAASvB,CAAC,EAACe,CAAC,EAACC,CAAC,EAAC;YAAC,IAAIE,CAAC,GAACF,CAAC,IAAEA,CAAC,CAACQ,WAAW,CAAC,CAAC;YAAC,OAAOT,CAAC,IAAED,CAAC,CAACE,CAAC,CAAC,IAAEjB,CAAC,CAACiB,CAAC,CAAC,IAAEF,CAAC,CAACI,CAAC,CAAC,CAACK,OAAO,CAAC,gCAAgC,EAAE,UAASxB,CAAC,EAACC,CAAC,EAACc,CAAC,EAAC;cAAC,OAAOd,CAAC,IAAEc,CAAC,CAACW,KAAK,CAAC,CAAC,CAAC;YAAA,CAAE,CAAC;UAAA,CAAE,CAAC;QAAA,CAAC,CAACzB,CAAC,EAAC,KAAK,CAAC,KAAGc,CAAC,GAAC,CAAC,CAAC,GAACA,CAAC,CAAC;MAAC,OAAOI,CAAC,CAACQ,IAAI,CAAC,IAAI,EAACX,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}