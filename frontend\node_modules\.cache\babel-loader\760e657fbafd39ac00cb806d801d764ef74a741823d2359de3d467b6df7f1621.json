{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useUtils } from \"../useUtils.js\";\nimport { changeSectionValueFormat, cleanDigitSectionValue, doesSectionFormatHaveLeadingZeros, getDateSectionConfigFromFormatToken, getDaysInWeekStr, getLetterEditingOptions, applyLocalizedDigits, removeLocalizedDigits, isStringNumber } from \"./useField.utils.js\";\n\n/**\n * The letter editing and the numeric editing each define a `CharacterEditingApplier`.\n * This function decides what the new section value should be and if the focus should switch to the next section.\n *\n * If it returns `null`, then the section value is not updated and the focus does not move.\n */\n\n/**\n * Function called by `applyQuery` which decides:\n * - what is the new section value ?\n * - should the query used to get this value be stored for the next key press ?\n *\n * If it returns `{ sectionValue: string; shouldGoToNextSection: boolean }`,\n * Then we store the query and update the section with the new value.\n *\n * If it returns `{ saveQuery: true` },\n * Then we store the query and don't update the section.\n *\n * If it returns `{ saveQuery: false },\n * Then we do nothing.\n */\n\nconst QUERY_LIFE_DURATION_MS = 5000;\nconst isQueryResponseWithoutValue = response => response.saveQuery != null;\n\n/**\n * Update the active section value when the user pressed a key that is not a navigation key (arrow key for example).\n * This hook has two main editing behaviors\n *\n * 1. The numeric editing when the user presses a digit\n * 2. The letter editing when the user presses another key\n */\nexport const useFieldCharacterEditing = ({\n  sections,\n  updateSectionValue,\n  sectionsValueBoundaries,\n  localizedDigits,\n  setTempAndroidValueStr,\n  timezone\n}) => {\n  const utils = useUtils();\n  const [query, setQuery] = React.useState(null);\n  const resetQuery = useEventCallback(() => setQuery(null));\n  React.useEffect(() => {\n    if (query != null && sections[query.sectionIndex]?.type !== query.sectionType) {\n      resetQuery();\n    }\n  }, [sections, query, resetQuery]);\n  React.useEffect(() => {\n    if (query != null) {\n      const timeout = setTimeout(() => resetQuery(), QUERY_LIFE_DURATION_MS);\n      return () => {\n        clearTimeout(timeout);\n      };\n    }\n    return () => {};\n  }, [query, resetQuery]);\n  const applyQuery = ({\n    keyPressed,\n    sectionIndex\n  }, getFirstSectionValueMatchingWithQuery, isValidQueryValue) => {\n    const cleanKeyPressed = keyPressed.toLowerCase();\n    const activeSection = sections[sectionIndex];\n\n    // The current query targets the section being editing\n    // We can try to concatenate the value\n    if (query != null && (!isValidQueryValue || isValidQueryValue(query.value)) && query.sectionIndex === sectionIndex) {\n      const concatenatedQueryValue = `${query.value}${cleanKeyPressed}`;\n      const queryResponse = getFirstSectionValueMatchingWithQuery(concatenatedQueryValue, activeSection);\n      if (!isQueryResponseWithoutValue(queryResponse)) {\n        setQuery({\n          sectionIndex,\n          value: concatenatedQueryValue,\n          sectionType: activeSection.type\n        });\n        return queryResponse;\n      }\n    }\n    const queryResponse = getFirstSectionValueMatchingWithQuery(cleanKeyPressed, activeSection);\n    if (isQueryResponseWithoutValue(queryResponse) && !queryResponse.saveQuery) {\n      resetQuery();\n      return null;\n    }\n    setQuery({\n      sectionIndex,\n      value: cleanKeyPressed,\n      sectionType: activeSection.type\n    });\n    if (isQueryResponseWithoutValue(queryResponse)) {\n      return null;\n    }\n    return queryResponse;\n  };\n  const applyLetterEditing = params => {\n    const findMatchingOptions = (format, options, queryValue) => {\n      const matchingValues = options.filter(option => option.toLowerCase().startsWith(queryValue));\n      if (matchingValues.length === 0) {\n        return {\n          saveQuery: false\n        };\n      }\n      return {\n        sectionValue: matchingValues[0],\n        shouldGoToNextSection: matchingValues.length === 1\n      };\n    };\n    const testQueryOnFormatAndFallbackFormat = (queryValue, activeSection, fallbackFormat, formatFallbackValue) => {\n      const getOptions = format => getLetterEditingOptions(utils, timezone, activeSection.type, format);\n      if (activeSection.contentType === 'letter') {\n        return findMatchingOptions(activeSection.format, getOptions(activeSection.format), queryValue);\n      }\n\n      // When editing a digit-format month / weekDay and the user presses a letter,\n      // We can support the letter editing by using the letter-format month / weekDay and re-formatting the result.\n      // We just have to make sure that the default month / weekDay format is a letter format,\n      if (fallbackFormat && formatFallbackValue != null && getDateSectionConfigFromFormatToken(utils, fallbackFormat).contentType === 'letter') {\n        const fallbackOptions = getOptions(fallbackFormat);\n        const response = findMatchingOptions(fallbackFormat, fallbackOptions, queryValue);\n        if (isQueryResponseWithoutValue(response)) {\n          return {\n            saveQuery: false\n          };\n        }\n        return _extends({}, response, {\n          sectionValue: formatFallbackValue(response.sectionValue, fallbackOptions)\n        });\n      }\n      return {\n        saveQuery: false\n      };\n    };\n    const getFirstSectionValueMatchingWithQuery = (queryValue, activeSection) => {\n      switch (activeSection.type) {\n        case 'month':\n          {\n            const formatFallbackValue = fallbackValue => changeSectionValueFormat(utils, fallbackValue, utils.formats.month, activeSection.format);\n            return testQueryOnFormatAndFallbackFormat(queryValue, activeSection, utils.formats.month, formatFallbackValue);\n          }\n        case 'weekDay':\n          {\n            const formatFallbackValue = (fallbackValue, fallbackOptions) => fallbackOptions.indexOf(fallbackValue).toString();\n            return testQueryOnFormatAndFallbackFormat(queryValue, activeSection, utils.formats.weekday, formatFallbackValue);\n          }\n        case 'meridiem':\n          {\n            return testQueryOnFormatAndFallbackFormat(queryValue, activeSection);\n          }\n        default:\n          {\n            return {\n              saveQuery: false\n            };\n          }\n      }\n    };\n    return applyQuery(params, getFirstSectionValueMatchingWithQuery);\n  };\n  const applyNumericEditing = params => {\n    const getNewSectionValue = (queryValue, section) => {\n      const cleanQueryValue = removeLocalizedDigits(queryValue, localizedDigits);\n      const queryValueNumber = Number(cleanQueryValue);\n      const sectionBoundaries = sectionsValueBoundaries[section.type]({\n        currentDate: null,\n        format: section.format,\n        contentType: section.contentType\n      });\n      if (queryValueNumber > sectionBoundaries.maximum) {\n        return {\n          saveQuery: false\n        };\n      }\n\n      // If the user types `0` on a month section,\n      // It is below the minimum, but we want to store the `0` in the query,\n      // So that when he pressed `1`, it will store `01` and move to the next section.\n      if (queryValueNumber < sectionBoundaries.minimum) {\n        return {\n          saveQuery: true\n        };\n      }\n      const shouldGoToNextSection = queryValueNumber * 10 > sectionBoundaries.maximum || cleanQueryValue.length === sectionBoundaries.maximum.toString().length;\n      const newSectionValue = cleanDigitSectionValue(utils, queryValueNumber, sectionBoundaries, localizedDigits, section);\n      return {\n        sectionValue: newSectionValue,\n        shouldGoToNextSection\n      };\n    };\n    const getFirstSectionValueMatchingWithQuery = (queryValue, activeSection) => {\n      if (activeSection.contentType === 'digit' || activeSection.contentType === 'digit-with-letter') {\n        return getNewSectionValue(queryValue, activeSection);\n      }\n\n      // When editing a letter-format month and the user presses a digit,\n      // We can support the numeric editing by using the digit-format month and re-formatting the result.\n      if (activeSection.type === 'month') {\n        const hasLeadingZerosInFormat = doesSectionFormatHaveLeadingZeros(utils, timezone, 'digit', 'month', 'MM');\n        const response = getNewSectionValue(queryValue, {\n          type: activeSection.type,\n          format: 'MM',\n          hasLeadingZerosInFormat,\n          hasLeadingZerosInInput: true,\n          contentType: 'digit',\n          maxLength: 2\n        });\n        if (isQueryResponseWithoutValue(response)) {\n          return response;\n        }\n        const formattedValue = changeSectionValueFormat(utils, response.sectionValue, 'MM', activeSection.format);\n        return _extends({}, response, {\n          sectionValue: formattedValue\n        });\n      }\n\n      // When editing a letter-format weekDay and the user presses a digit,\n      // We can support the numeric editing by returning the nth day in the week day array.\n      if (activeSection.type === 'weekDay') {\n        const response = getNewSectionValue(queryValue, activeSection);\n        if (isQueryResponseWithoutValue(response)) {\n          return response;\n        }\n        const formattedValue = getDaysInWeekStr(utils, timezone, activeSection.format)[Number(response.sectionValue) - 1];\n        return _extends({}, response, {\n          sectionValue: formattedValue\n        });\n      }\n      return {\n        saveQuery: false\n      };\n    };\n    return applyQuery(params, getFirstSectionValueMatchingWithQuery, queryValue => isStringNumber(queryValue, localizedDigits));\n  };\n  const applyCharacterEditing = useEventCallback(params => {\n    const activeSection = sections[params.sectionIndex];\n    const isNumericEditing = isStringNumber(params.keyPressed, localizedDigits);\n    const response = isNumericEditing ? applyNumericEditing(_extends({}, params, {\n      keyPressed: applyLocalizedDigits(params.keyPressed, localizedDigits)\n    })) : applyLetterEditing(params);\n    if (response == null) {\n      setTempAndroidValueStr(null);\n      return;\n    }\n    updateSectionValue({\n      activeSection,\n      newSectionValue: response.sectionValue,\n      shouldGoToNextSection: response.shouldGoToNextSection\n    });\n  });\n  return {\n    applyCharacterEditing,\n    resetCharacterQuery: resetQuery\n  };\n};", "map": {"version": 3, "names": ["_extends", "React", "useEventCallback", "useUtils", "changeSectionValueFormat", "cleanDigitSectionValue", "doesSectionFormatHaveLeadingZeros", "getDateSectionConfigFromFormatToken", "getDaysInWeekStr", "getLetterEditingOptions", "applyLocalizedDigits", "removeLocalizedDigits", "isStringNumber", "QUERY_LIFE_DURATION_MS", "isQueryResponseWithoutValue", "response", "saveQuery", "useFieldCharacterEditing", "sections", "updateSectionValue", "sectionsValueBoundaries", "localizedDigits", "setTempAndroidValueStr", "timezone", "utils", "query", "<PERSON><PERSON><PERSON><PERSON>", "useState", "reset<PERSON><PERSON>y", "useEffect", "sectionIndex", "type", "sectionType", "timeout", "setTimeout", "clearTimeout", "<PERSON><PERSON><PERSON><PERSON>", "keyPressed", "getFirstSectionValueMatchingWithQuery", "isValidQuery<PERSON>ue", "cleanKeyPressed", "toLowerCase", "activeSection", "value", "concatenatedQueryValue", "queryResponse", "applyLetterEditing", "params", "findMatchingOptions", "format", "options", "queryValue", "matchingV<PERSON>ues", "filter", "option", "startsWith", "length", "sectionValue", "shouldGoToNextSection", "testQueryOnFormatAndFallbackFormat", "fallbackFormat", "formatFallbackValue", "getOptions", "contentType", "fallbackOptions", "fallback<PERSON><PERSON><PERSON>", "formats", "month", "indexOf", "toString", "weekday", "applyNumericEditing", "getNewSectionValue", "section", "cleanQueryValue", "queryValueNumber", "Number", "sectionBoundaries", "currentDate", "maximum", "minimum", "newSectionValue", "hasLeadingZerosInFormat", "hasLeadingZerosInInput", "max<PERSON><PERSON><PERSON>", "formattedValue", "applyCharacterEditing", "isNumericEditing", "resetCharacterQuery"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldCharacterEditing.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useUtils } from \"../useUtils.js\";\nimport { changeSectionValueFormat, cleanDigitSectionValue, doesSectionFormatHaveLeadingZeros, getDateSectionConfigFromFormatToken, getDaysInWeekStr, getLetterEditingOptions, applyLocalizedDigits, removeLocalizedDigits, isStringNumber } from \"./useField.utils.js\";\n\n/**\n * The letter editing and the numeric editing each define a `CharacterEditingApplier`.\n * This function decides what the new section value should be and if the focus should switch to the next section.\n *\n * If it returns `null`, then the section value is not updated and the focus does not move.\n */\n\n/**\n * Function called by `applyQuery` which decides:\n * - what is the new section value ?\n * - should the query used to get this value be stored for the next key press ?\n *\n * If it returns `{ sectionValue: string; shouldGoToNextSection: boolean }`,\n * Then we store the query and update the section with the new value.\n *\n * If it returns `{ saveQuery: true` },\n * Then we store the query and don't update the section.\n *\n * If it returns `{ saveQuery: false },\n * Then we do nothing.\n */\n\nconst QUERY_LIFE_DURATION_MS = 5000;\nconst isQueryResponseWithoutValue = response => response.saveQuery != null;\n\n/**\n * Update the active section value when the user pressed a key that is not a navigation key (arrow key for example).\n * This hook has two main editing behaviors\n *\n * 1. The numeric editing when the user presses a digit\n * 2. The letter editing when the user presses another key\n */\nexport const useFieldCharacterEditing = ({\n  sections,\n  updateSectionValue,\n  sectionsValueBoundaries,\n  localizedDigits,\n  setTempAndroidValueStr,\n  timezone\n}) => {\n  const utils = useUtils();\n  const [query, setQuery] = React.useState(null);\n  const resetQuery = useEventCallback(() => setQuery(null));\n  React.useEffect(() => {\n    if (query != null && sections[query.sectionIndex]?.type !== query.sectionType) {\n      resetQuery();\n    }\n  }, [sections, query, resetQuery]);\n  React.useEffect(() => {\n    if (query != null) {\n      const timeout = setTimeout(() => resetQuery(), QUERY_LIFE_DURATION_MS);\n      return () => {\n        clearTimeout(timeout);\n      };\n    }\n    return () => {};\n  }, [query, resetQuery]);\n  const applyQuery = ({\n    keyPressed,\n    sectionIndex\n  }, getFirstSectionValueMatchingWithQuery, isValidQueryValue) => {\n    const cleanKeyPressed = keyPressed.toLowerCase();\n    const activeSection = sections[sectionIndex];\n\n    // The current query targets the section being editing\n    // We can try to concatenate the value\n    if (query != null && (!isValidQueryValue || isValidQueryValue(query.value)) && query.sectionIndex === sectionIndex) {\n      const concatenatedQueryValue = `${query.value}${cleanKeyPressed}`;\n      const queryResponse = getFirstSectionValueMatchingWithQuery(concatenatedQueryValue, activeSection);\n      if (!isQueryResponseWithoutValue(queryResponse)) {\n        setQuery({\n          sectionIndex,\n          value: concatenatedQueryValue,\n          sectionType: activeSection.type\n        });\n        return queryResponse;\n      }\n    }\n    const queryResponse = getFirstSectionValueMatchingWithQuery(cleanKeyPressed, activeSection);\n    if (isQueryResponseWithoutValue(queryResponse) && !queryResponse.saveQuery) {\n      resetQuery();\n      return null;\n    }\n    setQuery({\n      sectionIndex,\n      value: cleanKeyPressed,\n      sectionType: activeSection.type\n    });\n    if (isQueryResponseWithoutValue(queryResponse)) {\n      return null;\n    }\n    return queryResponse;\n  };\n  const applyLetterEditing = params => {\n    const findMatchingOptions = (format, options, queryValue) => {\n      const matchingValues = options.filter(option => option.toLowerCase().startsWith(queryValue));\n      if (matchingValues.length === 0) {\n        return {\n          saveQuery: false\n        };\n      }\n      return {\n        sectionValue: matchingValues[0],\n        shouldGoToNextSection: matchingValues.length === 1\n      };\n    };\n    const testQueryOnFormatAndFallbackFormat = (queryValue, activeSection, fallbackFormat, formatFallbackValue) => {\n      const getOptions = format => getLetterEditingOptions(utils, timezone, activeSection.type, format);\n      if (activeSection.contentType === 'letter') {\n        return findMatchingOptions(activeSection.format, getOptions(activeSection.format), queryValue);\n      }\n\n      // When editing a digit-format month / weekDay and the user presses a letter,\n      // We can support the letter editing by using the letter-format month / weekDay and re-formatting the result.\n      // We just have to make sure that the default month / weekDay format is a letter format,\n      if (fallbackFormat && formatFallbackValue != null && getDateSectionConfigFromFormatToken(utils, fallbackFormat).contentType === 'letter') {\n        const fallbackOptions = getOptions(fallbackFormat);\n        const response = findMatchingOptions(fallbackFormat, fallbackOptions, queryValue);\n        if (isQueryResponseWithoutValue(response)) {\n          return {\n            saveQuery: false\n          };\n        }\n        return _extends({}, response, {\n          sectionValue: formatFallbackValue(response.sectionValue, fallbackOptions)\n        });\n      }\n      return {\n        saveQuery: false\n      };\n    };\n    const getFirstSectionValueMatchingWithQuery = (queryValue, activeSection) => {\n      switch (activeSection.type) {\n        case 'month':\n          {\n            const formatFallbackValue = fallbackValue => changeSectionValueFormat(utils, fallbackValue, utils.formats.month, activeSection.format);\n            return testQueryOnFormatAndFallbackFormat(queryValue, activeSection, utils.formats.month, formatFallbackValue);\n          }\n        case 'weekDay':\n          {\n            const formatFallbackValue = (fallbackValue, fallbackOptions) => fallbackOptions.indexOf(fallbackValue).toString();\n            return testQueryOnFormatAndFallbackFormat(queryValue, activeSection, utils.formats.weekday, formatFallbackValue);\n          }\n        case 'meridiem':\n          {\n            return testQueryOnFormatAndFallbackFormat(queryValue, activeSection);\n          }\n        default:\n          {\n            return {\n              saveQuery: false\n            };\n          }\n      }\n    };\n    return applyQuery(params, getFirstSectionValueMatchingWithQuery);\n  };\n  const applyNumericEditing = params => {\n    const getNewSectionValue = (queryValue, section) => {\n      const cleanQueryValue = removeLocalizedDigits(queryValue, localizedDigits);\n      const queryValueNumber = Number(cleanQueryValue);\n      const sectionBoundaries = sectionsValueBoundaries[section.type]({\n        currentDate: null,\n        format: section.format,\n        contentType: section.contentType\n      });\n      if (queryValueNumber > sectionBoundaries.maximum) {\n        return {\n          saveQuery: false\n        };\n      }\n\n      // If the user types `0` on a month section,\n      // It is below the minimum, but we want to store the `0` in the query,\n      // So that when he pressed `1`, it will store `01` and move to the next section.\n      if (queryValueNumber < sectionBoundaries.minimum) {\n        return {\n          saveQuery: true\n        };\n      }\n      const shouldGoToNextSection = queryValueNumber * 10 > sectionBoundaries.maximum || cleanQueryValue.length === sectionBoundaries.maximum.toString().length;\n      const newSectionValue = cleanDigitSectionValue(utils, queryValueNumber, sectionBoundaries, localizedDigits, section);\n      return {\n        sectionValue: newSectionValue,\n        shouldGoToNextSection\n      };\n    };\n    const getFirstSectionValueMatchingWithQuery = (queryValue, activeSection) => {\n      if (activeSection.contentType === 'digit' || activeSection.contentType === 'digit-with-letter') {\n        return getNewSectionValue(queryValue, activeSection);\n      }\n\n      // When editing a letter-format month and the user presses a digit,\n      // We can support the numeric editing by using the digit-format month and re-formatting the result.\n      if (activeSection.type === 'month') {\n        const hasLeadingZerosInFormat = doesSectionFormatHaveLeadingZeros(utils, timezone, 'digit', 'month', 'MM');\n        const response = getNewSectionValue(queryValue, {\n          type: activeSection.type,\n          format: 'MM',\n          hasLeadingZerosInFormat,\n          hasLeadingZerosInInput: true,\n          contentType: 'digit',\n          maxLength: 2\n        });\n        if (isQueryResponseWithoutValue(response)) {\n          return response;\n        }\n        const formattedValue = changeSectionValueFormat(utils, response.sectionValue, 'MM', activeSection.format);\n        return _extends({}, response, {\n          sectionValue: formattedValue\n        });\n      }\n\n      // When editing a letter-format weekDay and the user presses a digit,\n      // We can support the numeric editing by returning the nth day in the week day array.\n      if (activeSection.type === 'weekDay') {\n        const response = getNewSectionValue(queryValue, activeSection);\n        if (isQueryResponseWithoutValue(response)) {\n          return response;\n        }\n        const formattedValue = getDaysInWeekStr(utils, timezone, activeSection.format)[Number(response.sectionValue) - 1];\n        return _extends({}, response, {\n          sectionValue: formattedValue\n        });\n      }\n      return {\n        saveQuery: false\n      };\n    };\n    return applyQuery(params, getFirstSectionValueMatchingWithQuery, queryValue => isStringNumber(queryValue, localizedDigits));\n  };\n  const applyCharacterEditing = useEventCallback(params => {\n    const activeSection = sections[params.sectionIndex];\n    const isNumericEditing = isStringNumber(params.keyPressed, localizedDigits);\n    const response = isNumericEditing ? applyNumericEditing(_extends({}, params, {\n      keyPressed: applyLocalizedDigits(params.keyPressed, localizedDigits)\n    })) : applyLetterEditing(params);\n    if (response == null) {\n      setTempAndroidValueStr(null);\n      return;\n    }\n    updateSectionValue({\n      activeSection,\n      newSectionValue: response.sectionValue,\n      shouldGoToNextSection: response.shouldGoToNextSection\n    });\n  });\n  return {\n    applyCharacterEditing,\n    resetCharacterQuery: resetQuery\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,wBAAwB,EAAEC,sBAAsB,EAAEC,iCAAiC,EAAEC,mCAAmC,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,cAAc,QAAQ,qBAAqB;;AAEtQ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,sBAAsB,GAAG,IAAI;AACnC,MAAMC,2BAA2B,GAAGC,QAAQ,IAAIA,QAAQ,CAACC,SAAS,IAAI,IAAI;;AAE1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAGA,CAAC;EACvCC,QAAQ;EACRC,kBAAkB;EAClBC,uBAAuB;EACvBC,eAAe;EACfC,sBAAsB;EACtBC;AACF,CAAC,KAAK;EACJ,MAAMC,KAAK,GAAGrB,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,KAAK,CAAC0B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAMC,UAAU,GAAG1B,gBAAgB,CAAC,MAAMwB,QAAQ,CAAC,IAAI,CAAC,CAAC;EACzDzB,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpB,IAAIJ,KAAK,IAAI,IAAI,IAAIP,QAAQ,CAACO,KAAK,CAACK,YAAY,CAAC,EAAEC,IAAI,KAAKN,KAAK,CAACO,WAAW,EAAE;MAC7EJ,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACV,QAAQ,EAAEO,KAAK,EAAEG,UAAU,CAAC,CAAC;EACjC3B,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpB,IAAIJ,KAAK,IAAI,IAAI,EAAE;MACjB,MAAMQ,OAAO,GAAGC,UAAU,CAAC,MAAMN,UAAU,CAAC,CAAC,EAAEf,sBAAsB,CAAC;MACtE,OAAO,MAAM;QACXsB,YAAY,CAACF,OAAO,CAAC;MACvB,CAAC;IACH;IACA,OAAO,MAAM,CAAC,CAAC;EACjB,CAAC,EAAE,CAACR,KAAK,EAAEG,UAAU,CAAC,CAAC;EACvB,MAAMQ,UAAU,GAAGA,CAAC;IAClBC,UAAU;IACVP;EACF,CAAC,EAAEQ,qCAAqC,EAAEC,iBAAiB,KAAK;IAC9D,MAAMC,eAAe,GAAGH,UAAU,CAACI,WAAW,CAAC,CAAC;IAChD,MAAMC,aAAa,GAAGxB,QAAQ,CAACY,YAAY,CAAC;;IAE5C;IACA;IACA,IAAIL,KAAK,IAAI,IAAI,KAAK,CAACc,iBAAiB,IAAIA,iBAAiB,CAACd,KAAK,CAACkB,KAAK,CAAC,CAAC,IAAIlB,KAAK,CAACK,YAAY,KAAKA,YAAY,EAAE;MAClH,MAAMc,sBAAsB,GAAG,GAAGnB,KAAK,CAACkB,KAAK,GAAGH,eAAe,EAAE;MACjE,MAAMK,aAAa,GAAGP,qCAAqC,CAACM,sBAAsB,EAAEF,aAAa,CAAC;MAClG,IAAI,CAAC5B,2BAA2B,CAAC+B,aAAa,CAAC,EAAE;QAC/CnB,QAAQ,CAAC;UACPI,YAAY;UACZa,KAAK,EAAEC,sBAAsB;UAC7BZ,WAAW,EAAEU,aAAa,CAACX;QAC7B,CAAC,CAAC;QACF,OAAOc,aAAa;MACtB;IACF;IACA,MAAMA,aAAa,GAAGP,qCAAqC,CAACE,eAAe,EAAEE,aAAa,CAAC;IAC3F,IAAI5B,2BAA2B,CAAC+B,aAAa,CAAC,IAAI,CAACA,aAAa,CAAC7B,SAAS,EAAE;MAC1EY,UAAU,CAAC,CAAC;MACZ,OAAO,IAAI;IACb;IACAF,QAAQ,CAAC;MACPI,YAAY;MACZa,KAAK,EAAEH,eAAe;MACtBR,WAAW,EAAEU,aAAa,CAACX;IAC7B,CAAC,CAAC;IACF,IAAIjB,2BAA2B,CAAC+B,aAAa,CAAC,EAAE;MAC9C,OAAO,IAAI;IACb;IACA,OAAOA,aAAa;EACtB,CAAC;EACD,MAAMC,kBAAkB,GAAGC,MAAM,IAAI;IACnC,MAAMC,mBAAmB,GAAGA,CAACC,MAAM,EAAEC,OAAO,EAAEC,UAAU,KAAK;MAC3D,MAAMC,cAAc,GAAGF,OAAO,CAACG,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACb,WAAW,CAAC,CAAC,CAACc,UAAU,CAACJ,UAAU,CAAC,CAAC;MAC5F,IAAIC,cAAc,CAACI,MAAM,KAAK,CAAC,EAAE;QAC/B,OAAO;UACLxC,SAAS,EAAE;QACb,CAAC;MACH;MACA,OAAO;QACLyC,YAAY,EAAEL,cAAc,CAAC,CAAC,CAAC;QAC/BM,qBAAqB,EAAEN,cAAc,CAACI,MAAM,KAAK;MACnD,CAAC;IACH,CAAC;IACD,MAAMG,kCAAkC,GAAGA,CAACR,UAAU,EAAET,aAAa,EAAEkB,cAAc,EAAEC,mBAAmB,KAAK;MAC7G,MAAMC,UAAU,GAAGb,MAAM,IAAIxC,uBAAuB,CAACe,KAAK,EAAED,QAAQ,EAAEmB,aAAa,CAACX,IAAI,EAAEkB,MAAM,CAAC;MACjG,IAAIP,aAAa,CAACqB,WAAW,KAAK,QAAQ,EAAE;QAC1C,OAAOf,mBAAmB,CAACN,aAAa,CAACO,MAAM,EAAEa,UAAU,CAACpB,aAAa,CAACO,MAAM,CAAC,EAAEE,UAAU,CAAC;MAChG;;MAEA;MACA;MACA;MACA,IAAIS,cAAc,IAAIC,mBAAmB,IAAI,IAAI,IAAItD,mCAAmC,CAACiB,KAAK,EAAEoC,cAAc,CAAC,CAACG,WAAW,KAAK,QAAQ,EAAE;QACxI,MAAMC,eAAe,GAAGF,UAAU,CAACF,cAAc,CAAC;QAClD,MAAM7C,QAAQ,GAAGiC,mBAAmB,CAACY,cAAc,EAAEI,eAAe,EAAEb,UAAU,CAAC;QACjF,IAAIrC,2BAA2B,CAACC,QAAQ,CAAC,EAAE;UACzC,OAAO;YACLC,SAAS,EAAE;UACb,CAAC;QACH;QACA,OAAOhB,QAAQ,CAAC,CAAC,CAAC,EAAEe,QAAQ,EAAE;UAC5B0C,YAAY,EAAEI,mBAAmB,CAAC9C,QAAQ,CAAC0C,YAAY,EAAEO,eAAe;QAC1E,CAAC,CAAC;MACJ;MACA,OAAO;QACLhD,SAAS,EAAE;MACb,CAAC;IACH,CAAC;IACD,MAAMsB,qCAAqC,GAAGA,CAACa,UAAU,EAAET,aAAa,KAAK;MAC3E,QAAQA,aAAa,CAACX,IAAI;QACxB,KAAK,OAAO;UACV;YACE,MAAM8B,mBAAmB,GAAGI,aAAa,IAAI7D,wBAAwB,CAACoB,KAAK,EAAEyC,aAAa,EAAEzC,KAAK,CAAC0C,OAAO,CAACC,KAAK,EAAEzB,aAAa,CAACO,MAAM,CAAC;YACtI,OAAOU,kCAAkC,CAACR,UAAU,EAAET,aAAa,EAAElB,KAAK,CAAC0C,OAAO,CAACC,KAAK,EAAEN,mBAAmB,CAAC;UAChH;QACF,KAAK,SAAS;UACZ;YACE,MAAMA,mBAAmB,GAAGA,CAACI,aAAa,EAAED,eAAe,KAAKA,eAAe,CAACI,OAAO,CAACH,aAAa,CAAC,CAACI,QAAQ,CAAC,CAAC;YACjH,OAAOV,kCAAkC,CAACR,UAAU,EAAET,aAAa,EAAElB,KAAK,CAAC0C,OAAO,CAACI,OAAO,EAAET,mBAAmB,CAAC;UAClH;QACF,KAAK,UAAU;UACb;YACE,OAAOF,kCAAkC,CAACR,UAAU,EAAET,aAAa,CAAC;UACtE;QACF;UACE;YACE,OAAO;cACL1B,SAAS,EAAE;YACb,CAAC;UACH;MACJ;IACF,CAAC;IACD,OAAOoB,UAAU,CAACW,MAAM,EAAET,qCAAqC,CAAC;EAClE,CAAC;EACD,MAAMiC,mBAAmB,GAAGxB,MAAM,IAAI;IACpC,MAAMyB,kBAAkB,GAAGA,CAACrB,UAAU,EAAEsB,OAAO,KAAK;MAClD,MAAMC,eAAe,GAAG/D,qBAAqB,CAACwC,UAAU,EAAE9B,eAAe,CAAC;MAC1E,MAAMsD,gBAAgB,GAAGC,MAAM,CAACF,eAAe,CAAC;MAChD,MAAMG,iBAAiB,GAAGzD,uBAAuB,CAACqD,OAAO,CAAC1C,IAAI,CAAC,CAAC;QAC9D+C,WAAW,EAAE,IAAI;QACjB7B,MAAM,EAAEwB,OAAO,CAACxB,MAAM;QACtBc,WAAW,EAAEU,OAAO,CAACV;MACvB,CAAC,CAAC;MACF,IAAIY,gBAAgB,GAAGE,iBAAiB,CAACE,OAAO,EAAE;QAChD,OAAO;UACL/D,SAAS,EAAE;QACb,CAAC;MACH;;MAEA;MACA;MACA;MACA,IAAI2D,gBAAgB,GAAGE,iBAAiB,CAACG,OAAO,EAAE;QAChD,OAAO;UACLhE,SAAS,EAAE;QACb,CAAC;MACH;MACA,MAAM0C,qBAAqB,GAAGiB,gBAAgB,GAAG,EAAE,GAAGE,iBAAiB,CAACE,OAAO,IAAIL,eAAe,CAAClB,MAAM,KAAKqB,iBAAiB,CAACE,OAAO,CAACV,QAAQ,CAAC,CAAC,CAACb,MAAM;MACzJ,MAAMyB,eAAe,GAAG5E,sBAAsB,CAACmB,KAAK,EAAEmD,gBAAgB,EAAEE,iBAAiB,EAAExD,eAAe,EAAEoD,OAAO,CAAC;MACpH,OAAO;QACLhB,YAAY,EAAEwB,eAAe;QAC7BvB;MACF,CAAC;IACH,CAAC;IACD,MAAMpB,qCAAqC,GAAGA,CAACa,UAAU,EAAET,aAAa,KAAK;MAC3E,IAAIA,aAAa,CAACqB,WAAW,KAAK,OAAO,IAAIrB,aAAa,CAACqB,WAAW,KAAK,mBAAmB,EAAE;QAC9F,OAAOS,kBAAkB,CAACrB,UAAU,EAAET,aAAa,CAAC;MACtD;;MAEA;MACA;MACA,IAAIA,aAAa,CAACX,IAAI,KAAK,OAAO,EAAE;QAClC,MAAMmD,uBAAuB,GAAG5E,iCAAiC,CAACkB,KAAK,EAAED,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;QAC1G,MAAMR,QAAQ,GAAGyD,kBAAkB,CAACrB,UAAU,EAAE;UAC9CpB,IAAI,EAAEW,aAAa,CAACX,IAAI;UACxBkB,MAAM,EAAE,IAAI;UACZiC,uBAAuB;UACvBC,sBAAsB,EAAE,IAAI;UAC5BpB,WAAW,EAAE,OAAO;UACpBqB,SAAS,EAAE;QACb,CAAC,CAAC;QACF,IAAItE,2BAA2B,CAACC,QAAQ,CAAC,EAAE;UACzC,OAAOA,QAAQ;QACjB;QACA,MAAMsE,cAAc,GAAGjF,wBAAwB,CAACoB,KAAK,EAAET,QAAQ,CAAC0C,YAAY,EAAE,IAAI,EAAEf,aAAa,CAACO,MAAM,CAAC;QACzG,OAAOjD,QAAQ,CAAC,CAAC,CAAC,EAAEe,QAAQ,EAAE;UAC5B0C,YAAY,EAAE4B;QAChB,CAAC,CAAC;MACJ;;MAEA;MACA;MACA,IAAI3C,aAAa,CAACX,IAAI,KAAK,SAAS,EAAE;QACpC,MAAMhB,QAAQ,GAAGyD,kBAAkB,CAACrB,UAAU,EAAET,aAAa,CAAC;QAC9D,IAAI5B,2BAA2B,CAACC,QAAQ,CAAC,EAAE;UACzC,OAAOA,QAAQ;QACjB;QACA,MAAMsE,cAAc,GAAG7E,gBAAgB,CAACgB,KAAK,EAAED,QAAQ,EAAEmB,aAAa,CAACO,MAAM,CAAC,CAAC2B,MAAM,CAAC7D,QAAQ,CAAC0C,YAAY,CAAC,GAAG,CAAC,CAAC;QACjH,OAAOzD,QAAQ,CAAC,CAAC,CAAC,EAAEe,QAAQ,EAAE;UAC5B0C,YAAY,EAAE4B;QAChB,CAAC,CAAC;MACJ;MACA,OAAO;QACLrE,SAAS,EAAE;MACb,CAAC;IACH,CAAC;IACD,OAAOoB,UAAU,CAACW,MAAM,EAAET,qCAAqC,EAAEa,UAAU,IAAIvC,cAAc,CAACuC,UAAU,EAAE9B,eAAe,CAAC,CAAC;EAC7H,CAAC;EACD,MAAMiE,qBAAqB,GAAGpF,gBAAgB,CAAC6C,MAAM,IAAI;IACvD,MAAML,aAAa,GAAGxB,QAAQ,CAAC6B,MAAM,CAACjB,YAAY,CAAC;IACnD,MAAMyD,gBAAgB,GAAG3E,cAAc,CAACmC,MAAM,CAACV,UAAU,EAAEhB,eAAe,CAAC;IAC3E,MAAMN,QAAQ,GAAGwE,gBAAgB,GAAGhB,mBAAmB,CAACvE,QAAQ,CAAC,CAAC,CAAC,EAAE+C,MAAM,EAAE;MAC3EV,UAAU,EAAE3B,oBAAoB,CAACqC,MAAM,CAACV,UAAU,EAAEhB,eAAe;IACrE,CAAC,CAAC,CAAC,GAAGyB,kBAAkB,CAACC,MAAM,CAAC;IAChC,IAAIhC,QAAQ,IAAI,IAAI,EAAE;MACpBO,sBAAsB,CAAC,IAAI,CAAC;MAC5B;IACF;IACAH,kBAAkB,CAAC;MACjBuB,aAAa;MACbuC,eAAe,EAAElE,QAAQ,CAAC0C,YAAY;MACtCC,qBAAqB,EAAE3C,QAAQ,CAAC2C;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO;IACL4B,qBAAqB;IACrBE,mBAAmB,EAAE5D;EACvB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}