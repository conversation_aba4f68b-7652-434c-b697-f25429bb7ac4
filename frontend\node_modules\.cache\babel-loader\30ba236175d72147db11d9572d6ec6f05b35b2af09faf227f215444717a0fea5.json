{"ast": null, "code": "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n  return node;\n}", "map": {"version": 3, "names": ["getWindow", "node", "window", "toString", "ownerDocument", "defaultView"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@popperjs/core/lib/dom-utils/getWindow.js"], "sourcesContent": ["export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}"], "mappings": "AAAA,eAAe,SAASA,SAASA,CAACC,IAAI,EAAE;EACtC,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB,OAAOC,MAAM;EACf;EAEA,IAAID,IAAI,CAACE,QAAQ,CAAC,CAAC,KAAK,iBAAiB,EAAE;IACzC,IAAIC,aAAa,GAAGH,IAAI,CAACG,aAAa;IACtC,OAAOA,aAAa,GAAGA,aAAa,CAACC,WAAW,IAAIH,MAAM,GAAGA,MAAM;EACrE;EAEA,OAAOD,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}