{"ast": null, "code": "export const CLOCK_WIDTH = 220;\nexport const CLOCK_HOUR_WIDTH = 36;\nconst clockCenter = {\n  x: CLOCK_WIDTH / 2,\n  y: CLOCK_WIDTH / 2\n};\nconst baseClockPoint = {\n  x: clockCenter.x,\n  y: 0\n};\nconst cx = baseClockPoint.x - clockCenter.x;\nconst cy = baseClockPoint.y - clockCenter.y;\nconst rad2deg = rad => rad * (180 / Math.PI);\nconst getAngleValue = (step, offsetX, offsetY) => {\n  const x = offsetX - clockCenter.x;\n  const y = offsetY - clockCenter.y;\n  const atan = Math.atan2(cx, cy) - Math.atan2(x, y);\n  let deg = rad2deg(atan);\n  deg = Math.round(deg / step) * step;\n  deg %= 360;\n  const value = Math.floor(deg / step) || 0;\n  const delta = x ** 2 + y ** 2;\n  const distance = Math.sqrt(delta);\n  return {\n    value,\n    distance\n  };\n};\nexport const getMinutes = (offsetX, offsetY, step = 1) => {\n  const angleStep = step * 6;\n  let {\n    value\n  } = getAngleValue(angleStep, offsetX, offsetY);\n  value = value * step % 60;\n  return value;\n};\nexport const getHours = (offsetX, offsetY, ampm) => {\n  const {\n    value,\n    distance\n  } = getAngleValue(30, offsetX, offsetY);\n  let hour = value || 12;\n  if (!ampm) {\n    if (distance < CLOCK_WIDTH / 2 - CLOCK_HOUR_WIDTH) {\n      hour += 12;\n      hour %= 24;\n    }\n  } else {\n    hour %= 12;\n  }\n  return hour;\n};", "map": {"version": 3, "names": ["CLOCK_WIDTH", "CLOCK_HOUR_WIDTH", "clockCenter", "x", "y", "baseClockPoint", "cx", "cy", "rad2deg", "rad", "Math", "PI", "getAngleValue", "step", "offsetX", "offsetY", "atan", "atan2", "deg", "round", "value", "floor", "delta", "distance", "sqrt", "getMinutes", "angleStep", "getHours", "ampm", "hour"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/TimeClock/shared.js"], "sourcesContent": ["export const CLOCK_WIDTH = 220;\nexport const CLOCK_HOUR_WIDTH = 36;\nconst clockCenter = {\n  x: CLOCK_WIDTH / 2,\n  y: CLOCK_WIDTH / 2\n};\nconst baseClockPoint = {\n  x: clockCenter.x,\n  y: 0\n};\nconst cx = baseClockPoint.x - clockCenter.x;\nconst cy = baseClockPoint.y - clockCenter.y;\nconst rad2deg = rad => rad * (180 / Math.PI);\nconst getAngleValue = (step, offsetX, offsetY) => {\n  const x = offsetX - clockCenter.x;\n  const y = offsetY - clockCenter.y;\n  const atan = Math.atan2(cx, cy) - Math.atan2(x, y);\n  let deg = rad2deg(atan);\n  deg = Math.round(deg / step) * step;\n  deg %= 360;\n  const value = Math.floor(deg / step) || 0;\n  const delta = x ** 2 + y ** 2;\n  const distance = Math.sqrt(delta);\n  return {\n    value,\n    distance\n  };\n};\nexport const getMinutes = (offsetX, offsetY, step = 1) => {\n  const angleStep = step * 6;\n  let {\n    value\n  } = getAngleValue(angleStep, offsetX, offsetY);\n  value = value * step % 60;\n  return value;\n};\nexport const getHours = (offsetX, offsetY, ampm) => {\n  const {\n    value,\n    distance\n  } = getAngleValue(30, offsetX, offsetY);\n  let hour = value || 12;\n  if (!ampm) {\n    if (distance < CLOCK_WIDTH / 2 - CLOCK_HOUR_WIDTH) {\n      hour += 12;\n      hour %= 24;\n    }\n  } else {\n    hour %= 12;\n  }\n  return hour;\n};"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG,GAAG;AAC9B,OAAO,MAAMC,gBAAgB,GAAG,EAAE;AAClC,MAAMC,WAAW,GAAG;EAClBC,CAAC,EAAEH,WAAW,GAAG,CAAC;EAClBI,CAAC,EAAEJ,WAAW,GAAG;AACnB,CAAC;AACD,MAAMK,cAAc,GAAG;EACrBF,CAAC,EAAED,WAAW,CAACC,CAAC;EAChBC,CAAC,EAAE;AACL,CAAC;AACD,MAAME,EAAE,GAAGD,cAAc,CAACF,CAAC,GAAGD,WAAW,CAACC,CAAC;AAC3C,MAAMI,EAAE,GAAGF,cAAc,CAACD,CAAC,GAAGF,WAAW,CAACE,CAAC;AAC3C,MAAMI,OAAO,GAAGC,GAAG,IAAIA,GAAG,IAAI,GAAG,GAAGC,IAAI,CAACC,EAAE,CAAC;AAC5C,MAAMC,aAAa,GAAGA,CAACC,IAAI,EAAEC,OAAO,EAAEC,OAAO,KAAK;EAChD,MAAMZ,CAAC,GAAGW,OAAO,GAAGZ,WAAW,CAACC,CAAC;EACjC,MAAMC,CAAC,GAAGW,OAAO,GAAGb,WAAW,CAACE,CAAC;EACjC,MAAMY,IAAI,GAAGN,IAAI,CAACO,KAAK,CAACX,EAAE,EAAEC,EAAE,CAAC,GAAGG,IAAI,CAACO,KAAK,CAACd,CAAC,EAAEC,CAAC,CAAC;EAClD,IAAIc,GAAG,GAAGV,OAAO,CAACQ,IAAI,CAAC;EACvBE,GAAG,GAAGR,IAAI,CAACS,KAAK,CAACD,GAAG,GAAGL,IAAI,CAAC,GAAGA,IAAI;EACnCK,GAAG,IAAI,GAAG;EACV,MAAME,KAAK,GAAGV,IAAI,CAACW,KAAK,CAACH,GAAG,GAAGL,IAAI,CAAC,IAAI,CAAC;EACzC,MAAMS,KAAK,GAAGnB,CAAC,IAAI,CAAC,GAAGC,CAAC,IAAI,CAAC;EAC7B,MAAMmB,QAAQ,GAAGb,IAAI,CAACc,IAAI,CAACF,KAAK,CAAC;EACjC,OAAO;IACLF,KAAK;IACLG;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAME,UAAU,GAAGA,CAACX,OAAO,EAAEC,OAAO,EAAEF,IAAI,GAAG,CAAC,KAAK;EACxD,MAAMa,SAAS,GAAGb,IAAI,GAAG,CAAC;EAC1B,IAAI;IACFO;EACF,CAAC,GAAGR,aAAa,CAACc,SAAS,EAAEZ,OAAO,EAAEC,OAAO,CAAC;EAC9CK,KAAK,GAAGA,KAAK,GAAGP,IAAI,GAAG,EAAE;EACzB,OAAOO,KAAK;AACd,CAAC;AACD,OAAO,MAAMO,QAAQ,GAAGA,CAACb,OAAO,EAAEC,OAAO,EAAEa,IAAI,KAAK;EAClD,MAAM;IACJR,KAAK;IACLG;EACF,CAAC,GAAGX,aAAa,CAAC,EAAE,EAAEE,OAAO,EAAEC,OAAO,CAAC;EACvC,IAAIc,IAAI,GAAGT,KAAK,IAAI,EAAE;EACtB,IAAI,CAACQ,IAAI,EAAE;IACT,IAAIL,QAAQ,GAAGvB,WAAW,GAAG,CAAC,GAAGC,gBAAgB,EAAE;MACjD4B,IAAI,IAAI,EAAE;MACVA,IAAI,IAAI,EAAE;IACZ;EACF,CAAC,MAAM;IACLA,IAAI,IAAI,EAAE;EACZ;EACA,OAAOA,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}