{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _commandRole = _interopRequireDefault(require(\"./abstract/commandRole\"));\nvar _compositeRole = _interopRequireDefault(require(\"./abstract/compositeRole\"));\nvar _inputRole = _interopRequireDefault(require(\"./abstract/inputRole\"));\nvar _landmarkRole = _interopRequireDefault(require(\"./abstract/landmarkRole\"));\nvar _rangeRole = _interopRequireDefault(require(\"./abstract/rangeRole\"));\nvar _roletypeRole = _interopRequireDefault(require(\"./abstract/roletypeRole\"));\nvar _sectionRole = _interopRequireDefault(require(\"./abstract/sectionRole\"));\nvar _sectionheadRole = _interopRequireDefault(require(\"./abstract/sectionheadRole\"));\nvar _selectRole = _interopRequireDefault(require(\"./abstract/selectRole\"));\nvar _structureRole = _interopRequireDefault(require(\"./abstract/structureRole\"));\nvar _widgetRole = _interopRequireDefault(require(\"./abstract/widgetRole\"));\nvar _windowRole = _interopRequireDefault(require(\"./abstract/windowRole\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar ariaAbstractRoles = [['command', _commandRole.default], ['composite', _compositeRole.default], ['input', _inputRole.default], ['landmark', _landmarkRole.default], ['range', _rangeRole.default], ['roletype', _roletypeRole.default], ['section', _sectionRole.default], ['sectionhead', _sectionheadRole.default], ['select', _selectRole.default], ['structure', _structureRole.default], ['widget', _widgetRole.default], ['window', _windowRole.default]];\nvar _default = ariaAbstractRoles;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_commandRole", "_interopRequireDefault", "require", "_compositeRole", "_inputRole", "_landmarkRole", "_rangeRole", "_roletypeRole", "_sectionRole", "_sectionheadRole", "_selectRole", "_structureRole", "_widgetRole", "_windowRole", "obj", "__esModule", "ariaAbstractRoles", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/etc/roles/ariaAbstractRoles.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _commandRole = _interopRequireDefault(require(\"./abstract/commandRole\"));\nvar _compositeRole = _interopRequireDefault(require(\"./abstract/compositeRole\"));\nvar _inputRole = _interopRequireDefault(require(\"./abstract/inputRole\"));\nvar _landmarkRole = _interopRequireDefault(require(\"./abstract/landmarkRole\"));\nvar _rangeRole = _interopRequireDefault(require(\"./abstract/rangeRole\"));\nvar _roletypeRole = _interopRequireDefault(require(\"./abstract/roletypeRole\"));\nvar _sectionRole = _interopRequireDefault(require(\"./abstract/sectionRole\"));\nvar _sectionheadRole = _interopRequireDefault(require(\"./abstract/sectionheadRole\"));\nvar _selectRole = _interopRequireDefault(require(\"./abstract/selectRole\"));\nvar _structureRole = _interopRequireDefault(require(\"./abstract/structureRole\"));\nvar _widgetRole = _interopRequireDefault(require(\"./abstract/widgetRole\"));\nvar _windowRole = _interopRequireDefault(require(\"./abstract/windowRole\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar ariaAbstractRoles = [['command', _commandRole.default], ['composite', _compositeRole.default], ['input', _inputRole.default], ['landmark', _landmarkRole.default], ['range', _rangeRole.default], ['roletype', _roletypeRole.default], ['section', _sectionRole.default], ['sectionhead', _sectionheadRole.default], ['select', _selectRole.default], ['structure', _structureRole.default], ['widget', _widgetRole.default], ['window', _windowRole.default]];\nvar _default = ariaAbstractRoles;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,YAAY,GAAGC,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAC5E,IAAIC,cAAc,GAAGF,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAChF,IAAIE,UAAU,GAAGH,sBAAsB,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAAC;AACxE,IAAIG,aAAa,GAAGJ,sBAAsB,CAACC,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAC9E,IAAII,UAAU,GAAGL,sBAAsB,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAAC;AACxE,IAAIK,aAAa,GAAGN,sBAAsB,CAACC,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAC9E,IAAIM,YAAY,GAAGP,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAC5E,IAAIO,gBAAgB,GAAGR,sBAAsB,CAACC,OAAO,CAAC,4BAA4B,CAAC,CAAC;AACpF,IAAIQ,WAAW,GAAGT,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC1E,IAAIS,cAAc,GAAGV,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAChF,IAAIU,WAAW,GAAGX,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC1E,IAAIW,WAAW,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC1E,SAASD,sBAAsBA,CAACa,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEf,OAAO,EAAEe;EAAI,CAAC;AAAE;AAE9F,IAAIE,iBAAiB,GAAG,CAAC,CAAC,SAAS,EAAEhB,YAAY,CAACD,OAAO,CAAC,EAAE,CAAC,WAAW,EAAEI,cAAc,CAACJ,OAAO,CAAC,EAAE,CAAC,OAAO,EAAEK,UAAU,CAACL,OAAO,CAAC,EAAE,CAAC,UAAU,EAAEM,aAAa,CAACN,OAAO,CAAC,EAAE,CAAC,OAAO,EAAEO,UAAU,CAACP,OAAO,CAAC,EAAE,CAAC,UAAU,EAAEQ,aAAa,CAACR,OAAO,CAAC,EAAE,CAAC,SAAS,EAAES,YAAY,CAACT,OAAO,CAAC,EAAE,CAAC,aAAa,EAAEU,gBAAgB,CAACV,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAEW,WAAW,CAACX,OAAO,CAAC,EAAE,CAAC,WAAW,EAAEY,cAAc,CAACZ,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAEa,WAAW,CAACb,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAEc,WAAW,CAACd,OAAO,CAAC,CAAC;AAClc,IAAIkB,QAAQ,GAAGD,iBAAiB;AAChCnB,OAAO,CAACE,OAAO,GAAGkB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}