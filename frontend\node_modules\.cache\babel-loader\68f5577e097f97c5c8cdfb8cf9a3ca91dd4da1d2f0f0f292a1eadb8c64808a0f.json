{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\IdeaProjects\\\\FoodOrdering-main\\\\FoodOrdering-main\\\\frontend\\\\src\\\\Components\\\\Restaurant\\\\RestaurantCard.jsx\",\n  _s = $RefreshSig$();\nimport { Card, Chip, IconButton } from \"@mui/material\";\nimport React, { useState } from \"react\";\nimport FavoriteBorderIcon from \"@mui/icons-material/FavoriteBorder\";\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { addToFavorite } from \"../State/Auth/Action\";\nimport { isPresentInFavorites } from \"../Config/config\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const RestaurantCard = ({\n  item\n}) => {\n  _s();\n  const [isHovered, setIsHovered] = useState(false);\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const jwt = localStorage.getItem(\"token\");\n  const {\n    auth\n  } = useSelector(store => store);\n  const handleAddToFavorite = () => {\n    dispatch(addToFavorite({\n      token: jwt,\n      restaurantId: item.id\n    }));\n  };\n  const handleNavigateToRestaurant = () => {\n    console.log(item.address.city, item.name, item.id);\n    //Eğer restaurant açıksa adrese gidiyor kapalı ise gitmez\n    if (item.open || !item.open) {\n      navigate(`/restaurant/${item.address.city}/${item.name}/${item.id}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"w-[18rem] transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 cursor-pointer group\",\n    onMouseEnter: () => setIsHovered(true),\n    onMouseLeave: () => setIsHovered(false),\n    onClick: handleNavigateToRestaurant,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative overflow-hidden\",\n      children: [!imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full h-[10rem] bg-gray-200 animate-pulse rounded-t-md\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        className: `w-full h-[10rem] rounded-t-md object-cover transition-transform duration-500 ${isHovered ? 'scale-110' : 'scale-100'} ${imageLoaded ? 'block' : 'hidden'}`,\n        src: item.images[0],\n        alt: item.name,\n        onLoad: () => setImageLoaded(true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute inset-0 bg-black bg-opacity-20 transition-opacity duration-300 ${isHovered ? 'opacity-100' : 'opacity-0'}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        size: \"small\",\n        className: `absolute top-3 left-3 transition-all duration-300 ${isHovered ? 'scale-110' : 'scale-100'}`,\n        color: item.open ? \"success\" : \"error\",\n        label: item.open ? \"Open\" : \"Closed\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), item.foodType && /*#__PURE__*/_jsxDEV(Chip, {\n        size: \"small\",\n        className: \"absolute top-3 right-3 bg-white bg-opacity-90\",\n        label: item.foodType\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 textPart lg:flex w-full justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2 flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-bold text-lg text-gray-800 group-hover:text-orange-600 transition-colors duration-300\",\n          children: item.name || item.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 text-sm line-clamp-2 leading-relaxed\",\n          children: item.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), item.address && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-xs\",\n          children: [\"\\uD83D\\uDCCD \", item.address.city, \", \", item.address.country]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: e => {\n            e.stopPropagation();\n            handleAddToFavorite();\n          },\n          className: `transition-all duration-300 ${isHovered ? 'scale-110' : 'scale-100'}`,\n          children: isPresentInFavorites(auth.favorites, item) ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {\n            className: \"text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {\n            className: \"text-gray-400 hover:text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(RestaurantCard, \"eZIa9bGXNTryNV9W0VP8HiGGxb8=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = RestaurantCard;\nexport default RestaurantCard;\nvar _c;\n$RefreshReg$(_c, \"RestaurantCard\");", "map": {"version": 3, "names": ["Card", "Chip", "IconButton", "React", "useState", "FavoriteBorderIcon", "FavoriteIcon", "useNavigate", "useDispatch", "useSelector", "addToFavorite", "isPresentInFavorites", "jsxDEV", "_jsxDEV", "RestaurantCard", "item", "_s", "isHovered", "setIsHovered", "imageLoaded", "setImageLoaded", "navigate", "dispatch", "jwt", "localStorage", "getItem", "auth", "store", "handleAddToFavorite", "token", "restaurantId", "id", "handleNavigateToRestaurant", "console", "log", "address", "city", "name", "open", "className", "onMouseEnter", "onMouseLeave", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "images", "alt", "onLoad", "size", "color", "label", "foodType", "title", "description", "country", "e", "stopPropagation", "favorites", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/src/Components/Restaurant/RestaurantCard.jsx"], "sourcesContent": ["import { Card, Chip, IconButton } from \"@mui/material\";\nimport React, { useState } from \"react\";\nimport FavoriteBorderIcon from \"@mui/icons-material/FavoriteBorder\";\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { addToFavorite } from \"../State/Auth/Action\";\nimport { isPresentInFavorites } from \"../Config/config\";\nexport const RestaurantCard = ({ item }) => {\n    const [isHovered, setIsHovered] = useState(false);\n    const [imageLoaded, setImageLoaded] = useState(false);\n\n    const navigate = useNavigate()\n    const dispatch = useDispatch()\n\n    const jwt = localStorage.getItem(\"token\")\n\n    const {auth} = useSelector(store=>store)\n\n    const handleAddToFavorite =()=> {\n        dispatch(addToFavorite({token: jwt, restaurantId: item.id}));\n\n    }\n\n    const handleNavigateToRestaurant = () => {\n        console.log(item.address.city, item.name, item.id);\n        //Eğer restaurant açıksa adrese gidiyor kapalı ise gitmez\n        if (item.open || !item.open) {\n          navigate(`/restaurant/${item.address.city}/${item.name}/${item.id}`);\n        }\n      };\n      \n  return (\n    <Card\n      className=\"w-[18rem] transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 cursor-pointer group\"\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n      onClick={handleNavigateToRestaurant}\n    >\n      <div className=\"relative overflow-hidden\">\n        {!imageLoaded && (\n          <div className=\"w-full h-[10rem] bg-gray-200 animate-pulse rounded-t-md\"></div>\n        )}\n        <img\n          className={`w-full h-[10rem] rounded-t-md object-cover transition-transform duration-500 ${\n            isHovered ? 'scale-110' : 'scale-100'\n          } ${imageLoaded ? 'block' : 'hidden'}`}\n          src={item.images[0]}\n          alt={item.name}\n          onLoad={() => setImageLoaded(true)}\n        />\n\n        <div className={`absolute inset-0 bg-black bg-opacity-20 transition-opacity duration-300 ${\n          isHovered ? 'opacity-100' : 'opacity-0'\n        }`}></div>\n\n        <Chip\n          size=\"small\"\n          className={`absolute top-3 left-3 transition-all duration-300 ${\n            isHovered ? 'scale-110' : 'scale-100'\n          }`}\n          color={item.open ? \"success\" : \"error\"}\n          label={item.open ? \"Open\" : \"Closed\"}\n        />\n\n        {item.foodType && (\n          <Chip\n            size=\"small\"\n            className=\"absolute top-3 right-3 bg-white bg-opacity-90\"\n            label={item.foodType}\n          />\n        )}\n      </div>\n\n      <div className=\"p-4 textPart lg:flex w-full justify-between\">\n        <div className=\"space-y-2 flex-1\">\n          <h3 className=\"font-bold text-lg text-gray-800 group-hover:text-orange-600 transition-colors duration-300\">\n            {item.name || item.title}\n          </h3>\n          <p className=\"text-gray-500 text-sm line-clamp-2 leading-relaxed\">\n            {item.description}\n          </p>\n          {item.address && (\n            <p className=\"text-gray-400 text-xs\">\n              📍 {item.address.city}, {item.address.country}\n            </p>\n          )}\n        </div>\n\n        <div className=\"flex items-start\">\n          <IconButton\n            onClick={(e) => {\n              e.stopPropagation();\n              handleAddToFavorite();\n            }}\n            className={`transition-all duration-300 ${\n              isHovered ? 'scale-110' : 'scale-100'\n            }`}\n          >\n            {isPresentInFavorites(auth.favorites,item) ?\n              <FavoriteIcon className=\"text-red-500\" /> :\n              <FavoriteBorderIcon className=\"text-gray-400 hover:text-red-500\" />\n            }\n          </IconButton>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default RestaurantCard;\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,UAAU,QAAQ,eAAe;AACtD,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,oBAAoB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACxD,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMiB,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAMe,GAAG,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAEzC,MAAM;IAACC;EAAI,CAAC,GAAGjB,WAAW,CAACkB,KAAK,IAAEA,KAAK,CAAC;EAExC,MAAMC,mBAAmB,GAAEA,CAAA,KAAK;IAC5BN,QAAQ,CAACZ,aAAa,CAAC;MAACmB,KAAK,EAAEN,GAAG;MAAEO,YAAY,EAAEf,IAAI,CAACgB;IAAE,CAAC,CAAC,CAAC;EAEhE,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACrCC,OAAO,CAACC,GAAG,CAACnB,IAAI,CAACoB,OAAO,CAACC,IAAI,EAAErB,IAAI,CAACsB,IAAI,EAAEtB,IAAI,CAACgB,EAAE,CAAC;IAClD;IACA,IAAIhB,IAAI,CAACuB,IAAI,IAAI,CAACvB,IAAI,CAACuB,IAAI,EAAE;MAC3BjB,QAAQ,CAAC,eAAeN,IAAI,CAACoB,OAAO,CAACC,IAAI,IAAIrB,IAAI,CAACsB,IAAI,IAAItB,IAAI,CAACgB,EAAE,EAAE,CAAC;IACtE;EACF,CAAC;EAEL,oBACElB,OAAA,CAACb,IAAI;IACHuC,SAAS,EAAC,kGAAkG;IAC5GC,YAAY,EAAEA,CAAA,KAAMtB,YAAY,CAAC,IAAI,CAAE;IACvCuB,YAAY,EAAEA,CAAA,KAAMvB,YAAY,CAAC,KAAK,CAAE;IACxCwB,OAAO,EAAEV,0BAA2B;IAAAW,QAAA,gBAEpC9B,OAAA;MAAK0B,SAAS,EAAC,0BAA0B;MAAAI,QAAA,GACtC,CAACxB,WAAW,iBACXN,OAAA;QAAK0B,SAAS,EAAC;MAAyD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAC/E,eACDlC,OAAA;QACE0B,SAAS,EAAE,gFACTtB,SAAS,GAAG,WAAW,GAAG,WAAW,IACnCE,WAAW,GAAG,OAAO,GAAG,QAAQ,EAAG;QACvC6B,GAAG,EAAEjC,IAAI,CAACkC,MAAM,CAAC,CAAC,CAAE;QACpBC,GAAG,EAAEnC,IAAI,CAACsB,IAAK;QACfc,MAAM,EAAEA,CAAA,KAAM/B,cAAc,CAAC,IAAI;MAAE;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAEFlC,OAAA;QAAK0B,SAAS,EAAE,2EACdtB,SAAS,GAAG,aAAa,GAAG,WAAW;MACtC;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEVlC,OAAA,CAACZ,IAAI;QACHmD,IAAI,EAAC,OAAO;QACZb,SAAS,EAAE,qDACTtB,SAAS,GAAG,WAAW,GAAG,WAAW,EACpC;QACHoC,KAAK,EAAEtC,IAAI,CAACuB,IAAI,GAAG,SAAS,GAAG,OAAQ;QACvCgB,KAAK,EAAEvC,IAAI,CAACuB,IAAI,GAAG,MAAM,GAAG;MAAS;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,EAEDhC,IAAI,CAACwC,QAAQ,iBACZ1C,OAAA,CAACZ,IAAI;QACHmD,IAAI,EAAC,OAAO;QACZb,SAAS,EAAC,+CAA+C;QACzDe,KAAK,EAAEvC,IAAI,CAACwC;MAAS;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENlC,OAAA;MAAK0B,SAAS,EAAC,6CAA6C;MAAAI,QAAA,gBAC1D9B,OAAA;QAAK0B,SAAS,EAAC,kBAAkB;QAAAI,QAAA,gBAC/B9B,OAAA;UAAI0B,SAAS,EAAC,4FAA4F;UAAAI,QAAA,EACvG5B,IAAI,CAACsB,IAAI,IAAItB,IAAI,CAACyC;QAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACLlC,OAAA;UAAG0B,SAAS,EAAC,oDAAoD;UAAAI,QAAA,EAC9D5B,IAAI,CAAC0C;QAAW;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,EACHhC,IAAI,CAACoB,OAAO,iBACXtB,OAAA;UAAG0B,SAAS,EAAC,uBAAuB;UAAAI,QAAA,GAAC,eAChC,EAAC5B,IAAI,CAACoB,OAAO,CAACC,IAAI,EAAC,IAAE,EAACrB,IAAI,CAACoB,OAAO,CAACuB,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlC,OAAA;QAAK0B,SAAS,EAAC,kBAAkB;QAAAI,QAAA,eAC/B9B,OAAA,CAACX,UAAU;UACTwC,OAAO,EAAGiB,CAAC,IAAK;YACdA,CAAC,CAACC,eAAe,CAAC,CAAC;YACnBhC,mBAAmB,CAAC,CAAC;UACvB,CAAE;UACFW,SAAS,EAAE,+BACTtB,SAAS,GAAG,WAAW,GAAG,WAAW,EACpC;UAAA0B,QAAA,EAEFhC,oBAAoB,CAACe,IAAI,CAACmC,SAAS,EAAC9C,IAAI,CAAC,gBACxCF,OAAA,CAACP,YAAY;YAACiC,SAAS,EAAC;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBACzClC,OAAA,CAACR,kBAAkB;YAACkC,SAAS,EAAC;UAAkC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE3D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAAC/B,EAAA,CApGWF,cAAc;EAAA,QAINP,WAAW,EACXC,WAAW,EAIbC,WAAW;AAAA;AAAAqD,EAAA,GATjBhD,cAAc;AAsG3B,eAAeA,cAAc;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}