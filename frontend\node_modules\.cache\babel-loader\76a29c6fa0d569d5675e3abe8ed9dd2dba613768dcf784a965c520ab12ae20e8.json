{"ast": null, "code": "import { applyDefaultDate } from \"../internals/utils/date-utils.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nexport const validateDate = ({\n  props,\n  value,\n  timezone,\n  adapter\n}) => {\n  if (value === null) {\n    return null;\n  }\n  const {\n    shouldDisableDate,\n    shouldDisableMonth,\n    shouldDisableYear,\n    disablePast,\n    disableFuture\n  } = props;\n  const now = adapter.utils.date(undefined, timezone);\n  const minDate = applyDefaultDate(adapter.utils, props.minDate, adapter.defaultDates.minDate);\n  const maxDate = applyDefaultDate(adapter.utils, props.maxDate, adapter.defaultDates.maxDate);\n  switch (true) {\n    case !adapter.utils.isValid(value):\n      return 'invalidDate';\n    case Boolean(shouldDisableDate && shouldDisableDate(value)):\n      return 'shouldDisableDate';\n    case Boolean(shouldDisableMonth && shouldDisableMonth(value)):\n      return 'shouldDisableMonth';\n    case Boolean(shouldDisableYear && shouldDisableYear(value)):\n      return 'shouldDisableYear';\n    case Boolean(disableFuture && adapter.utils.isAfterDay(value, now)):\n      return 'disableFuture';\n    case Boolean(disablePast && adapter.utils.isBeforeDay(value, now)):\n      return 'disablePast';\n    case Boolean(minDate && adapter.utils.isBeforeDay(value, minDate)):\n      return 'minDate';\n    case Boolean(maxDate && adapter.utils.isAfterDay(value, maxDate)):\n      return 'maxDate';\n    default:\n      return null;\n  }\n};\nvalidateDate.valueManager = singleItemValueManager;", "map": {"version": 3, "names": ["applyDefaultDate", "singleItemValueManager", "validateDate", "props", "value", "timezone", "adapter", "shouldDisableDate", "shouldDisableMonth", "shouldDisableYear", "disablePast", "disableFuture", "now", "utils", "date", "undefined", "minDate", "defaultDates", "maxDate", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "isAfterDay", "isBeforeDay", "valueManager"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/validation/validateDate.js"], "sourcesContent": ["import { applyDefaultDate } from \"../internals/utils/date-utils.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nexport const validateDate = ({\n  props,\n  value,\n  timezone,\n  adapter\n}) => {\n  if (value === null) {\n    return null;\n  }\n  const {\n    shouldDisableDate,\n    shouldDisableMonth,\n    shouldDisableYear,\n    disablePast,\n    disableFuture\n  } = props;\n  const now = adapter.utils.date(undefined, timezone);\n  const minDate = applyDefaultDate(adapter.utils, props.minDate, adapter.defaultDates.minDate);\n  const maxDate = applyDefaultDate(adapter.utils, props.maxDate, adapter.defaultDates.maxDate);\n  switch (true) {\n    case !adapter.utils.isValid(value):\n      return 'invalidDate';\n    case Boolean(shouldDisableDate && shouldDisableDate(value)):\n      return 'shouldDisableDate';\n    case Boolean(shouldDisableMonth && shouldDisableMonth(value)):\n      return 'shouldDisableMonth';\n    case Boolean(shouldDisableYear && shouldDisableYear(value)):\n      return 'shouldDisableYear';\n    case Boolean(disableFuture && adapter.utils.isAfterDay(value, now)):\n      return 'disableFuture';\n    case Boolean(disablePast && adapter.utils.isBeforeDay(value, now)):\n      return 'disablePast';\n    case Boolean(minDate && adapter.utils.isBeforeDay(value, minDate)):\n      return 'minDate';\n    case Boolean(maxDate && adapter.utils.isAfterDay(value, maxDate)):\n      return 'maxDate';\n    default:\n      return null;\n  }\n};\nvalidateDate.valueManager = singleItemValueManager;"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,OAAO,MAAMC,YAAY,GAAGA,CAAC;EAC3BC,KAAK;EACLC,KAAK;EACLC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,IAAIF,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO,IAAI;EACb;EACA,MAAM;IACJG,iBAAiB;IACjBC,kBAAkB;IAClBC,iBAAiB;IACjBC,WAAW;IACXC;EACF,CAAC,GAAGR,KAAK;EACT,MAAMS,GAAG,GAAGN,OAAO,CAACO,KAAK,CAACC,IAAI,CAACC,SAAS,EAAEV,QAAQ,CAAC;EACnD,MAAMW,OAAO,GAAGhB,gBAAgB,CAACM,OAAO,CAACO,KAAK,EAAEV,KAAK,CAACa,OAAO,EAAEV,OAAO,CAACW,YAAY,CAACD,OAAO,CAAC;EAC5F,MAAME,OAAO,GAAGlB,gBAAgB,CAACM,OAAO,CAACO,KAAK,EAAEV,KAAK,CAACe,OAAO,EAAEZ,OAAO,CAACW,YAAY,CAACC,OAAO,CAAC;EAC5F,QAAQ,IAAI;IACV,KAAK,CAACZ,OAAO,CAACO,KAAK,CAACM,OAAO,CAACf,KAAK,CAAC;MAChC,OAAO,aAAa;IACtB,KAAKgB,OAAO,CAACb,iBAAiB,IAAIA,iBAAiB,CAACH,KAAK,CAAC,CAAC;MACzD,OAAO,mBAAmB;IAC5B,KAAKgB,OAAO,CAACZ,kBAAkB,IAAIA,kBAAkB,CAACJ,KAAK,CAAC,CAAC;MAC3D,OAAO,oBAAoB;IAC7B,KAAKgB,OAAO,CAACX,iBAAiB,IAAIA,iBAAiB,CAACL,KAAK,CAAC,CAAC;MACzD,OAAO,mBAAmB;IAC5B,KAAKgB,OAAO,CAACT,aAAa,IAAIL,OAAO,CAACO,KAAK,CAACQ,UAAU,CAACjB,KAAK,EAAEQ,GAAG,CAAC,CAAC;MACjE,OAAO,eAAe;IACxB,KAAKQ,OAAO,CAACV,WAAW,IAAIJ,OAAO,CAACO,KAAK,CAACS,WAAW,CAAClB,KAAK,EAAEQ,GAAG,CAAC,CAAC;MAChE,OAAO,aAAa;IACtB,KAAKQ,OAAO,CAACJ,OAAO,IAAIV,OAAO,CAACO,KAAK,CAACS,WAAW,CAAClB,KAAK,EAAEY,OAAO,CAAC,CAAC;MAChE,OAAO,SAAS;IAClB,KAAKI,OAAO,CAACF,OAAO,IAAIZ,OAAO,CAACO,KAAK,CAACQ,UAAU,CAACjB,KAAK,EAAEc,OAAO,CAAC,CAAC;MAC/D,OAAO,SAAS;IAClB;MACE,OAAO,IAAI;EACf;AACF,CAAC;AACDhB,YAAY,CAACqB,YAAY,GAAGtB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}