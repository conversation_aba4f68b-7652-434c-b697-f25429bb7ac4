{"ast": null, "code": "import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport const getDayCalendarSkeletonUtilityClass = slot => generateUtilityClass('MuiDayCalendarSkeleton', slot);\nexport const dayCalendarSkeletonClasses = generateUtilityClasses('MuiDayCalendarSkeleton', ['root', 'week', 'daySkeleton']);", "map": {"version": 3, "names": ["unstable_generateUtilityClass", "generateUtilityClass", "unstable_generateUtilityClasses", "generateUtilityClasses", "getDayCalendarSkeletonUtilityClass", "slot", "dayCalendarSkeletonClasses"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/DayCalendarSkeleton/dayCalendarSkeletonClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport const getDayCalendarSkeletonUtilityClass = slot => generateUtilityClass('MuiDayCalendarSkeleton', slot);\nexport const dayCalendarSkeletonClasses = generateUtilityClasses('MuiDayCalendarSkeleton', ['root', 'week', 'daySkeleton']);"], "mappings": "AAAA,SAASA,6BAA6B,IAAIC,oBAAoB,EAAEC,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AAC7I,OAAO,MAAMC,kCAAkC,GAAGC,IAAI,IAAIJ,oBAAoB,CAAC,wBAAwB,EAAEI,IAAI,CAAC;AAC9G,OAAO,MAAMC,0BAA0B,GAAGH,sBAAsB,CAAC,wBAAwB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}