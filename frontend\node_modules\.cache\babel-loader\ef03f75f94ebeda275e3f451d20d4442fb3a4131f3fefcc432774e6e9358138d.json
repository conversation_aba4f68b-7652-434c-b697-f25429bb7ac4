{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar scrollbarRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-valuetext': null,\n    'aria-orientation': 'vertical',\n    'aria-valuemax': '100',\n    'aria-valuemin': '0'\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-controls': null,\n    'aria-valuenow': null\n  },\n  superClass: [['roletype', 'structure', 'range'], ['roletype', 'widget']]\n};\nvar _default = scrollbarRole;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "scrollbarRole", "abstract", "accessibleNameRequired", "baseConcepts", "childrenPresentational", "nameFrom", "prohibitedProps", "props", "relatedConcepts", "requireContextRole", "requiredContextRole", "requiredOwnedElements", "requiredProps", "superClass", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/etc/roles/literal/scrollbarRole.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar scrollbarRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-valuetext': null,\n    'aria-orientation': 'vertical',\n    'aria-valuemax': '100',\n    'aria-valuemin': '0'\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-controls': null,\n    'aria-valuenow': null\n  },\n  superClass: [['roletype', 'structure', 'range'], ['roletype', 'widget']]\n};\nvar _default = scrollbarRole;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,aAAa,GAAG;EAClBC,QAAQ,EAAE,KAAK;EACfC,sBAAsB,EAAE,KAAK;EAC7BC,YAAY,EAAE,EAAE;EAChBC,sBAAsB,EAAE,IAAI;EAC5BC,QAAQ,EAAE,CAAC,QAAQ,CAAC;EACpBC,eAAe,EAAE,EAAE;EACnBC,KAAK,EAAE;IACL,eAAe,EAAE,IAAI;IACrB,gBAAgB,EAAE,IAAI;IACtB,kBAAkB,EAAE,UAAU;IAC9B,eAAe,EAAE,KAAK;IACtB,eAAe,EAAE;EACnB,CAAC;EACDC,eAAe,EAAE,EAAE;EACnBC,kBAAkB,EAAE,EAAE;EACtBC,mBAAmB,EAAE,EAAE;EACvBC,qBAAqB,EAAE,EAAE;EACzBC,aAAa,EAAE;IACb,eAAe,EAAE,IAAI;IACrB,eAAe,EAAE;EACnB,CAAC;EACDC,UAAU,EAAE,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;AACzE,CAAC;AACD,IAAIC,QAAQ,GAAGd,aAAa;AAC5BH,OAAO,CAACE,OAAO,GAAGe,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}