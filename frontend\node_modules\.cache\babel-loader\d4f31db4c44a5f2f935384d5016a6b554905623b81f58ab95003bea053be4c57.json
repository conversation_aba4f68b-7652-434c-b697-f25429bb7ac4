{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar graphicsDocumentRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    module: 'GRAPHICS',\n    concept: {\n      name: 'graphics-object'\n    }\n  }, {\n    module: 'ARIA',\n    concept: {\n      name: 'img'\n    }\n  }, {\n    module: 'ARIA',\n    concept: {\n      name: 'article'\n    }\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'document']]\n};\nvar _default = graphicsDocumentRole;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "graphicsDocumentRole", "abstract", "accessibleNameRequired", "baseConcepts", "childrenPresentational", "nameFrom", "prohibitedProps", "props", "relatedConcepts", "module", "concept", "name", "requireContextRole", "requiredContextRole", "requiredOwnedElements", "requiredProps", "superClass", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/etc/roles/graphics/graphicsDocumentRole.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar graphicsDocumentRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    module: 'GRAPHICS',\n    concept: {\n      name: 'graphics-object'\n    }\n  }, {\n    module: 'ARIA',\n    concept: {\n      name: 'img'\n    }\n  }, {\n    module: 'ARIA',\n    concept: {\n      name: 'article'\n    }\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'document']]\n};\nvar _default = graphicsDocumentRole;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,KAAK;EACfC,sBAAsB,EAAE,IAAI;EAC5BC,YAAY,EAAE,EAAE;EAChBC,sBAAsB,EAAE,KAAK;EAC7BC,QAAQ,EAAE,CAAC,QAAQ,CAAC;EACpBC,eAAe,EAAE,EAAE;EACnBC,KAAK,EAAE;IACL,eAAe,EAAE,IAAI;IACrB,mBAAmB,EAAE,IAAI;IACzB,eAAe,EAAE,IAAI;IACrB,eAAe,EAAE,IAAI;IACrB,cAAc,EAAE;EAClB,CAAC;EACDC,eAAe,EAAE,CAAC;IAChBC,MAAM,EAAE,UAAU;IAClBC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR;EACF,CAAC,EAAE;IACDF,MAAM,EAAE,MAAM;IACdC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR;EACF,CAAC,EAAE;IACDF,MAAM,EAAE,MAAM;IACdC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EACFC,kBAAkB,EAAE,EAAE;EACtBC,mBAAmB,EAAE,EAAE;EACvBC,qBAAqB,EAAE,EAAE;EACzBC,aAAa,EAAE,CAAC,CAAC;EACjBC,UAAU,EAAE,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;AACpD,CAAC;AACD,IAAIC,QAAQ,GAAGjB,oBAAoB;AACnCH,OAAO,CAACE,OAAO,GAAGkB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}