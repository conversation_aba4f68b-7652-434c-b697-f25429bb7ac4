{"ast": null, "code": "!function (e, i) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = i() : \"function\" == typeof define && define.amd ? define(i) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isBetween = i();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, i, t) {\n    i.prototype.isBetween = function (e, i, s, f) {\n      var n = t(e),\n        o = t(i),\n        r = \"(\" === (f = f || \"()\")[0],\n        u = \")\" === f[1];\n      return (r ? this.isAfter(n, s) : !this.isBefore(n, s)) && (u ? this.isBefore(o, s) : !this.isAfter(o, s)) || (r ? this.isBefore(n, s) : !this.isAfter(n, s)) && (u ? this.isAfter(o, s) : !this.isBefore(o, s));\n    };\n  };\n});", "map": {"version": 3, "names": ["e", "i", "exports", "module", "define", "amd", "globalThis", "self", "dayjs_plugin_isBetween", "t", "prototype", "isBetween", "s", "f", "n", "o", "r", "u", "isAfter", "isBefore"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/dayjs/plugin/isBetween.js"], "sourcesContent": ["!function(e,i){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=i():\"function\"==typeof define&&define.amd?define(i):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isBetween=i()}(this,(function(){\"use strict\";return function(e,i,t){i.prototype.isBetween=function(e,i,s,f){var n=t(e),o=t(i),r=\"(\"===(f=f||\"()\")[0],u=\")\"===f[1];return(r?this.isAfter(n,s):!this.isBefore(n,s))&&(u?this.isBefore(o,s):!this.isAfter(o,s))||(r?this.isBefore(n,s):!this.isAfter(n,s))&&(u?this.isAfter(o,s):!this.isBefore(o,s))}}}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAACH,CAAC,CAAC,GAAC,CAACD,CAAC,GAAC,WAAW,IAAE,OAAOM,UAAU,GAACA,UAAU,GAACN,CAAC,IAAEO,IAAI,EAAEC,sBAAsB,GAACP,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC,IAAI,EAAE,YAAU;EAAC,YAAY;;EAAC,OAAO,UAASD,CAAC,EAACC,CAAC,EAACQ,CAAC,EAAC;IAACR,CAAC,CAACS,SAAS,CAACC,SAAS,GAAC,UAASX,CAAC,EAACC,CAAC,EAACW,CAAC,EAACC,CAAC,EAAC;MAAC,IAAIC,CAAC,GAACL,CAAC,CAACT,CAAC,CAAC;QAACe,CAAC,GAACN,CAAC,CAACR,CAAC,CAAC;QAACe,CAAC,GAAC,GAAG,KAAG,CAACH,CAAC,GAACA,CAAC,IAAE,IAAI,EAAE,CAAC,CAAC;QAACI,CAAC,GAAC,GAAG,KAAGJ,CAAC,CAAC,CAAC,CAAC;MAAC,OAAM,CAACG,CAAC,GAAC,IAAI,CAACE,OAAO,CAACJ,CAAC,EAACF,CAAC,CAAC,GAAC,CAAC,IAAI,CAACO,QAAQ,CAACL,CAAC,EAACF,CAAC,CAAC,MAAIK,CAAC,GAAC,IAAI,CAACE,QAAQ,CAACJ,CAAC,EAACH,CAAC,CAAC,GAAC,CAAC,IAAI,CAACM,OAAO,CAACH,CAAC,EAACH,CAAC,CAAC,CAAC,IAAE,CAACI,CAAC,GAAC,IAAI,CAACG,QAAQ,CAACL,CAAC,EAACF,CAAC,CAAC,GAAC,CAAC,IAAI,CAACM,OAAO,CAACJ,CAAC,EAACF,CAAC,CAAC,MAAIK,CAAC,GAAC,IAAI,CAACC,OAAO,CAACH,CAAC,EAACH,CAAC,CAAC,GAAC,CAAC,IAAI,CAACO,QAAQ,CAACJ,CAAC,EAACH,CAAC,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}