{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\IdeaProjects\\\\FoodOrdering-main\\\\FoodOrdering-main\\\\frontend\\\\src\\\\Components\\\\Home\\\\Home.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport \"./Home.css\";\nimport MultiItemCarousel from \"./MultiItemCarousel\";\nimport RestaurantCard from \"../Restaurant/RestaurantCard\";\nimport Auth from \"../Auth/Auth\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { getAllRestaurants } from \"../State/Restaurant/action\";\nimport { useNavigate } from \"react-router-dom\";\nimport { findCard } from \"../State/Card/action\";\nimport { restaurantSeedData } from \"../../Data/RestaurantSeedData\";\nimport { CircularProgress } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Home = () => {\n  _s();\n  const dispatch = useDispatch();\n  const jwt = localStorage.getItem(\"token\");\n  const navigate = useNavigate();\n  const {\n    restaurant\n  } = useSelector(store => store);\n  useEffect(() => {\n    dispatch(getAllRestaurants(jwt));\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pb-10\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"banner -z-50 relative flex flex-col justify-center items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-[50vw] z-10 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-2xl lg:text-6xl font-bold z-10 py-5\",\n          children: \"Zashopp Food\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"z-10 text-gray-300 text-xl lg:text-4xl\",\n          children: \"Delicious Food, Delivered Fresh to Your Door!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cover absolute top-0 left-0 right-0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"p-10 lg:py-10 lg:px-20\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-2xl font-semibold text-gray-400 py-3 pb-10\",\n        children: \"Top Meels\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MultiItemCarousel, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"px-5 lg:px-20 pt-10\",\n      children: /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-semibold text-gray-400 pb-8\",\n        children: \"Order From Best Restaurants \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap items-center justify-around gap-5\",\n      children: restaurant.restaurants.map(item => /*#__PURE__*/_jsxDEV(RestaurantCard, {\n        item: item\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 58\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 9\n  }, this);\n};\n_s(Home, \"Fmoefg67/jTjYUuD4aylZcnsgSw=\", false, function () {\n  return [useDispatch, useNavigate, useSelector];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "MultiItemCarousel", "RestaurantCard", "<PERSON><PERSON>", "useDispatch", "useSelector", "getAllRestaurants", "useNavigate", "findCard", "restaurantSeedData", "CircularProgress", "jsxDEV", "_jsxDEV", "Home", "_s", "dispatch", "jwt", "localStorage", "getItem", "navigate", "restaurant", "store", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "restaurants", "map", "item", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/src/Components/Home/Home.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\"\nimport \"./Home.css\"\nimport MultiItemCarousel from \"./MultiItemCarousel\"\nimport RestaurantCard from \"../Restaurant/RestaurantCard\"\nimport Auth from \"../Auth/Auth\"\nimport { useDispatch, useSelector } from \"react-redux\"\nimport { getAllRestaurants } from \"../State/Restaurant/action\"\nimport { useNavigate } from \"react-router-dom\"\nimport { findCard } from \"../State/Card/action\"\nimport { restaurantSeedData } from \"../../Data/RestaurantSeedData\"\nimport { CircularProgress } from \"@mui/material\"\nexport const Home = () => {\n    const dispatch = useDispatch()\n    const jwt = localStorage.getItem(\"token\")\n    const navigate = useNavigate()\n    const {restaurant} = useSelector(store=>store)\n\n    useEffect(()=> {\n        dispatch(getAllRestaurants(jwt))\n        \n    },[])\n\n\n    return(\n        <div className=\"pb-10\">\n            <section className=\"banner -z-50 relative flex flex-col justify-center items-center\">\n\n                <div className=\"w-[50vw] z-10 text-center\">\n\n                    <p className=\"text-2xl lg:text-6xl font-bold z-10 py-5\">Zashopp Food</p>\n                    <p className=\"z-10 text-gray-300 text-xl lg:text-4xl\">Delicious Food, Delivered Fresh to Your Door!</p>\n\n                </div>\n\n                <div className=\"cover absolute top-0 left-0 right-0\">\n\n                </div>\n\n                <div className=\"footer\">\n\n                </div>\n            </section>\n\n            <section className=\"p-10 lg:py-10 lg:px-20\">\n                <p className=\"text-2xl font-semibold text-gray-400 py-3 pb-10\">Top Meels</p>\n                <MultiItemCarousel/>\n            </section>\n\n\n            <section className=\"px-5 lg:px-20 pt-10\">\n                <h1 className=\"text-2xl font-semibold text-gray-400 pb-8\">Order From Best Restaurants </h1>\n            </section>\n\n            <div className=\"flex flex-wrap items-center justify-around gap-5\">\n                {\n                    restaurant.restaurants.map((item) => <RestaurantCard item={item}/>)\n                }\n            </div>\n\n           \n            \n        </div>\n    )\n}\n\nexport default Home"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,YAAY;AACnB,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,IAAI,MAAM,cAAc;AAC/B,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,gBAAgB,QAAQ,eAAe;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAChD,OAAO,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,GAAG,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACzC,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM;IAACa;EAAU,CAAC,GAAGf,WAAW,CAACgB,KAAK,IAAEA,KAAK,CAAC;EAE9CtB,SAAS,CAAC,MAAK;IACXgB,QAAQ,CAACT,iBAAiB,CAACU,GAAG,CAAC,CAAC;EAEpC,CAAC,EAAC,EAAE,CAAC;EAGL,oBACIJ,OAAA;IAAKU,SAAS,EAAC,OAAO;IAAAC,QAAA,gBAClBX,OAAA;MAASU,SAAS,EAAC,iEAAiE;MAAAC,QAAA,gBAEhFX,OAAA;QAAKU,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBAEtCX,OAAA;UAAGU,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxEf,OAAA;UAAGU,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEtG,CAAC,eAENf,OAAA;QAAKU,SAAS,EAAC;MAAqC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE/C,CAAC,eAENf,OAAA;QAAKU,SAAS,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAElB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEVf,OAAA;MAASU,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACvCX,OAAA;QAAGU,SAAS,EAAC,iDAAiD;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5Ef,OAAA,CAACX,iBAAiB;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAGVf,OAAA;MAASU,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eACpCX,OAAA;QAAIU,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAA4B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC,eAEVf,OAAA;MAAKU,SAAS,EAAC,kDAAkD;MAAAC,QAAA,EAEzDH,UAAU,CAACQ,WAAW,CAACC,GAAG,CAAEC,IAAI,iBAAKlB,OAAA,CAACV,cAAc;QAAC4B,IAAI,EAAEA;MAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEtE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAIL,CAAC;AAEd,CAAC;AAAAb,EAAA,CApDYD,IAAI;EAAA,QACIT,WAAW,EAEXG,WAAW,EACPF,WAAW;AAAA;AAAA0B,EAAA,GAJvBlB,IAAI;AAsDjB,eAAeA,IAAI;AAAA,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}