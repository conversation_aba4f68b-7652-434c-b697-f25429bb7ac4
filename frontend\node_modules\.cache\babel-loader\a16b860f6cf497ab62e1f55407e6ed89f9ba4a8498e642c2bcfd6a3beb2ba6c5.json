{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ampm\", \"ampmInClock\", \"autoFocus\", \"slots\", \"slotProps\", \"value\", \"defaultValue\", \"referenceDate\", \"disableIgnoringDatePartForTimeValidation\", \"maxTime\", \"minTime\", \"disableFuture\", \"disablePast\", \"minutesStep\", \"shouldDisableTime\", \"showViewSwitcher\", \"onChange\", \"view\", \"views\", \"openTo\", \"onViewChange\", \"focusedView\", \"onFocusedViewChange\", \"className\", \"disabled\", \"readOnly\", \"timezone\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses, unstable_useId as useId } from '@mui/utils';\nimport { usePickersTranslations } from \"../hooks/usePickersTranslations.js\";\nimport { useUtils, useNow } from \"../internals/hooks/useUtils.js\";\nimport { PickersArrowSwitcher } from \"../internals/components/PickersArrowSwitcher/index.js\";\nimport { convertValueToMeridiem, createIsAfterIgnoreDatePart } from \"../internals/utils/time-utils.js\";\nimport { useViews } from \"../internals/hooks/useViews.js\";\nimport { useMeridiemMode } from \"../internals/hooks/date-helpers-hooks.js\";\nimport { PickerViewRoot } from \"../internals/components/PickerViewRoot/index.js\";\nimport { getTimeClockUtilityClass } from \"./timeClockClasses.js\";\nimport { Clock } from \"./Clock.js\";\nimport { getHourNumbers, getMinutesNumbers } from \"./ClockNumbers.js\";\nimport { useControlledValueWithTimezone } from \"../internals/hooks/useValueWithTimezone.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { useClockReferenceDate } from \"../internals/hooks/useClockReferenceDate.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    arrowSwitcher: ['arrowSwitcher']\n  };\n  return composeClasses(slots, getTimeClockUtilityClass, classes);\n};\nconst TimeClockRoot = styled(PickerViewRoot, {\n  name: 'MuiTimeClock',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  display: 'flex',\n  flexDirection: 'column',\n  position: 'relative'\n});\nconst TimeClockArrowSwitcher = styled(PickersArrowSwitcher, {\n  name: 'MuiTimeClock',\n  slot: 'ArrowSwitcher',\n  overridesResolver: (props, styles) => styles.arrowSwitcher\n})({\n  position: 'absolute',\n  right: 12,\n  top: 15\n});\nconst TIME_CLOCK_DEFAULT_VIEWS = ['hours', 'minutes'];\n\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [TimeClock](https://mui.com/x/react-date-pickers/time-clock/)\n *\n * API:\n *\n * - [TimeClock API](https://mui.com/x/api/date-pickers/time-clock/)\n */\nexport const TimeClock = /*#__PURE__*/React.forwardRef(function TimeClock(inProps, ref) {\n  const utils = useUtils();\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimeClock'\n  });\n  const {\n      ampm = utils.is12HourCycleInCurrentLocale(),\n      ampmInClock = false,\n      autoFocus,\n      slots,\n      slotProps,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disableIgnoringDatePartForTimeValidation = false,\n      maxTime,\n      minTime,\n      disableFuture,\n      disablePast,\n      minutesStep = 1,\n      shouldDisableTime,\n      showViewSwitcher,\n      onChange,\n      view: inView,\n      views = TIME_CLOCK_DEFAULT_VIEWS,\n      openTo,\n      onViewChange,\n      focusedView,\n      onFocusedViewChange,\n      className,\n      disabled,\n      readOnly,\n      timezone: timezoneProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = useControlledValueWithTimezone({\n    name: 'TimeClock',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    onChange,\n    valueManager: singleItemValueManager\n  });\n  const valueOrReferenceDate = useClockReferenceDate({\n    value,\n    referenceDate: referenceDateProp,\n    utils,\n    props,\n    timezone\n  });\n  const translations = usePickersTranslations();\n  const now = useNow(timezone);\n  const {\n    view,\n    setView,\n    previousView,\n    nextView,\n    setValueAndGoToNextView\n  } = useViews({\n    view: inView,\n    views,\n    openTo,\n    onViewChange,\n    onChange: handleValueChange,\n    focusedView,\n    onFocusedViewChange\n  });\n  const {\n    meridiemMode,\n    handleMeridiemChange\n  } = useMeridiemMode(valueOrReferenceDate, ampm, setValueAndGoToNextView);\n  const isTimeDisabled = React.useCallback((rawValue, viewType) => {\n    const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, utils);\n    const shouldCheckPastEnd = viewType === 'hours' || viewType === 'minutes' && views.includes('seconds');\n    const containsValidTime = ({\n      start,\n      end\n    }) => {\n      if (minTime && isAfter(minTime, end)) {\n        return false;\n      }\n      if (maxTime && isAfter(start, maxTime)) {\n        return false;\n      }\n      if (disableFuture && isAfter(start, now)) {\n        return false;\n      }\n      if (disablePast && isAfter(now, shouldCheckPastEnd ? end : start)) {\n        return false;\n      }\n      return true;\n    };\n    const isValidValue = (timeValue, step = 1) => {\n      if (timeValue % step !== 0) {\n        return false;\n      }\n      if (shouldDisableTime) {\n        switch (viewType) {\n          case 'hours':\n            return !shouldDisableTime(utils.setHours(valueOrReferenceDate, timeValue), 'hours');\n          case 'minutes':\n            return !shouldDisableTime(utils.setMinutes(valueOrReferenceDate, timeValue), 'minutes');\n          case 'seconds':\n            return !shouldDisableTime(utils.setSeconds(valueOrReferenceDate, timeValue), 'seconds');\n          default:\n            return false;\n        }\n      }\n      return true;\n    };\n    switch (viewType) {\n      case 'hours':\n        {\n          const valueWithMeridiem = convertValueToMeridiem(rawValue, meridiemMode, ampm);\n          const dateWithNewHours = utils.setHours(valueOrReferenceDate, valueWithMeridiem);\n          const start = utils.setSeconds(utils.setMinutes(dateWithNewHours, 0), 0);\n          const end = utils.setSeconds(utils.setMinutes(dateWithNewHours, 59), 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(valueWithMeridiem);\n        }\n      case 'minutes':\n        {\n          const dateWithNewMinutes = utils.setMinutes(valueOrReferenceDate, rawValue);\n          const start = utils.setSeconds(dateWithNewMinutes, 0);\n          const end = utils.setSeconds(dateWithNewMinutes, 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue, minutesStep);\n        }\n      case 'seconds':\n        {\n          const dateWithNewSeconds = utils.setSeconds(valueOrReferenceDate, rawValue);\n          const start = dateWithNewSeconds;\n          const end = dateWithNewSeconds;\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue);\n        }\n      default:\n        throw new Error('not supported');\n    }\n  }, [ampm, valueOrReferenceDate, disableIgnoringDatePartForTimeValidation, maxTime, meridiemMode, minTime, minutesStep, shouldDisableTime, utils, disableFuture, disablePast, now, views]);\n  const selectedId = useId();\n  const viewProps = React.useMemo(() => {\n    switch (view) {\n      case 'hours':\n        {\n          const handleHoursChange = (hourValue, isFinish) => {\n            const valueWithMeridiem = convertValueToMeridiem(hourValue, meridiemMode, ampm);\n            setValueAndGoToNextView(utils.setHours(valueOrReferenceDate, valueWithMeridiem), isFinish, 'hours');\n          };\n          return {\n            onChange: handleHoursChange,\n            viewValue: utils.getHours(valueOrReferenceDate),\n            children: getHourNumbers({\n              value,\n              utils,\n              ampm,\n              onChange: handleHoursChange,\n              getClockNumberText: translations.hoursClockNumberText,\n              isDisabled: hourValue => disabled || isTimeDisabled(hourValue, 'hours'),\n              selectedId\n            })\n          };\n        }\n      case 'minutes':\n        {\n          const minutesValue = utils.getMinutes(valueOrReferenceDate);\n          const handleMinutesChange = (minuteValue, isFinish) => {\n            setValueAndGoToNextView(utils.setMinutes(valueOrReferenceDate, minuteValue), isFinish, 'minutes');\n          };\n          return {\n            viewValue: minutesValue,\n            onChange: handleMinutesChange,\n            children: getMinutesNumbers({\n              utils,\n              value: minutesValue,\n              onChange: handleMinutesChange,\n              getClockNumberText: translations.minutesClockNumberText,\n              isDisabled: minuteValue => disabled || isTimeDisabled(minuteValue, 'minutes'),\n              selectedId\n            })\n          };\n        }\n      case 'seconds':\n        {\n          const secondsValue = utils.getSeconds(valueOrReferenceDate);\n          const handleSecondsChange = (secondValue, isFinish) => {\n            setValueAndGoToNextView(utils.setSeconds(valueOrReferenceDate, secondValue), isFinish, 'seconds');\n          };\n          return {\n            viewValue: secondsValue,\n            onChange: handleSecondsChange,\n            children: getMinutesNumbers({\n              utils,\n              value: secondsValue,\n              onChange: handleSecondsChange,\n              getClockNumberText: translations.secondsClockNumberText,\n              isDisabled: secondValue => disabled || isTimeDisabled(secondValue, 'seconds'),\n              selectedId\n            })\n          };\n        }\n      default:\n        throw new Error('You must provide the type for ClockView');\n    }\n  }, [view, utils, value, ampm, translations.hoursClockNumberText, translations.minutesClockNumberText, translations.secondsClockNumberText, meridiemMode, setValueAndGoToNextView, valueOrReferenceDate, isTimeDisabled, selectedId, disabled]);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(TimeClockRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: ownerState\n  }, other, {\n    children: [/*#__PURE__*/_jsx(Clock, _extends({\n      autoFocus: autoFocus ?? !!focusedView,\n      ampmInClock: ampmInClock && views.includes('hours'),\n      value: value,\n      type: view,\n      ampm: ampm,\n      minutesStep: minutesStep,\n      isTimeDisabled: isTimeDisabled,\n      meridiemMode: meridiemMode,\n      handleMeridiemChange: handleMeridiemChange,\n      selectedId: selectedId,\n      disabled: disabled,\n      readOnly: readOnly\n    }, viewProps)), showViewSwitcher && /*#__PURE__*/_jsx(TimeClockArrowSwitcher, {\n      className: classes.arrowSwitcher,\n      slots: slots,\n      slotProps: slotProps,\n      onGoToPrevious: () => setView(previousView),\n      isPreviousDisabled: !previousView,\n      previousLabel: translations.openPreviousView,\n      onGoToNext: () => setView(nextView),\n      isNextDisabled: !nextView,\n      nextLabel: translations.openNextView,\n      ownerState: ownerState\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TimeClock.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * Display ampm controls under the clock (instead of in the toolbar).\n   * @default false\n   */\n  ampmInClock: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the picker views and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * Controlled focused view.\n   */\n  focusedView: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TView The view type. Will be one of date or time views.\n   * @param {TValue} value The new value.\n   * @param {PickerSelectionState | undefined} selectionState Indicates if the date selection is complete.\n   * @param {TView | undefined} selectedView Indicates the view in which the selection has been made.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired on focused view change.\n   * @template TView\n   * @param {TView} view The new view to focus or not.\n   * @param {boolean} hasFocus `true` if the view should be focused.\n   */\n  onFocusedViewChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * If `true`, the picker views and text field are read-only.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid time using the validation props, except callbacks such as `shouldDisableTime`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Disable specific time.\n   * @template TDate\n   * @param {TDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  showViewSwitcher: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * Available views.\n   * @default ['hours', 'minutes']\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'minutes', 'seconds']).isRequired)\n} : void 0;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "clsx", "PropTypes", "styled", "useThemeProps", "unstable_composeClasses", "composeClasses", "unstable_useId", "useId", "usePickersTranslations", "useUtils", "useNow", "PickersArrowSwitcher", "convertValueToMeridiem", "createIsAfterIgnoreDatePart", "useViews", "useMeridiemMode", "PickerViewRoot", "getTimeClockUtilityClass", "Clock", "getHourNumbers", "getMinutesNumbers", "useControlledValueWithTimezone", "singleItemValueManager", "useClockReferenceDate", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "slots", "root", "arrowSwitcher", "TimeClockRoot", "name", "slot", "overridesResolver", "props", "styles", "display", "flexDirection", "position", "TimeClockArrowSwitcher", "right", "top", "TIME_CLOCK_DEFAULT_VIEWS", "TimeClock", "forwardRef", "inProps", "ref", "utils", "ampm", "is12HourCycleInCurrentLocale", "ampmInClock", "autoFocus", "slotProps", "value", "valueProp", "defaultValue", "referenceDate", "referenceDateProp", "disableIgnoringDatePartForTimeValidation", "maxTime", "minTime", "disableFuture", "disablePast", "minutesStep", "shouldDisableTime", "showViewSwitcher", "onChange", "view", "inView", "views", "openTo", "onViewChange", "focused<PERSON>iew", "onFocusedViewChange", "className", "disabled", "readOnly", "timezone", "timezoneProp", "other", "handleValueChange", "valueManager", "valueOrReferenceDate", "translations", "now", "<PERSON><PERSON><PERSON><PERSON>", "previousView", "next<PERSON>iew", "setValueAndGoToNextView", "meridiemMode", "handleMeridiemChange", "isTimeDisabled", "useCallback", "rawValue", "viewType", "isAfter", "shouldCheckPastEnd", "includes", "containsValidTime", "start", "end", "isValidValue", "timeValue", "step", "setHours", "setMinutes", "setSeconds", "valueWithMeridiem", "dateWithNewHours", "dateWithNewMinutes", "dateWithNewSeconds", "Error", "selectedId", "viewProps", "useMemo", "handleHoursChange", "hourValue", "is<PERSON><PERSON><PERSON>", "viewValue", "getHours", "children", "getClockNumberText", "hoursClockNumberText", "isDisabled", "minutesValue", "getMinutes", "handleMinutesChange", "minuteValue", "minutesClockNumberText", "secondsValue", "getSeconds", "handleSecondsChange", "secondValue", "secondsClockNumberText", "type", "onGoToPrevious", "isPreviousDisabled", "previousLabel", "openPreviousView", "onGoToNext", "isNextDisabled", "next<PERSON><PERSON><PERSON>", "openNextView", "process", "env", "NODE_ENV", "propTypes", "bool", "object", "string", "oneOf", "number", "func", "sx", "oneOfType", "arrayOf", "isRequired"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/TimeClock/TimeClock.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ampm\", \"ampmInClock\", \"autoFocus\", \"slots\", \"slotProps\", \"value\", \"defaultValue\", \"referenceDate\", \"disableIgnoringDatePartForTimeValidation\", \"maxTime\", \"minTime\", \"disableFuture\", \"disablePast\", \"minutesStep\", \"shouldDisableTime\", \"showViewSwitcher\", \"onChange\", \"view\", \"views\", \"openTo\", \"onViewChange\", \"focusedView\", \"onFocusedViewChange\", \"className\", \"disabled\", \"readOnly\", \"timezone\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses, unstable_useId as useId } from '@mui/utils';\nimport { usePickersTranslations } from \"../hooks/usePickersTranslations.js\";\nimport { useUtils, useNow } from \"../internals/hooks/useUtils.js\";\nimport { PickersArrowSwitcher } from \"../internals/components/PickersArrowSwitcher/index.js\";\nimport { convertValueToMeridiem, createIsAfterIgnoreDatePart } from \"../internals/utils/time-utils.js\";\nimport { useViews } from \"../internals/hooks/useViews.js\";\nimport { useMeridiemMode } from \"../internals/hooks/date-helpers-hooks.js\";\nimport { PickerViewRoot } from \"../internals/components/PickerViewRoot/index.js\";\nimport { getTimeClockUtilityClass } from \"./timeClockClasses.js\";\nimport { Clock } from \"./Clock.js\";\nimport { getHourNumbers, getMinutesNumbers } from \"./ClockNumbers.js\";\nimport { useControlledValueWithTimezone } from \"../internals/hooks/useValueWithTimezone.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { useClockReferenceDate } from \"../internals/hooks/useClockReferenceDate.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    arrowSwitcher: ['arrowSwitcher']\n  };\n  return composeClasses(slots, getTimeClockUtilityClass, classes);\n};\nconst TimeClockRoot = styled(PickerViewRoot, {\n  name: 'MuiTimeClock',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  display: 'flex',\n  flexDirection: 'column',\n  position: 'relative'\n});\nconst TimeClockArrowSwitcher = styled(PickersArrowSwitcher, {\n  name: 'MuiTimeClock',\n  slot: 'ArrowSwitcher',\n  overridesResolver: (props, styles) => styles.arrowSwitcher\n})({\n  position: 'absolute',\n  right: 12,\n  top: 15\n});\nconst TIME_CLOCK_DEFAULT_VIEWS = ['hours', 'minutes'];\n\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [TimeClock](https://mui.com/x/react-date-pickers/time-clock/)\n *\n * API:\n *\n * - [TimeClock API](https://mui.com/x/api/date-pickers/time-clock/)\n */\nexport const TimeClock = /*#__PURE__*/React.forwardRef(function TimeClock(inProps, ref) {\n  const utils = useUtils();\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimeClock'\n  });\n  const {\n      ampm = utils.is12HourCycleInCurrentLocale(),\n      ampmInClock = false,\n      autoFocus,\n      slots,\n      slotProps,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disableIgnoringDatePartForTimeValidation = false,\n      maxTime,\n      minTime,\n      disableFuture,\n      disablePast,\n      minutesStep = 1,\n      shouldDisableTime,\n      showViewSwitcher,\n      onChange,\n      view: inView,\n      views = TIME_CLOCK_DEFAULT_VIEWS,\n      openTo,\n      onViewChange,\n      focusedView,\n      onFocusedViewChange,\n      className,\n      disabled,\n      readOnly,\n      timezone: timezoneProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = useControlledValueWithTimezone({\n    name: 'TimeClock',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    onChange,\n    valueManager: singleItemValueManager\n  });\n  const valueOrReferenceDate = useClockReferenceDate({\n    value,\n    referenceDate: referenceDateProp,\n    utils,\n    props,\n    timezone\n  });\n  const translations = usePickersTranslations();\n  const now = useNow(timezone);\n  const {\n    view,\n    setView,\n    previousView,\n    nextView,\n    setValueAndGoToNextView\n  } = useViews({\n    view: inView,\n    views,\n    openTo,\n    onViewChange,\n    onChange: handleValueChange,\n    focusedView,\n    onFocusedViewChange\n  });\n  const {\n    meridiemMode,\n    handleMeridiemChange\n  } = useMeridiemMode(valueOrReferenceDate, ampm, setValueAndGoToNextView);\n  const isTimeDisabled = React.useCallback((rawValue, viewType) => {\n    const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, utils);\n    const shouldCheckPastEnd = viewType === 'hours' || viewType === 'minutes' && views.includes('seconds');\n    const containsValidTime = ({\n      start,\n      end\n    }) => {\n      if (minTime && isAfter(minTime, end)) {\n        return false;\n      }\n      if (maxTime && isAfter(start, maxTime)) {\n        return false;\n      }\n      if (disableFuture && isAfter(start, now)) {\n        return false;\n      }\n      if (disablePast && isAfter(now, shouldCheckPastEnd ? end : start)) {\n        return false;\n      }\n      return true;\n    };\n    const isValidValue = (timeValue, step = 1) => {\n      if (timeValue % step !== 0) {\n        return false;\n      }\n      if (shouldDisableTime) {\n        switch (viewType) {\n          case 'hours':\n            return !shouldDisableTime(utils.setHours(valueOrReferenceDate, timeValue), 'hours');\n          case 'minutes':\n            return !shouldDisableTime(utils.setMinutes(valueOrReferenceDate, timeValue), 'minutes');\n          case 'seconds':\n            return !shouldDisableTime(utils.setSeconds(valueOrReferenceDate, timeValue), 'seconds');\n          default:\n            return false;\n        }\n      }\n      return true;\n    };\n    switch (viewType) {\n      case 'hours':\n        {\n          const valueWithMeridiem = convertValueToMeridiem(rawValue, meridiemMode, ampm);\n          const dateWithNewHours = utils.setHours(valueOrReferenceDate, valueWithMeridiem);\n          const start = utils.setSeconds(utils.setMinutes(dateWithNewHours, 0), 0);\n          const end = utils.setSeconds(utils.setMinutes(dateWithNewHours, 59), 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(valueWithMeridiem);\n        }\n      case 'minutes':\n        {\n          const dateWithNewMinutes = utils.setMinutes(valueOrReferenceDate, rawValue);\n          const start = utils.setSeconds(dateWithNewMinutes, 0);\n          const end = utils.setSeconds(dateWithNewMinutes, 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue, minutesStep);\n        }\n      case 'seconds':\n        {\n          const dateWithNewSeconds = utils.setSeconds(valueOrReferenceDate, rawValue);\n          const start = dateWithNewSeconds;\n          const end = dateWithNewSeconds;\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue);\n        }\n      default:\n        throw new Error('not supported');\n    }\n  }, [ampm, valueOrReferenceDate, disableIgnoringDatePartForTimeValidation, maxTime, meridiemMode, minTime, minutesStep, shouldDisableTime, utils, disableFuture, disablePast, now, views]);\n  const selectedId = useId();\n  const viewProps = React.useMemo(() => {\n    switch (view) {\n      case 'hours':\n        {\n          const handleHoursChange = (hourValue, isFinish) => {\n            const valueWithMeridiem = convertValueToMeridiem(hourValue, meridiemMode, ampm);\n            setValueAndGoToNextView(utils.setHours(valueOrReferenceDate, valueWithMeridiem), isFinish, 'hours');\n          };\n          return {\n            onChange: handleHoursChange,\n            viewValue: utils.getHours(valueOrReferenceDate),\n            children: getHourNumbers({\n              value,\n              utils,\n              ampm,\n              onChange: handleHoursChange,\n              getClockNumberText: translations.hoursClockNumberText,\n              isDisabled: hourValue => disabled || isTimeDisabled(hourValue, 'hours'),\n              selectedId\n            })\n          };\n        }\n      case 'minutes':\n        {\n          const minutesValue = utils.getMinutes(valueOrReferenceDate);\n          const handleMinutesChange = (minuteValue, isFinish) => {\n            setValueAndGoToNextView(utils.setMinutes(valueOrReferenceDate, minuteValue), isFinish, 'minutes');\n          };\n          return {\n            viewValue: minutesValue,\n            onChange: handleMinutesChange,\n            children: getMinutesNumbers({\n              utils,\n              value: minutesValue,\n              onChange: handleMinutesChange,\n              getClockNumberText: translations.minutesClockNumberText,\n              isDisabled: minuteValue => disabled || isTimeDisabled(minuteValue, 'minutes'),\n              selectedId\n            })\n          };\n        }\n      case 'seconds':\n        {\n          const secondsValue = utils.getSeconds(valueOrReferenceDate);\n          const handleSecondsChange = (secondValue, isFinish) => {\n            setValueAndGoToNextView(utils.setSeconds(valueOrReferenceDate, secondValue), isFinish, 'seconds');\n          };\n          return {\n            viewValue: secondsValue,\n            onChange: handleSecondsChange,\n            children: getMinutesNumbers({\n              utils,\n              value: secondsValue,\n              onChange: handleSecondsChange,\n              getClockNumberText: translations.secondsClockNumberText,\n              isDisabled: secondValue => disabled || isTimeDisabled(secondValue, 'seconds'),\n              selectedId\n            })\n          };\n        }\n      default:\n        throw new Error('You must provide the type for ClockView');\n    }\n  }, [view, utils, value, ampm, translations.hoursClockNumberText, translations.minutesClockNumberText, translations.secondsClockNumberText, meridiemMode, setValueAndGoToNextView, valueOrReferenceDate, isTimeDisabled, selectedId, disabled]);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(TimeClockRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: ownerState\n  }, other, {\n    children: [/*#__PURE__*/_jsx(Clock, _extends({\n      autoFocus: autoFocus ?? !!focusedView,\n      ampmInClock: ampmInClock && views.includes('hours'),\n      value: value,\n      type: view,\n      ampm: ampm,\n      minutesStep: minutesStep,\n      isTimeDisabled: isTimeDisabled,\n      meridiemMode: meridiemMode,\n      handleMeridiemChange: handleMeridiemChange,\n      selectedId: selectedId,\n      disabled: disabled,\n      readOnly: readOnly\n    }, viewProps)), showViewSwitcher && /*#__PURE__*/_jsx(TimeClockArrowSwitcher, {\n      className: classes.arrowSwitcher,\n      slots: slots,\n      slotProps: slotProps,\n      onGoToPrevious: () => setView(previousView),\n      isPreviousDisabled: !previousView,\n      previousLabel: translations.openPreviousView,\n      onGoToNext: () => setView(nextView),\n      isNextDisabled: !nextView,\n      nextLabel: translations.openNextView,\n      ownerState: ownerState\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TimeClock.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * Display ampm controls under the clock (instead of in the toolbar).\n   * @default false\n   */\n  ampmInClock: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the picker views and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * Controlled focused view.\n   */\n  focusedView: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TView The view type. Will be one of date or time views.\n   * @param {TValue} value The new value.\n   * @param {PickerSelectionState | undefined} selectionState Indicates if the date selection is complete.\n   * @param {TView | undefined} selectedView Indicates the view in which the selection has been made.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired on focused view change.\n   * @template TView\n   * @param {TView} view The new view to focus or not.\n   * @param {boolean} hasFocus `true` if the view should be focused.\n   */\n  onFocusedViewChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * If `true`, the picker views and text field are read-only.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid time using the validation props, except callbacks such as `shouldDisableTime`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Disable specific time.\n   * @template TDate\n   * @param {TDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  showViewSwitcher: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * Available views.\n   * @default ['hours', 'minutes']\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'minutes', 'seconds']).isRequired)\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,0CAA0C,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAE,qBAAqB,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;AAC9Z,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,uBAAuB,IAAIC,cAAc,EAAEC,cAAc,IAAIC,KAAK,QAAQ,YAAY;AAC/F,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,QAAQ,EAAEC,MAAM,QAAQ,gCAAgC;AACjE,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,sBAAsB,EAAEC,2BAA2B,QAAQ,kCAAkC;AACtG,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,eAAe,QAAQ,0CAA0C;AAC1E,SAASC,cAAc,QAAQ,iDAAiD;AAChF,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,mBAAmB;AACrE,SAASC,8BAA8B,QAAQ,4CAA4C;AAC3F,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,aAAa,EAAE,CAAC,eAAe;EACjC,CAAC;EACD,OAAO5B,cAAc,CAAC0B,KAAK,EAAEd,wBAAwB,EAAEa,OAAO,CAAC;AACjE,CAAC;AACD,MAAMI,aAAa,GAAGhC,MAAM,CAACc,cAAc,EAAE;EAC3CmB,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAAC;EACDQ,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,QAAQ;EACvBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,MAAMC,sBAAsB,GAAGzC,MAAM,CAACS,oBAAoB,EAAE;EAC1DwB,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,eAAe;EACrBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC;EACDS,QAAQ,EAAE,UAAU;EACpBE,KAAK,EAAE,EAAE;EACTC,GAAG,EAAE;AACP,CAAC,CAAC;AACF,MAAMC,wBAAwB,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,GAAG,aAAahD,KAAK,CAACiD,UAAU,CAAC,SAASD,SAASA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACtF,MAAMC,KAAK,GAAG1C,QAAQ,CAAC,CAAC;EACxB,MAAM6B,KAAK,GAAGnC,aAAa,CAAC;IAC1BmC,KAAK,EAAEW,OAAO;IACdd,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFiB,IAAI,GAAGD,KAAK,CAACE,4BAA4B,CAAC,CAAC;MAC3CC,WAAW,GAAG,KAAK;MACnBC,SAAS;MACTxB,KAAK;MACLyB,SAAS;MACTC,KAAK,EAAEC,SAAS;MAChBC,YAAY;MACZC,aAAa,EAAEC,iBAAiB;MAChCC,wCAAwC,GAAG,KAAK;MAChDC,OAAO;MACPC,OAAO;MACPC,aAAa;MACbC,WAAW;MACXC,WAAW,GAAG,CAAC;MACfC,iBAAiB;MACjBC,gBAAgB;MAChBC,QAAQ;MACRC,IAAI,EAAEC,MAAM;MACZC,KAAK,GAAG3B,wBAAwB;MAChC4B,MAAM;MACNC,YAAY;MACZC,WAAW;MACXC,mBAAmB;MACnBC,SAAS;MACTC,QAAQ;MACRC,QAAQ;MACRC,QAAQ,EAAEC;IACZ,CAAC,GAAG5C,KAAK;IACT6C,KAAK,GAAGtF,6BAA6B,CAACyC,KAAK,EAAExC,SAAS,CAAC;EACzD,MAAM;IACJ2D,KAAK;IACL2B,iBAAiB;IACjBH;EACF,CAAC,GAAG5D,8BAA8B,CAAC;IACjCc,IAAI,EAAE,WAAW;IACjB8C,QAAQ,EAAEC,YAAY;IACtBzB,KAAK,EAAEC,SAAS;IAChBC,YAAY;IACZW,QAAQ;IACRe,YAAY,EAAE/D;EAChB,CAAC,CAAC;EACF,MAAMgE,oBAAoB,GAAG/D,qBAAqB,CAAC;IACjDkC,KAAK;IACLG,aAAa,EAAEC,iBAAiB;IAChCV,KAAK;IACLb,KAAK;IACL2C;EACF,CAAC,CAAC;EACF,MAAMM,YAAY,GAAG/E,sBAAsB,CAAC,CAAC;EAC7C,MAAMgF,GAAG,GAAG9E,MAAM,CAACuE,QAAQ,CAAC;EAC5B,MAAM;IACJV,IAAI;IACJkB,OAAO;IACPC,YAAY;IACZC,QAAQ;IACRC;EACF,CAAC,GAAG9E,QAAQ,CAAC;IACXyD,IAAI,EAAEC,MAAM;IACZC,KAAK;IACLC,MAAM;IACNC,YAAY;IACZL,QAAQ,EAAEc,iBAAiB;IAC3BR,WAAW;IACXC;EACF,CAAC,CAAC;EACF,MAAM;IACJgB,YAAY;IACZC;EACF,CAAC,GAAG/E,eAAe,CAACuE,oBAAoB,EAAElC,IAAI,EAAEwC,uBAAuB,CAAC;EACxE,MAAMG,cAAc,GAAGhG,KAAK,CAACiG,WAAW,CAAC,CAACC,QAAQ,EAAEC,QAAQ,KAAK;IAC/D,MAAMC,OAAO,GAAGtF,2BAA2B,CAACiD,wCAAwC,EAAEX,KAAK,CAAC;IAC5F,MAAMiD,kBAAkB,GAAGF,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,SAAS,IAAIzB,KAAK,CAAC4B,QAAQ,CAAC,SAAS,CAAC;IACtG,MAAMC,iBAAiB,GAAGA,CAAC;MACzBC,KAAK;MACLC;IACF,CAAC,KAAK;MACJ,IAAIxC,OAAO,IAAImC,OAAO,CAACnC,OAAO,EAAEwC,GAAG,CAAC,EAAE;QACpC,OAAO,KAAK;MACd;MACA,IAAIzC,OAAO,IAAIoC,OAAO,CAACI,KAAK,EAAExC,OAAO,CAAC,EAAE;QACtC,OAAO,KAAK;MACd;MACA,IAAIE,aAAa,IAAIkC,OAAO,CAACI,KAAK,EAAEf,GAAG,CAAC,EAAE;QACxC,OAAO,KAAK;MACd;MACA,IAAItB,WAAW,IAAIiC,OAAO,CAACX,GAAG,EAAEY,kBAAkB,GAAGI,GAAG,GAAGD,KAAK,CAAC,EAAE;QACjE,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC;IACD,MAAME,YAAY,GAAGA,CAACC,SAAS,EAAEC,IAAI,GAAG,CAAC,KAAK;MAC5C,IAAID,SAAS,GAAGC,IAAI,KAAK,CAAC,EAAE;QAC1B,OAAO,KAAK;MACd;MACA,IAAIvC,iBAAiB,EAAE;QACrB,QAAQ8B,QAAQ;UACd,KAAK,OAAO;YACV,OAAO,CAAC9B,iBAAiB,CAACjB,KAAK,CAACyD,QAAQ,CAACtB,oBAAoB,EAAEoB,SAAS,CAAC,EAAE,OAAO,CAAC;UACrF,KAAK,SAAS;YACZ,OAAO,CAACtC,iBAAiB,CAACjB,KAAK,CAAC0D,UAAU,CAACvB,oBAAoB,EAAEoB,SAAS,CAAC,EAAE,SAAS,CAAC;UACzF,KAAK,SAAS;YACZ,OAAO,CAACtC,iBAAiB,CAACjB,KAAK,CAAC2D,UAAU,CAACxB,oBAAoB,EAAEoB,SAAS,CAAC,EAAE,SAAS,CAAC;UACzF;YACE,OAAO,KAAK;QAChB;MACF;MACA,OAAO,IAAI;IACb,CAAC;IACD,QAAQR,QAAQ;MACd,KAAK,OAAO;QACV;UACE,MAAMa,iBAAiB,GAAGnG,sBAAsB,CAACqF,QAAQ,EAAEJ,YAAY,EAAEzC,IAAI,CAAC;UAC9E,MAAM4D,gBAAgB,GAAG7D,KAAK,CAACyD,QAAQ,CAACtB,oBAAoB,EAAEyB,iBAAiB,CAAC;UAChF,MAAMR,KAAK,GAAGpD,KAAK,CAAC2D,UAAU,CAAC3D,KAAK,CAAC0D,UAAU,CAACG,gBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UACxE,MAAMR,GAAG,GAAGrD,KAAK,CAAC2D,UAAU,CAAC3D,KAAK,CAAC0D,UAAU,CAACG,gBAAgB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;UACxE,OAAO,CAACV,iBAAiB,CAAC;YACxBC,KAAK;YACLC;UACF,CAAC,CAAC,IAAI,CAACC,YAAY,CAACM,iBAAiB,CAAC;QACxC;MACF,KAAK,SAAS;QACZ;UACE,MAAME,kBAAkB,GAAG9D,KAAK,CAAC0D,UAAU,CAACvB,oBAAoB,EAAEW,QAAQ,CAAC;UAC3E,MAAMM,KAAK,GAAGpD,KAAK,CAAC2D,UAAU,CAACG,kBAAkB,EAAE,CAAC,CAAC;UACrD,MAAMT,GAAG,GAAGrD,KAAK,CAAC2D,UAAU,CAACG,kBAAkB,EAAE,EAAE,CAAC;UACpD,OAAO,CAACX,iBAAiB,CAAC;YACxBC,KAAK;YACLC;UACF,CAAC,CAAC,IAAI,CAACC,YAAY,CAACR,QAAQ,EAAE9B,WAAW,CAAC;QAC5C;MACF,KAAK,SAAS;QACZ;UACE,MAAM+C,kBAAkB,GAAG/D,KAAK,CAAC2D,UAAU,CAACxB,oBAAoB,EAAEW,QAAQ,CAAC;UAC3E,MAAMM,KAAK,GAAGW,kBAAkB;UAChC,MAAMV,GAAG,GAAGU,kBAAkB;UAC9B,OAAO,CAACZ,iBAAiB,CAAC;YACxBC,KAAK;YACLC;UACF,CAAC,CAAC,IAAI,CAACC,YAAY,CAACR,QAAQ,CAAC;QAC/B;MACF;QACE,MAAM,IAAIkB,KAAK,CAAC,eAAe,CAAC;IACpC;EACF,CAAC,EAAE,CAAC/D,IAAI,EAAEkC,oBAAoB,EAAExB,wCAAwC,EAAEC,OAAO,EAAE8B,YAAY,EAAE7B,OAAO,EAAEG,WAAW,EAAEC,iBAAiB,EAAEjB,KAAK,EAAEc,aAAa,EAAEC,WAAW,EAAEsB,GAAG,EAAEf,KAAK,CAAC,CAAC;EACzL,MAAM2C,UAAU,GAAG7G,KAAK,CAAC,CAAC;EAC1B,MAAM8G,SAAS,GAAGtH,KAAK,CAACuH,OAAO,CAAC,MAAM;IACpC,QAAQ/C,IAAI;MACV,KAAK,OAAO;QACV;UACE,MAAMgD,iBAAiB,GAAGA,CAACC,SAAS,EAAEC,QAAQ,KAAK;YACjD,MAAMV,iBAAiB,GAAGnG,sBAAsB,CAAC4G,SAAS,EAAE3B,YAAY,EAAEzC,IAAI,CAAC;YAC/EwC,uBAAuB,CAACzC,KAAK,CAACyD,QAAQ,CAACtB,oBAAoB,EAAEyB,iBAAiB,CAAC,EAAEU,QAAQ,EAAE,OAAO,CAAC;UACrG,CAAC;UACD,OAAO;YACLnD,QAAQ,EAAEiD,iBAAiB;YAC3BG,SAAS,EAAEvE,KAAK,CAACwE,QAAQ,CAACrC,oBAAoB,CAAC;YAC/CsC,QAAQ,EAAEzG,cAAc,CAAC;cACvBsC,KAAK;cACLN,KAAK;cACLC,IAAI;cACJkB,QAAQ,EAAEiD,iBAAiB;cAC3BM,kBAAkB,EAAEtC,YAAY,CAACuC,oBAAoB;cACrDC,UAAU,EAAEP,SAAS,IAAIzC,QAAQ,IAAIgB,cAAc,CAACyB,SAAS,EAAE,OAAO,CAAC;cACvEJ;YACF,CAAC;UACH,CAAC;QACH;MACF,KAAK,SAAS;QACZ;UACE,MAAMY,YAAY,GAAG7E,KAAK,CAAC8E,UAAU,CAAC3C,oBAAoB,CAAC;UAC3D,MAAM4C,mBAAmB,GAAGA,CAACC,WAAW,EAAEV,QAAQ,KAAK;YACrD7B,uBAAuB,CAACzC,KAAK,CAAC0D,UAAU,CAACvB,oBAAoB,EAAE6C,WAAW,CAAC,EAAEV,QAAQ,EAAE,SAAS,CAAC;UACnG,CAAC;UACD,OAAO;YACLC,SAAS,EAAEM,YAAY;YACvB1D,QAAQ,EAAE4D,mBAAmB;YAC7BN,QAAQ,EAAExG,iBAAiB,CAAC;cAC1B+B,KAAK;cACLM,KAAK,EAAEuE,YAAY;cACnB1D,QAAQ,EAAE4D,mBAAmB;cAC7BL,kBAAkB,EAAEtC,YAAY,CAAC6C,sBAAsB;cACvDL,UAAU,EAAEI,WAAW,IAAIpD,QAAQ,IAAIgB,cAAc,CAACoC,WAAW,EAAE,SAAS,CAAC;cAC7Ef;YACF,CAAC;UACH,CAAC;QACH;MACF,KAAK,SAAS;QACZ;UACE,MAAMiB,YAAY,GAAGlF,KAAK,CAACmF,UAAU,CAAChD,oBAAoB,CAAC;UAC3D,MAAMiD,mBAAmB,GAAGA,CAACC,WAAW,EAAEf,QAAQ,KAAK;YACrD7B,uBAAuB,CAACzC,KAAK,CAAC2D,UAAU,CAACxB,oBAAoB,EAAEkD,WAAW,CAAC,EAAEf,QAAQ,EAAE,SAAS,CAAC;UACnG,CAAC;UACD,OAAO;YACLC,SAAS,EAAEW,YAAY;YACvB/D,QAAQ,EAAEiE,mBAAmB;YAC7BX,QAAQ,EAAExG,iBAAiB,CAAC;cAC1B+B,KAAK;cACLM,KAAK,EAAE4E,YAAY;cACnB/D,QAAQ,EAAEiE,mBAAmB;cAC7BV,kBAAkB,EAAEtC,YAAY,CAACkD,sBAAsB;cACvDV,UAAU,EAAES,WAAW,IAAIzD,QAAQ,IAAIgB,cAAc,CAACyC,WAAW,EAAE,SAAS,CAAC;cAC7EpB;YACF,CAAC;UACH,CAAC;QACH;MACF;QACE,MAAM,IAAID,KAAK,CAAC,yCAAyC,CAAC;IAC9D;EACF,CAAC,EAAE,CAAC5C,IAAI,EAAEpB,KAAK,EAAEM,KAAK,EAAEL,IAAI,EAAEmC,YAAY,CAACuC,oBAAoB,EAAEvC,YAAY,CAAC6C,sBAAsB,EAAE7C,YAAY,CAACkD,sBAAsB,EAAE5C,YAAY,EAAED,uBAAuB,EAAEN,oBAAoB,EAAES,cAAc,EAAEqB,UAAU,EAAErC,QAAQ,CAAC,CAAC;EAC9O,MAAMlD,UAAU,GAAGS,KAAK;EACxB,MAAMR,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,KAAK,CAACO,aAAa,EAAEtC,QAAQ,CAAC;IAChDsD,GAAG,EAAEA,GAAG;IACR4B,SAAS,EAAE9E,IAAI,CAAC8B,OAAO,CAACE,IAAI,EAAE8C,SAAS,CAAC;IACxCjD,UAAU,EAAEA;EACd,CAAC,EAAEsD,KAAK,EAAE;IACRyC,QAAQ,EAAE,CAAC,aAAanG,IAAI,CAACP,KAAK,EAAEtB,QAAQ,CAAC;MAC3C2D,SAAS,EAAEA,SAAS,IAAI,CAAC,CAACqB,WAAW;MACrCtB,WAAW,EAAEA,WAAW,IAAImB,KAAK,CAAC4B,QAAQ,CAAC,OAAO,CAAC;MACnD5C,KAAK,EAAEA,KAAK;MACZiF,IAAI,EAAEnE,IAAI;MACVnB,IAAI,EAAEA,IAAI;MACVe,WAAW,EAAEA,WAAW;MACxB4B,cAAc,EAAEA,cAAc;MAC9BF,YAAY,EAAEA,YAAY;MAC1BC,oBAAoB,EAAEA,oBAAoB;MAC1CsB,UAAU,EAAEA,UAAU;MACtBrC,QAAQ,EAAEA,QAAQ;MAClBC,QAAQ,EAAEA;IACZ,CAAC,EAAEqC,SAAS,CAAC,CAAC,EAAEhD,gBAAgB,IAAI,aAAa5C,IAAI,CAACkB,sBAAsB,EAAE;MAC5EmC,SAAS,EAAEhD,OAAO,CAACG,aAAa;MAChCF,KAAK,EAAEA,KAAK;MACZyB,SAAS,EAAEA,SAAS;MACpBmF,cAAc,EAAEA,CAAA,KAAMlD,OAAO,CAACC,YAAY,CAAC;MAC3CkD,kBAAkB,EAAE,CAAClD,YAAY;MACjCmD,aAAa,EAAEtD,YAAY,CAACuD,gBAAgB;MAC5CC,UAAU,EAAEA,CAAA,KAAMtD,OAAO,CAACE,QAAQ,CAAC;MACnCqD,cAAc,EAAE,CAACrD,QAAQ;MACzBsD,SAAS,EAAE1D,YAAY,CAAC2D,YAAY;MACpCrH,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFsH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtG,SAAS,CAACuG,SAAS,GAAG;EAC5D;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACElG,IAAI,EAAEnD,SAAS,CAACsJ,IAAI;EACpB;AACF;AACA;AACA;EACEjG,WAAW,EAAErD,SAAS,CAACsJ,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEhG,SAAS,EAAEtD,SAAS,CAACsJ,IAAI;EACzB;AACF;AACA;EACEzH,OAAO,EAAE7B,SAAS,CAACuJ,MAAM;EACzB1E,SAAS,EAAE7E,SAAS,CAACwJ,MAAM;EAC3B;AACF;AACA;AACA;EACE9F,YAAY,EAAE1D,SAAS,CAACuJ,MAAM;EAC9B;AACF;AACA;AACA;EACEzE,QAAQ,EAAE9E,SAAS,CAACsJ,IAAI;EACxB;AACF;AACA;AACA;EACEtF,aAAa,EAAEhE,SAAS,CAACsJ,IAAI;EAC7B;AACF;AACA;AACA;EACEzF,wCAAwC,EAAE7D,SAAS,CAACsJ,IAAI;EACxD;AACF;AACA;AACA;EACErF,WAAW,EAAEjE,SAAS,CAACsJ,IAAI;EAC3B;AACF;AACA;EACE3E,WAAW,EAAE3E,SAAS,CAACyJ,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EAC7D;AACF;AACA;AACA;EACE3F,OAAO,EAAE9D,SAAS,CAACuJ,MAAM;EACzB;AACF;AACA;AACA;EACExF,OAAO,EAAE/D,SAAS,CAACuJ,MAAM;EACzB;AACF;AACA;AACA;EACErF,WAAW,EAAElE,SAAS,CAAC0J,MAAM;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACErF,QAAQ,EAAErE,SAAS,CAAC2J,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;EACE/E,mBAAmB,EAAE5E,SAAS,CAAC2J,IAAI;EACnC;AACF;AACA;AACA;AACA;EACEjF,YAAY,EAAE1E,SAAS,CAAC2J,IAAI;EAC5B;AACF;AACA;AACA;AACA;EACElF,MAAM,EAAEzE,SAAS,CAACyJ,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACxD;AACF;AACA;AACA;EACE1E,QAAQ,EAAE/E,SAAS,CAACsJ,IAAI;EACxB;AACF;AACA;AACA;EACE3F,aAAa,EAAE3D,SAAS,CAACuJ,MAAM;EAC/B;AACF;AACA;AACA;AACA;AACA;AACA;EACEpF,iBAAiB,EAAEnE,SAAS,CAAC2J,IAAI;EACjCvF,gBAAgB,EAAEpE,SAAS,CAACsJ,IAAI;EAChC;AACF;AACA;AACA;EACE/F,SAAS,EAAEvD,SAAS,CAACuJ,MAAM;EAC3B;AACF;AACA;AACA;EACEzH,KAAK,EAAE9B,SAAS,CAACuJ,MAAM;EACvB;AACF;AACA;EACEK,EAAE,EAAE5J,SAAS,CAAC6J,SAAS,CAAC,CAAC7J,SAAS,CAAC8J,OAAO,CAAC9J,SAAS,CAAC6J,SAAS,CAAC,CAAC7J,SAAS,CAAC2J,IAAI,EAAE3J,SAAS,CAACuJ,MAAM,EAAEvJ,SAAS,CAACsJ,IAAI,CAAC,CAAC,CAAC,EAAEtJ,SAAS,CAAC2J,IAAI,EAAE3J,SAAS,CAACuJ,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;EACEvE,QAAQ,EAAEhF,SAAS,CAACwJ,MAAM;EAC1B;AACF;AACA;AACA;EACEhG,KAAK,EAAExD,SAAS,CAACuJ,MAAM;EACvB;AACF;AACA;AACA;AACA;EACEjF,IAAI,EAAEtE,SAAS,CAACyJ,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACtD;AACF;AACA;AACA;EACEjF,KAAK,EAAExE,SAAS,CAAC8J,OAAO,CAAC9J,SAAS,CAACyJ,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAACM,UAAU;AACtF,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}