{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getKeyEventProps = getKeyEventProps;\nexports.getMouseEventProps = getMouseEventProps;\nfunction getKeyEventProps(keyDef, state) {\n  var _keyDef$keyCode, _keyDef$key;\n  return {\n    key: keyDef.key,\n    code: keyDef.code,\n    altKey: state.modifiers.alt,\n    ctrlKey: state.modifiers.ctrl,\n    metaKey: state.modifiers.meta,\n    shiftKey: state.modifiers.shift,\n    /** @deprecated use code instead */\n    keyCode: (_keyDef$keyCode = keyDef.keyCode) != null ? _keyDef$keyCode :\n    // istanbul ignore next\n    ((_keyDef$key = keyDef.key) == null ? void 0 : _keyDef$key.length) === 1 ? keyDef.key.charCodeAt(0) : undefined\n  };\n}\nfunction getMouseEventProps(state) {\n  return {\n    altKey: state.modifiers.alt,\n    ctrlKey: state.modifiers.ctrl,\n    metaKey: state.modifiers.meta,\n    shiftKey: state.modifiers.shift\n  };\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getKeyEventProps", "getMouseEventProps", "keyDef", "state", "_keyDef$keyCode", "_keyDef$key", "key", "code", "altKey", "modifiers", "alt", "ctrl<PERSON>ey", "ctrl", "metaKey", "meta", "shift<PERSON>ey", "shift", "keyCode", "length", "charCodeAt", "undefined"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/user-event/dist/keyboard/getEventProps.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getKeyEventProps = getKeyEventProps;\nexports.getMouseEventProps = getMouseEventProps;\n\nfunction getKeyEventProps(keyDef, state) {\n  var _keyDef$keyCode, _keyDef$key;\n\n  return {\n    key: keyDef.key,\n    code: keyDef.code,\n    altKey: state.modifiers.alt,\n    ctrlKey: state.modifiers.ctrl,\n    metaKey: state.modifiers.meta,\n    shiftKey: state.modifiers.shift,\n\n    /** @deprecated use code instead */\n    keyCode: (_keyDef$keyCode = keyDef.keyCode) != null ? _keyDef$keyCode : // istanbul ignore next\n    ((_keyDef$key = keyDef.key) == null ? void 0 : _keyDef$key.length) === 1 ? keyDef.key.charCodeAt(0) : undefined\n  };\n}\n\nfunction getMouseEventProps(state) {\n  return {\n    altKey: state.modifiers.alt,\n    ctrlKey: state.modifiers.ctrl,\n    metaKey: state.modifiers.meta,\n    shiftKey: state.modifiers.shift\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB;AAC3CF,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAE/C,SAASD,gBAAgBA,CAACE,MAAM,EAAEC,KAAK,EAAE;EACvC,IAAIC,eAAe,EAAEC,WAAW;EAEhC,OAAO;IACLC,GAAG,EAAEJ,MAAM,CAACI,GAAG;IACfC,IAAI,EAAEL,MAAM,CAACK,IAAI;IACjBC,MAAM,EAAEL,KAAK,CAACM,SAAS,CAACC,GAAG;IAC3BC,OAAO,EAAER,KAAK,CAACM,SAAS,CAACG,IAAI;IAC7BC,OAAO,EAAEV,KAAK,CAACM,SAAS,CAACK,IAAI;IAC7BC,QAAQ,EAAEZ,KAAK,CAACM,SAAS,CAACO,KAAK;IAE/B;IACAC,OAAO,EAAE,CAACb,eAAe,GAAGF,MAAM,CAACe,OAAO,KAAK,IAAI,GAAGb,eAAe;IAAG;IACxE,CAAC,CAACC,WAAW,GAAGH,MAAM,CAACI,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,WAAW,CAACa,MAAM,MAAM,CAAC,GAAGhB,MAAM,CAACI,GAAG,CAACa,UAAU,CAAC,CAAC,CAAC,GAAGC;EACxG,CAAC;AACH;AAEA,SAASnB,kBAAkBA,CAACE,KAAK,EAAE;EACjC,OAAO;IACLK,MAAM,EAAEL,KAAK,CAACM,SAAS,CAACC,GAAG;IAC3BC,OAAO,EAAER,KAAK,CAACM,SAAS,CAACG,IAAI;IAC7BC,OAAO,EAAEV,KAAK,CAACM,SAAS,CAACK,IAAI;IAC7BC,QAAQ,EAAEZ,KAAK,CAACM,SAAS,CAACO;EAC5B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}