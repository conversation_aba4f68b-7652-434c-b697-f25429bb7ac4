{"ast": null, "code": "// https://w3c.github.io/html-aria/#document-conformance-requirements-for-use-of-aria-attributes-in-html\n\n/**\n * Safe Element.localName for all supported environments\n * @param element\n */\nexport function getLocalName(element) {\n  var _element$localName;\n  return (\n    // eslint-disable-next-line no-restricted-properties -- actual guard for environments without localName\n    (_element$localName = element.localName) !== null && _element$localName !== void 0 ? _element$localName :\n    // eslint-disable-next-line no-restricted-properties -- required for the fallback\n    element.tagName.toLowerCase()\n  );\n}\nvar localNameToRoleMappings = {\n  article: \"article\",\n  aside: \"complementary\",\n  button: \"button\",\n  datalist: \"listbox\",\n  dd: \"definition\",\n  details: \"group\",\n  dialog: \"dialog\",\n  dt: \"term\",\n  fieldset: \"group\",\n  figure: \"figure\",\n  // WARNING: Only with an accessible name\n  form: \"form\",\n  footer: \"contentinfo\",\n  h1: \"heading\",\n  h2: \"heading\",\n  h3: \"heading\",\n  h4: \"heading\",\n  h5: \"heading\",\n  h6: \"heading\",\n  header: \"banner\",\n  hr: \"separator\",\n  html: \"document\",\n  legend: \"legend\",\n  li: \"listitem\",\n  math: \"math\",\n  main: \"main\",\n  menu: \"list\",\n  nav: \"navigation\",\n  ol: \"list\",\n  optgroup: \"group\",\n  // WARNING: Only in certain context\n  option: \"option\",\n  output: \"status\",\n  progress: \"progressbar\",\n  // WARNING: Only with an accessible name\n  section: \"region\",\n  summary: \"button\",\n  table: \"table\",\n  tbody: \"rowgroup\",\n  textarea: \"textbox\",\n  tfoot: \"rowgroup\",\n  // WARNING: Only in certain context\n  td: \"cell\",\n  th: \"columnheader\",\n  thead: \"rowgroup\",\n  tr: \"row\",\n  ul: \"list\"\n};\nvar prohibitedAttributes = {\n  caption: new Set([\"aria-label\", \"aria-labelledby\"]),\n  code: new Set([\"aria-label\", \"aria-labelledby\"]),\n  deletion: new Set([\"aria-label\", \"aria-labelledby\"]),\n  emphasis: new Set([\"aria-label\", \"aria-labelledby\"]),\n  generic: new Set([\"aria-label\", \"aria-labelledby\", \"aria-roledescription\"]),\n  insertion: new Set([\"aria-label\", \"aria-labelledby\"]),\n  paragraph: new Set([\"aria-label\", \"aria-labelledby\"]),\n  presentation: new Set([\"aria-label\", \"aria-labelledby\"]),\n  strong: new Set([\"aria-label\", \"aria-labelledby\"]),\n  subscript: new Set([\"aria-label\", \"aria-labelledby\"]),\n  superscript: new Set([\"aria-label\", \"aria-labelledby\"])\n};\n\n/**\n *\n * @param element\n * @param role The role used for this element. This is specified to control whether you want to use the implicit or explicit role.\n */\nfunction hasGlobalAriaAttributes(element, role) {\n  // https://rawgit.com/w3c/aria/stable/#global_states\n  // commented attributes are deprecated\n  return [\"aria-atomic\", \"aria-busy\", \"aria-controls\", \"aria-current\", \"aria-describedby\", \"aria-details\",\n  // \"disabled\",\n  \"aria-dropeffect\",\n  // \"errormessage\",\n  \"aria-flowto\", \"aria-grabbed\",\n  // \"haspopup\",\n  \"aria-hidden\",\n  // \"invalid\",\n  \"aria-keyshortcuts\", \"aria-label\", \"aria-labelledby\", \"aria-live\", \"aria-owns\", \"aria-relevant\", \"aria-roledescription\"].some(function (attributeName) {\n    var _prohibitedAttributes;\n    return element.hasAttribute(attributeName) && !((_prohibitedAttributes = prohibitedAttributes[role]) !== null && _prohibitedAttributes !== void 0 && _prohibitedAttributes.has(attributeName));\n  });\n}\nfunction ignorePresentationalRole(element, implicitRole) {\n  // https://rawgit.com/w3c/aria/stable/#conflict_resolution_presentation_none\n  return hasGlobalAriaAttributes(element, implicitRole);\n}\nexport default function getRole(element) {\n  var explicitRole = getExplicitRole(element);\n  if (explicitRole === null || explicitRole === \"presentation\") {\n    var implicitRole = getImplicitRole(element);\n    if (explicitRole !== \"presentation\" || ignorePresentationalRole(element, implicitRole || \"\")) {\n      return implicitRole;\n    }\n  }\n  return explicitRole;\n}\nfunction getImplicitRole(element) {\n  var mappedByTag = localNameToRoleMappings[getLocalName(element)];\n  if (mappedByTag !== undefined) {\n    return mappedByTag;\n  }\n  switch (getLocalName(element)) {\n    case \"a\":\n    case \"area\":\n    case \"link\":\n      if (element.hasAttribute(\"href\")) {\n        return \"link\";\n      }\n      break;\n    case \"img\":\n      if (element.getAttribute(\"alt\") === \"\" && !ignorePresentationalRole(element, \"img\")) {\n        return \"presentation\";\n      }\n      return \"img\";\n    case \"input\":\n      {\n        var _ref = element,\n          type = _ref.type;\n        switch (type) {\n          case \"button\":\n          case \"image\":\n          case \"reset\":\n          case \"submit\":\n            return \"button\";\n          case \"checkbox\":\n          case \"radio\":\n            return type;\n          case \"range\":\n            return \"slider\";\n          case \"email\":\n          case \"tel\":\n          case \"text\":\n          case \"url\":\n            if (element.hasAttribute(\"list\")) {\n              return \"combobox\";\n            }\n            return \"textbox\";\n          case \"search\":\n            if (element.hasAttribute(\"list\")) {\n              return \"combobox\";\n            }\n            return \"searchbox\";\n          case \"number\":\n            return \"spinbutton\";\n          default:\n            return null;\n        }\n      }\n    case \"select\":\n      if (element.hasAttribute(\"multiple\") || element.size > 1) {\n        return \"listbox\";\n      }\n      return \"combobox\";\n  }\n  return null;\n}\nfunction getExplicitRole(element) {\n  var role = element.getAttribute(\"role\");\n  if (role !== null) {\n    var explicitRole = role.trim().split(\" \")[0];\n    // String.prototype.split(sep, limit) will always return an array with at least one member\n    // as long as limit is either undefined or > 0\n    if (explicitRole.length > 0) {\n      return explicitRole;\n    }\n  }\n  return null;\n}", "map": {"version": 3, "names": ["getLocalName", "element", "_element$localName", "localName", "tagName", "toLowerCase", "localNameToRoleMappings", "article", "aside", "button", "datalist", "dd", "details", "dialog", "dt", "fieldset", "figure", "form", "footer", "h1", "h2", "h3", "h4", "h5", "h6", "header", "hr", "html", "legend", "li", "math", "main", "menu", "nav", "ol", "optgroup", "option", "output", "progress", "section", "summary", "table", "tbody", "textarea", "tfoot", "td", "th", "thead", "tr", "ul", "prohibitedAttributes", "caption", "Set", "code", "deletion", "emphasis", "generic", "insertion", "paragraph", "presentation", "strong", "subscript", "superscript", "hasGlobalAriaAttributes", "role", "some", "attributeName", "_prohibitedAttributes", "hasAttribute", "has", "ignorePresentationalRole", "implicitRole", "getRole", "explicitRole", "getExplicitRole", "getImplicitRole", "mappedByTag", "undefined", "getAttribute", "_ref", "type", "size", "trim", "split", "length"], "sources": ["C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\node_modules\\dom-accessibility-api\\sources\\getRole.ts"], "sourcesContent": ["// https://w3c.github.io/html-aria/#document-conformance-requirements-for-use-of-aria-attributes-in-html\n\n/**\n * Safe Element.localName for all supported environments\n * @param element\n */\nexport function getLocalName(element: Element): string {\n\treturn (\n\t\t// eslint-disable-next-line no-restricted-properties -- actual guard for environments without localName\n\t\telement.localName ??\n\t\t// eslint-disable-next-line no-restricted-properties -- required for the fallback\n\t\telement.tagName.toLowerCase()\n\t);\n}\n\nconst localNameToRoleMappings: Record<string, string | undefined> = {\n\tarticle: \"article\",\n\taside: \"complementary\",\n\tbutton: \"button\",\n\tdatalist: \"listbox\",\n\tdd: \"definition\",\n\tdetails: \"group\",\n\tdialog: \"dialog\",\n\tdt: \"term\",\n\tfieldset: \"group\",\n\tfigure: \"figure\",\n\t// WARNING: Only with an accessible name\n\tform: \"form\",\n\tfooter: \"contentinfo\",\n\th1: \"heading\",\n\th2: \"heading\",\n\th3: \"heading\",\n\th4: \"heading\",\n\th5: \"heading\",\n\th6: \"heading\",\n\theader: \"banner\",\n\thr: \"separator\",\n\thtml: \"document\",\n\tlegend: \"legend\",\n\tli: \"listitem\",\n\tmath: \"math\",\n\tmain: \"main\",\n\tmenu: \"list\",\n\tnav: \"navigation\",\n\tol: \"list\",\n\toptgroup: \"group\",\n\t// WARNING: Only in certain context\n\toption: \"option\",\n\toutput: \"status\",\n\tprogress: \"progressbar\",\n\t// WARNING: Only with an accessible name\n\tsection: \"region\",\n\tsummary: \"button\",\n\ttable: \"table\",\n\ttbody: \"rowgroup\",\n\ttextarea: \"textbox\",\n\ttfoot: \"rowgroup\",\n\t// WARNING: Only in certain context\n\ttd: \"cell\",\n\tth: \"columnheader\",\n\tthead: \"rowgroup\",\n\ttr: \"row\",\n\tul: \"list\",\n};\n\nconst prohibitedAttributes: Record<string, Set<string>> = {\n\tcaption: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tcode: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tdeletion: new Set([\"aria-label\", \"aria-labelledby\"]),\n\temphasis: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tgeneric: new Set([\"aria-label\", \"aria-labelledby\", \"aria-roledescription\"]),\n\tinsertion: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tparagraph: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tpresentation: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tstrong: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tsubscript: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tsuperscript: new Set([\"aria-label\", \"aria-labelledby\"]),\n};\n\n/**\n *\n * @param element\n * @param role The role used for this element. This is specified to control whether you want to use the implicit or explicit role.\n */\nfunction hasGlobalAriaAttributes(element: Element, role: string): boolean {\n\t// https://rawgit.com/w3c/aria/stable/#global_states\n\t// commented attributes are deprecated\n\treturn [\n\t\t\"aria-atomic\",\n\t\t\"aria-busy\",\n\t\t\"aria-controls\",\n\t\t\"aria-current\",\n\t\t\"aria-describedby\",\n\t\t\"aria-details\",\n\t\t// \"disabled\",\n\t\t\"aria-dropeffect\",\n\t\t// \"errormessage\",\n\t\t\"aria-flowto\",\n\t\t\"aria-grabbed\",\n\t\t// \"haspopup\",\n\t\t\"aria-hidden\",\n\t\t// \"invalid\",\n\t\t\"aria-keyshortcuts\",\n\t\t\"aria-label\",\n\t\t\"aria-labelledby\",\n\t\t\"aria-live\",\n\t\t\"aria-owns\",\n\t\t\"aria-relevant\",\n\t\t\"aria-roledescription\",\n\t].some((attributeName) => {\n\t\treturn (\n\t\t\telement.hasAttribute(attributeName) &&\n\t\t\t!prohibitedAttributes[role]?.has(attributeName)\n\t\t);\n\t});\n}\n\nfunction ignorePresentationalRole(\n\telement: Element,\n\timplicitRole: string\n): boolean {\n\t// https://rawgit.com/w3c/aria/stable/#conflict_resolution_presentation_none\n\treturn hasGlobalAriaAttributes(element, implicitRole);\n}\n\nexport default function getRole(element: Element): string | null {\n\tconst explicitRole = getExplicitRole(element);\n\tif (explicitRole === null || explicitRole === \"presentation\") {\n\t\tconst implicitRole = getImplicitRole(element);\n\t\tif (\n\t\t\texplicitRole !== \"presentation\" ||\n\t\t\tignorePresentationalRole(element, implicitRole || \"\")\n\t\t) {\n\t\t\treturn implicitRole;\n\t\t}\n\t}\n\n\treturn explicitRole;\n}\n\nfunction getImplicitRole(element: Element): string | null {\n\tconst mappedByTag = localNameToRoleMappings[getLocalName(element)];\n\tif (mappedByTag !== undefined) {\n\t\treturn mappedByTag;\n\t}\n\n\tswitch (getLocalName(element)) {\n\t\tcase \"a\":\n\t\tcase \"area\":\n\t\tcase \"link\":\n\t\t\tif (element.hasAttribute(\"href\")) {\n\t\t\t\treturn \"link\";\n\t\t\t}\n\t\t\tbreak;\n\t\tcase \"img\":\n\t\t\tif (\n\t\t\t\telement.getAttribute(\"alt\") === \"\" &&\n\t\t\t\t!ignorePresentationalRole(element, \"img\")\n\t\t\t) {\n\t\t\t\treturn \"presentation\";\n\t\t\t}\n\t\t\treturn \"img\";\n\t\tcase \"input\": {\n\t\t\tconst { type } = element as HTMLInputElement;\n\t\t\tswitch (type) {\n\t\t\t\tcase \"button\":\n\t\t\t\tcase \"image\":\n\t\t\t\tcase \"reset\":\n\t\t\t\tcase \"submit\":\n\t\t\t\t\treturn \"button\";\n\t\t\t\tcase \"checkbox\":\n\t\t\t\tcase \"radio\":\n\t\t\t\t\treturn type;\n\t\t\t\tcase \"range\":\n\t\t\t\t\treturn \"slider\";\n\t\t\t\tcase \"email\":\n\t\t\t\tcase \"tel\":\n\t\t\t\tcase \"text\":\n\t\t\t\tcase \"url\":\n\t\t\t\t\tif (element.hasAttribute(\"list\")) {\n\t\t\t\t\t\treturn \"combobox\";\n\t\t\t\t\t}\n\t\t\t\t\treturn \"textbox\";\n\n\t\t\t\tcase \"search\":\n\t\t\t\t\tif (element.hasAttribute(\"list\")) {\n\t\t\t\t\t\treturn \"combobox\";\n\t\t\t\t\t}\n\t\t\t\t\treturn \"searchbox\";\n\t\t\t\tcase \"number\":\n\t\t\t\t\treturn \"spinbutton\";\n\t\t\t\tdefault:\n\t\t\t\t\treturn null;\n\t\t\t}\n\t\t}\n\t\tcase \"select\":\n\t\t\tif (\n\t\t\t\telement.hasAttribute(\"multiple\") ||\n\t\t\t\t(element as HTMLSelectElement).size > 1\n\t\t\t) {\n\t\t\t\treturn \"listbox\";\n\t\t\t}\n\t\t\treturn \"combobox\";\n\t}\n\treturn null;\n}\n\nfunction getExplicitRole(element: Element): string | null {\n\tconst role = element.getAttribute(\"role\");\n\tif (role !== null) {\n\t\tconst explicitRole = role.trim().split(\" \")[0];\n\t\t// String.prototype.split(sep, limit) will always return an array with at least one member\n\t\t// as long as limit is either undefined or > 0\n\t\tif (explicitRole.length > 0) {\n\t\t\treturn explicitRole;\n\t\t}\n\t}\n\n\treturn null;\n}\n"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASA,YAAYA,CAACC,OAAgB,EAAU;EAAA,IAAAC,kBAAA;EACtD;IACC;IAAA,CAAAA,kBAAA,GACAD,OAAO,CAACE,SAAS,cAAAD,kBAAA,cAAAA,kBAAA;IACjB;IACAD,OAAO,CAACG,OAAO,CAACC,WAAW;EAAA;AAE7B;AAEA,IAAMC,uBAA2D,GAAG;EACnEC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,eAAe;EACtBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,SAAS;EACnBC,EAAE,EAAE,YAAY;EAChBC,OAAO,EAAE,OAAO;EAChBC,MAAM,EAAE,QAAQ;EAChBC,EAAE,EAAE,MAAM;EACVC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,QAAQ;EAChB;EACAC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,aAAa;EACrBC,EAAE,EAAE,SAAS;EACbC,EAAE,EAAE,SAAS;EACbC,EAAE,EAAE,SAAS;EACbC,EAAE,EAAE,SAAS;EACbC,EAAE,EAAE,SAAS;EACbC,EAAE,EAAE,SAAS;EACbC,MAAM,EAAE,QAAQ;EAChBC,EAAE,EAAE,WAAW;EACfC,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAE,QAAQ;EAChBC,EAAE,EAAE,UAAU;EACdC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,YAAY;EACjBC,EAAE,EAAE,MAAM;EACVC,QAAQ,EAAE,OAAO;EACjB;EACAC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,aAAa;EACvB;EACAC,OAAO,EAAE,QAAQ;EACjBC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,UAAU;EACjBC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,UAAU;EACjB;EACAC,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,cAAc;EAClBC,KAAK,EAAE,UAAU;EACjBC,EAAE,EAAE,KAAK;EACTC,EAAE,EAAE;AACL,CAAC;AAED,IAAMC,oBAAiD,GAAG;EACzDC,OAAO,EAAE,IAAIC,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACnDC,IAAI,EAAE,IAAID,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EAChDE,QAAQ,EAAE,IAAIF,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACpDG,QAAQ,EAAE,IAAIH,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACpDI,OAAO,EAAE,IAAIJ,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,EAAE,sBAAsB,CAAC,CAAC;EAC3EK,SAAS,EAAE,IAAIL,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACrDM,SAAS,EAAE,IAAIN,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACrDO,YAAY,EAAE,IAAIP,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACxDQ,MAAM,EAAE,IAAIR,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EAClDS,SAAS,EAAE,IAAIT,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACrDU,WAAW,EAAE,IAAIV,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC;AACvD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASW,uBAAuBA,CAAC9D,OAAgB,EAAE+D,IAAY,EAAW;EACzE;EACA;EACA,OAAO,CACN,aAAa,EACb,WAAW,EACX,eAAe,EACf,cAAc,EACd,kBAAkB,EAClB,cAAc;EACd;EACA,iBAAiB;EACjB;EACA,aAAa,EACb,cAAc;EACd;EACA,aAAa;EACb;EACA,mBAAmB,EACnB,YAAY,EACZ,iBAAiB,EACjB,WAAW,EACX,WAAW,EACX,eAAe,EACf,sBAAsB,CACtB,CAACC,IAAI,CAAC,UAACC,aAAa,EAAK;IAAA,IAAAC,qBAAA;IACzB,OACClE,OAAO,CAACmE,YAAY,CAACF,aAAa,CAAC,IACnC,GAAAC,qBAAA,GAACjB,oBAAoB,CAACc,IAAI,CAAC,cAAAG,qBAAA,eAA1BA,qBAAA,CAA4BE,GAAG,CAACH,aAAa,CAAC;EAEjD,CAAC,CAAC;AACH;AAEA,SAASI,wBAAwBA,CAChCrE,OAAgB,EAChBsE,YAAoB,EACV;EACV;EACA,OAAOR,uBAAuB,CAAC9D,OAAO,EAAEsE,YAAY,CAAC;AACtD;AAEA,eAAe,SAASC,OAAOA,CAACvE,OAAgB,EAAiB;EAChE,IAAMwE,YAAY,GAAGC,eAAe,CAACzE,OAAO,CAAC;EAC7C,IAAIwE,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,cAAc,EAAE;IAC7D,IAAMF,YAAY,GAAGI,eAAe,CAAC1E,OAAO,CAAC;IAC7C,IACCwE,YAAY,KAAK,cAAc,IAC/BH,wBAAwB,CAACrE,OAAO,EAAEsE,YAAY,IAAI,EAAE,CAAC,EACpD;MACD,OAAOA,YAAY;IACpB;EACD;EAEA,OAAOE,YAAY;AACpB;AAEA,SAASE,eAAeA,CAAC1E,OAAgB,EAAiB;EACzD,IAAM2E,WAAW,GAAGtE,uBAAuB,CAACN,YAAY,CAACC,OAAO,CAAC,CAAC;EAClE,IAAI2E,WAAW,KAAKC,SAAS,EAAE;IAC9B,OAAOD,WAAW;EACnB;EAEA,QAAQ5E,YAAY,CAACC,OAAO,CAAC;IAC5B,KAAK,GAAG;IACR,KAAK,MAAM;IACX,KAAK,MAAM;MACV,IAAIA,OAAO,CAACmE,YAAY,CAAC,MAAM,CAAC,EAAE;QACjC,OAAO,MAAM;MACd;MACA;IACD,KAAK,KAAK;MACT,IACCnE,OAAO,CAAC6E,YAAY,CAAC,KAAK,CAAC,KAAK,EAAE,IAClC,CAACR,wBAAwB,CAACrE,OAAO,EAAE,KAAK,CAAC,EACxC;QACD,OAAO,cAAc;MACtB;MACA,OAAO,KAAK;IACb,KAAK,OAAO;MAAE;QACb,IAAA8E,IAAA,GAAiB9E,OAAO;UAAhB+E,IAAI,GAAAD,IAAA,CAAJC,IAAI;QACZ,QAAQA,IAAI;UACX,KAAK,QAAQ;UACb,KAAK,OAAO;UACZ,KAAK,OAAO;UACZ,KAAK,QAAQ;YACZ,OAAO,QAAQ;UAChB,KAAK,UAAU;UACf,KAAK,OAAO;YACX,OAAOA,IAAI;UACZ,KAAK,OAAO;YACX,OAAO,QAAQ;UAChB,KAAK,OAAO;UACZ,KAAK,KAAK;UACV,KAAK,MAAM;UACX,KAAK,KAAK;YACT,IAAI/E,OAAO,CAACmE,YAAY,CAAC,MAAM,CAAC,EAAE;cACjC,OAAO,UAAU;YAClB;YACA,OAAO,SAAS;UAEjB,KAAK,QAAQ;YACZ,IAAInE,OAAO,CAACmE,YAAY,CAAC,MAAM,CAAC,EAAE;cACjC,OAAO,UAAU;YAClB;YACA,OAAO,WAAW;UACnB,KAAK,QAAQ;YACZ,OAAO,YAAY;UACpB;YACC,OAAO,IAAI;QAAC;MAEf;IACA,KAAK,QAAQ;MACZ,IACCnE,OAAO,CAACmE,YAAY,CAAC,UAAU,CAAC,IAC/BnE,OAAO,CAAuBgF,IAAI,GAAG,CAAC,EACtC;QACD,OAAO,SAAS;MACjB;MACA,OAAO,UAAU;EAAC;EAEpB,OAAO,IAAI;AACZ;AAEA,SAASP,eAAeA,CAACzE,OAAgB,EAAiB;EACzD,IAAM+D,IAAI,GAAG/D,OAAO,CAAC6E,YAAY,CAAC,MAAM,CAAC;EACzC,IAAId,IAAI,KAAK,IAAI,EAAE;IAClB,IAAMS,YAAY,GAAGT,IAAI,CAACkB,IAAI,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9C;IACA;IACA,IAAIV,YAAY,CAACW,MAAM,GAAG,CAAC,EAAE;MAC5B,OAAOX,YAAY;IACpB;EACD;EAEA,OAAO,IAAI;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}