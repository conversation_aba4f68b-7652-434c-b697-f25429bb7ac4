{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"desktopModeMediaQuery\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useThemeProps } from '@mui/material/styles';\nimport { refType } from '@mui/utils';\nimport { DesktopTimePicker } from \"../DesktopTimePicker/index.js\";\nimport { MobileTimePicker } from \"../MobileTimePicker/index.js\";\nimport { DEFAULT_DESKTOP_MODE_MEDIA_QUERY } from \"../internals/utils/utils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [Validation](https://mui.com/x/react-date-pickers/validation/)\n *\n * API:\n *\n * - [TimePicker API](https://mui.com/x/api/date-pickers/time-picker/)\n */\nconst TimePicker = /*#__PURE__*/React.forwardRef(function TimePicker(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimePicker'\n  });\n  const {\n      desktopModeMediaQuery = DEFAULT_DESKTOP_MODE_MEDIA_QUERY\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  // defaults to `true` in environments where `window.matchMedia` would not be available (i.e. test/jsdom)\n  const isDesktop = useMediaQuery(desktopModeMediaQuery, {\n    defaultMatches: true\n  });\n  if (isDesktop) {\n    return /*#__PURE__*/_jsx(DesktopTimePicker, _extends({\n      ref: ref\n    }, other));\n  }\n  return /*#__PURE__*/_jsx(MobileTimePicker, _extends({\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? TimePicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * Display ampm controls under the clock (instead of in the toolbar).\n   * @default true on desktop, false on mobile\n   */\n  ampmInClock: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  className: PropTypes.string,\n  /**\n   * If `true`, the popover or modal will close after submitting the full date.\n   * @default `true` for desktop, `false` for mobile (based on the chosen wrapper and `desktopModeMediaQuery` prop).\n   */\n  closeOnSelect: PropTypes.bool,\n  /**\n   * The default value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * CSS media query when `Mobile` mode will be changed to `Desktop`.\n   * @default '@media (pointer: fine)'\n   * @example '@media (min-width: 720px)' or theme.breakpoints.up(\"sm\")\n   */\n  desktopModeMediaQuery: PropTypes.string,\n  /**\n   * If `true`, the picker and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, the open picker button will not be rendered (renders only the field).\n   * @default false\n   */\n  disableOpenPicker: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * @default false\n   */\n  enableAccessibleFieldDOMStructure: PropTypes.any,\n  /**\n   * Format of the date when rendered in the input(s).\n   * Defaults to localized format based on the used `views`.\n   */\n  format: PropTypes.string,\n  /**\n   * Density of the format when rendered in the input.\n   * Setting `formatDensity` to `\"spacious\"` will add a space before and after each `/`, `-` and `.` character.\n   * @default \"dense\"\n   */\n  formatDensity: PropTypes.oneOf(['dense', 'spacious']),\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * The label content.\n   */\n  label: PropTypes.node,\n  /**\n   * Locale for components texts.\n   * Allows overriding texts coming from `LocalizationProvider` and `theme`.\n   */\n  localeText: PropTypes.object,\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Name attribute used by the `input` element in the Field.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value is accepted.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The value that was just accepted.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onAccept: PropTypes.func,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The new value.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see `open`).\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the error associated with the current value changes.\n   * When a validation error is detected, the `error` parameter contains a non-null value.\n   * This can be used to render an appropriate form error.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TError} error The reason why the current value is not valid.\n   * @param {TValue} value The value associated with the error.\n   */\n  onError: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see `open`).\n   */\n  onOpen: PropTypes.func,\n  /**\n   * Callback fired when the selected sections change.\n   * @param {FieldSelectedSections} newValue The new selected sections.\n   */\n  onSelectedSectionsChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * Control the popup or dialog open state.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Force rendering in particular orientation.\n   */\n  orientation: PropTypes.oneOf(['landscape', 'portrait']),\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, disable heavy animations.\n   * @default `@media(prefers-reduced-motion: reduce)` || `navigator.userAgent` matches Android <10 or iOS <13\n   */\n  reduceAnimations: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid date-time using the validation props, except callbacks like `shouldDisable<...>`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * The currently selected sections.\n   * This prop accepts four formats:\n   * 1. If a number is provided, the section at this index will be selected.\n   * 2. If a string of type `FieldSectionType` is provided, the first section with that name will be selected.\n   * 3. If `\"all\"` is provided, all the sections will be selected.\n   * 4. If `null` is provided, no section will be selected.\n   * If not provided, the selected sections will be handled internally.\n   */\n  selectedSections: PropTypes.oneOfType([PropTypes.oneOf(['all', 'day', 'empty', 'hours', 'meridiem', 'minutes', 'month', 'seconds', 'weekDay', 'year']), PropTypes.number]),\n  /**\n   * Disable specific time.\n   * @template TDate\n   * @param {TDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  /**\n   * If `true`, disabled digital clock items will not be rendered.\n   * @default false\n   */\n  skipDisabled: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Amount of time options below or at which the single column time renderer is used.\n   * @default 24\n   */\n  thresholdToRenderTimeInASingleColumn: PropTypes.number,\n  /**\n   * The time steps between two time unit options.\n   * For example, if `timeStep.minutes = 8`, then the available minute options will be `[0, 8, 16, 24, 32, 40, 48, 56]`.\n   * When single column time renderer is used, only `timeStep.minutes` will be used.\n   * @default{ hours: 1, minutes: 5, seconds: 5 }\n   */\n  timeSteps: PropTypes.shape({\n    hours: PropTypes.number,\n    minutes: PropTypes.number,\n    seconds: PropTypes.number\n  }),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Define custom view renderers for each section.\n   * If `null`, the section will only have field editing.\n   * If `undefined`, internally defined view will be used.\n   */\n  viewRenderers: PropTypes.shape({\n    hours: PropTypes.func,\n    meridiem: PropTypes.func,\n    minutes: PropTypes.func,\n    seconds: PropTypes.func\n  }),\n  /**\n   * Available views.\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'minutes', 'seconds']).isRequired)\n} : void 0;\nexport { TimePicker };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "useMediaQuery", "useThemeProps", "refType", "DesktopTimePicker", "MobileTimePicker", "DEFAULT_DESKTOP_MODE_MEDIA_QUERY", "jsx", "_jsx", "TimePicker", "forwardRef", "inProps", "ref", "props", "name", "desktopModeMediaQuery", "other", "isDesktop", "defaultMatches", "process", "env", "NODE_ENV", "propTypes", "ampm", "bool", "ampmInClock", "autoFocus", "className", "string", "closeOnSelect", "defaultValue", "object", "disabled", "disableFuture", "disableIgnoringDatePartForTimeValidation", "disableOpenPicker", "disablePast", "enableAccessibleFieldDOMStructure", "any", "format", "formatDensity", "oneOf", "inputRef", "label", "node", "localeText", "maxTime", "minTime", "minutesStep", "number", "onAccept", "func", "onChange", "onClose", "onError", "onOpen", "onSelectedSectionsChange", "onViewChange", "open", "openTo", "orientation", "readOnly", "reduceAnimations", "referenceDate", "selectedSections", "oneOfType", "shouldDisableTime", "skipDisabled", "slotProps", "slots", "sx", "arrayOf", "thresholdToRenderTimeInASingleColumn", "timeSteps", "shape", "hours", "minutes", "seconds", "timezone", "value", "view", "viewRenderers", "meridiem", "views", "isRequired"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/TimePicker/TimePicker.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"desktopModeMediaQuery\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useThemeProps } from '@mui/material/styles';\nimport { refType } from '@mui/utils';\nimport { DesktopTimePicker } from \"../DesktopTimePicker/index.js\";\nimport { MobileTimePicker } from \"../MobileTimePicker/index.js\";\nimport { DEFAULT_DESKTOP_MODE_MEDIA_QUERY } from \"../internals/utils/utils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [Validation](https://mui.com/x/react-date-pickers/validation/)\n *\n * API:\n *\n * - [TimePicker API](https://mui.com/x/api/date-pickers/time-picker/)\n */\nconst TimePicker = /*#__PURE__*/React.forwardRef(function TimePicker(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimePicker'\n  });\n  const {\n      desktopModeMediaQuery = DEFAULT_DESKTOP_MODE_MEDIA_QUERY\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  // defaults to `true` in environments where `window.matchMedia` would not be available (i.e. test/jsdom)\n  const isDesktop = useMediaQuery(desktopModeMediaQuery, {\n    defaultMatches: true\n  });\n  if (isDesktop) {\n    return /*#__PURE__*/_jsx(DesktopTimePicker, _extends({\n      ref: ref\n    }, other));\n  }\n  return /*#__PURE__*/_jsx(MobileTimePicker, _extends({\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? TimePicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * Display ampm controls under the clock (instead of in the toolbar).\n   * @default true on desktop, false on mobile\n   */\n  ampmInClock: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  className: PropTypes.string,\n  /**\n   * If `true`, the popover or modal will close after submitting the full date.\n   * @default `true` for desktop, `false` for mobile (based on the chosen wrapper and `desktopModeMediaQuery` prop).\n   */\n  closeOnSelect: PropTypes.bool,\n  /**\n   * The default value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * CSS media query when `Mobile` mode will be changed to `Desktop`.\n   * @default '@media (pointer: fine)'\n   * @example '@media (min-width: 720px)' or theme.breakpoints.up(\"sm\")\n   */\n  desktopModeMediaQuery: PropTypes.string,\n  /**\n   * If `true`, the picker and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, the open picker button will not be rendered (renders only the field).\n   * @default false\n   */\n  disableOpenPicker: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * @default false\n   */\n  enableAccessibleFieldDOMStructure: PropTypes.any,\n  /**\n   * Format of the date when rendered in the input(s).\n   * Defaults to localized format based on the used `views`.\n   */\n  format: PropTypes.string,\n  /**\n   * Density of the format when rendered in the input.\n   * Setting `formatDensity` to `\"spacious\"` will add a space before and after each `/`, `-` and `.` character.\n   * @default \"dense\"\n   */\n  formatDensity: PropTypes.oneOf(['dense', 'spacious']),\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * The label content.\n   */\n  label: PropTypes.node,\n  /**\n   * Locale for components texts.\n   * Allows overriding texts coming from `LocalizationProvider` and `theme`.\n   */\n  localeText: PropTypes.object,\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Name attribute used by the `input` element in the Field.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value is accepted.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The value that was just accepted.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onAccept: PropTypes.func,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The new value.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see `open`).\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the error associated with the current value changes.\n   * When a validation error is detected, the `error` parameter contains a non-null value.\n   * This can be used to render an appropriate form error.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TError} error The reason why the current value is not valid.\n   * @param {TValue} value The value associated with the error.\n   */\n  onError: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see `open`).\n   */\n  onOpen: PropTypes.func,\n  /**\n   * Callback fired when the selected sections change.\n   * @param {FieldSelectedSections} newValue The new selected sections.\n   */\n  onSelectedSectionsChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * Control the popup or dialog open state.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Force rendering in particular orientation.\n   */\n  orientation: PropTypes.oneOf(['landscape', 'portrait']),\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, disable heavy animations.\n   * @default `@media(prefers-reduced-motion: reduce)` || `navigator.userAgent` matches Android <10 or iOS <13\n   */\n  reduceAnimations: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid date-time using the validation props, except callbacks like `shouldDisable<...>`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * The currently selected sections.\n   * This prop accepts four formats:\n   * 1. If a number is provided, the section at this index will be selected.\n   * 2. If a string of type `FieldSectionType` is provided, the first section with that name will be selected.\n   * 3. If `\"all\"` is provided, all the sections will be selected.\n   * 4. If `null` is provided, no section will be selected.\n   * If not provided, the selected sections will be handled internally.\n   */\n  selectedSections: PropTypes.oneOfType([PropTypes.oneOf(['all', 'day', 'empty', 'hours', 'meridiem', 'minutes', 'month', 'seconds', 'weekDay', 'year']), PropTypes.number]),\n  /**\n   * Disable specific time.\n   * @template TDate\n   * @param {TDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  /**\n   * If `true`, disabled digital clock items will not be rendered.\n   * @default false\n   */\n  skipDisabled: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Amount of time options below or at which the single column time renderer is used.\n   * @default 24\n   */\n  thresholdToRenderTimeInASingleColumn: PropTypes.number,\n  /**\n   * The time steps between two time unit options.\n   * For example, if `timeStep.minutes = 8`, then the available minute options will be `[0, 8, 16, 24, 32, 40, 48, 56]`.\n   * When single column time renderer is used, only `timeStep.minutes` will be used.\n   * @default{ hours: 1, minutes: 5, seconds: 5 }\n   */\n  timeSteps: PropTypes.shape({\n    hours: PropTypes.number,\n    minutes: PropTypes.number,\n    seconds: PropTypes.number\n  }),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Define custom view renderers for each section.\n   * If `null`, the section will only have field editing.\n   * If `undefined`, internally defined view will be used.\n   */\n  viewRenderers: PropTypes.shape({\n    hours: PropTypes.func,\n    meridiem: PropTypes.func,\n    minutes: PropTypes.func,\n    seconds: PropTypes.func\n  }),\n  /**\n   * Available views.\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'minutes', 'seconds']).isRequired)\n} : void 0;\nexport { TimePicker };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,uBAAuB,CAAC;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,gCAAgC,QAAQ,6BAA6B;AAC9E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAG,aAAaV,KAAK,CAACW,UAAU,CAAC,SAASD,UAAUA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACjF,MAAMC,KAAK,GAAGX,aAAa,CAAC;IAC1BW,KAAK,EAAEF,OAAO;IACdG,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFC,qBAAqB,GAAGT;IAC1B,CAAC,GAAGO,KAAK;IACTG,KAAK,GAAGnB,6BAA6B,CAACgB,KAAK,EAAEf,SAAS,CAAC;;EAEzD;EACA,MAAMmB,SAAS,GAAGhB,aAAa,CAACc,qBAAqB,EAAE;IACrDG,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,IAAID,SAAS,EAAE;IACb,OAAO,aAAaT,IAAI,CAACJ,iBAAiB,EAAER,QAAQ,CAAC;MACnDgB,GAAG,EAAEA;IACP,CAAC,EAAEI,KAAK,CAAC,CAAC;EACZ;EACA,OAAO,aAAaR,IAAI,CAACH,gBAAgB,EAAET,QAAQ,CAAC;IAClDgB,GAAG,EAAEA;EACP,CAAC,EAAEI,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGZ,UAAU,CAACa,SAAS,GAAG;EAC7D;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEC,IAAI,EAAEvB,SAAS,CAACwB,IAAI;EACpB;AACF;AACA;AACA;EACEC,WAAW,EAAEzB,SAAS,CAACwB,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEE,SAAS,EAAE1B,SAAS,CAACwB,IAAI;EACzBG,SAAS,EAAE3B,SAAS,CAAC4B,MAAM;EAC3B;AACF;AACA;AACA;EACEC,aAAa,EAAE7B,SAAS,CAACwB,IAAI;EAC7B;AACF;AACA;AACA;EACEM,YAAY,EAAE9B,SAAS,CAAC+B,MAAM;EAC9B;AACF;AACA;AACA;AACA;EACEhB,qBAAqB,EAAEf,SAAS,CAAC4B,MAAM;EACvC;AACF;AACA;AACA;EACEI,QAAQ,EAAEhC,SAAS,CAACwB,IAAI;EACxB;AACF;AACA;AACA;EACES,aAAa,EAAEjC,SAAS,CAACwB,IAAI;EAC7B;AACF;AACA;AACA;EACEU,wCAAwC,EAAElC,SAAS,CAACwB,IAAI;EACxD;AACF;AACA;AACA;EACEW,iBAAiB,EAAEnC,SAAS,CAACwB,IAAI;EACjC;AACF;AACA;AACA;EACEY,WAAW,EAAEpC,SAAS,CAACwB,IAAI;EAC3B;AACF;AACA;EACEa,iCAAiC,EAAErC,SAAS,CAACsC,GAAG;EAChD;AACF;AACA;AACA;EACEC,MAAM,EAAEvC,SAAS,CAAC4B,MAAM;EACxB;AACF;AACA;AACA;AACA;EACEY,aAAa,EAAExC,SAAS,CAACyC,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;EACrD;AACF;AACA;EACEC,QAAQ,EAAEvC,OAAO;EACjB;AACF;AACA;EACEwC,KAAK,EAAE3C,SAAS,CAAC4C,IAAI;EACrB;AACF;AACA;AACA;EACEC,UAAU,EAAE7C,SAAS,CAAC+B,MAAM;EAC5B;AACF;AACA;AACA;EACEe,OAAO,EAAE9C,SAAS,CAAC+B,MAAM;EACzB;AACF;AACA;AACA;EACEgB,OAAO,EAAE/C,SAAS,CAAC+B,MAAM;EACzB;AACF;AACA;AACA;EACEiB,WAAW,EAAEhD,SAAS,CAACiD,MAAM;EAC7B;AACF;AACA;EACEnC,IAAI,EAAEd,SAAS,CAAC4B,MAAM;EACtB;AACF;AACA;AACA;AACA;AACA;AACA;EACEsB,QAAQ,EAAElD,SAAS,CAACmD,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,QAAQ,EAAEpD,SAAS,CAACmD,IAAI;EACxB;AACF;AACA;AACA;EACEE,OAAO,EAAErD,SAAS,CAACmD,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,OAAO,EAAEtD,SAAS,CAACmD,IAAI;EACvB;AACF;AACA;AACA;EACEI,MAAM,EAAEvD,SAAS,CAACmD,IAAI;EACtB;AACF;AACA;AACA;EACEK,wBAAwB,EAAExD,SAAS,CAACmD,IAAI;EACxC;AACF;AACA;AACA;AACA;EACEM,YAAY,EAAEzD,SAAS,CAACmD,IAAI;EAC5B;AACF;AACA;AACA;EACEO,IAAI,EAAE1D,SAAS,CAACwB,IAAI;EACpB;AACF;AACA;AACA;AACA;EACEmC,MAAM,EAAE3D,SAAS,CAACyC,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACpE;AACF;AACA;EACEmB,WAAW,EAAE5D,SAAS,CAACyC,KAAK,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;EACvDoB,QAAQ,EAAE7D,SAAS,CAACwB,IAAI;EACxB;AACF;AACA;AACA;EACEsC,gBAAgB,EAAE9D,SAAS,CAACwB,IAAI;EAChC;AACF;AACA;AACA;EACEuC,aAAa,EAAE/D,SAAS,CAAC+B,MAAM;EAC/B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEiC,gBAAgB,EAAEhE,SAAS,CAACiE,SAAS,CAAC,CAACjE,SAAS,CAACyC,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,EAAEzC,SAAS,CAACiD,MAAM,CAAC,CAAC;EAC1K;AACF;AACA;AACA;AACA;AACA;AACA;EACEiB,iBAAiB,EAAElE,SAAS,CAACmD,IAAI;EACjC;AACF;AACA;AACA;EACEgB,YAAY,EAAEnE,SAAS,CAACwB,IAAI;EAC5B;AACF;AACA;AACA;EACE4C,SAAS,EAAEpE,SAAS,CAAC+B,MAAM;EAC3B;AACF;AACA;AACA;EACEsC,KAAK,EAAErE,SAAS,CAAC+B,MAAM;EACvB;AACF;AACA;EACEuC,EAAE,EAAEtE,SAAS,CAACiE,SAAS,CAAC,CAACjE,SAAS,CAACuE,OAAO,CAACvE,SAAS,CAACiE,SAAS,CAAC,CAACjE,SAAS,CAACmD,IAAI,EAAEnD,SAAS,CAAC+B,MAAM,EAAE/B,SAAS,CAACwB,IAAI,CAAC,CAAC,CAAC,EAAExB,SAAS,CAACmD,IAAI,EAAEnD,SAAS,CAAC+B,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEyC,oCAAoC,EAAExE,SAAS,CAACiD,MAAM;EACtD;AACF;AACA;AACA;AACA;AACA;EACEwB,SAAS,EAAEzE,SAAS,CAAC0E,KAAK,CAAC;IACzBC,KAAK,EAAE3E,SAAS,CAACiD,MAAM;IACvB2B,OAAO,EAAE5E,SAAS,CAACiD,MAAM;IACzB4B,OAAO,EAAE7E,SAAS,CAACiD;EACrB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;EACE6B,QAAQ,EAAE9E,SAAS,CAAC4B,MAAM;EAC1B;AACF;AACA;AACA;EACEmD,KAAK,EAAE/E,SAAS,CAAC+B,MAAM;EACvB;AACF;AACA;AACA;AACA;EACEiD,IAAI,EAAEhF,SAAS,CAACyC,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EAClE;AACF;AACA;AACA;AACA;EACEwC,aAAa,EAAEjF,SAAS,CAAC0E,KAAK,CAAC;IAC7BC,KAAK,EAAE3E,SAAS,CAACmD,IAAI;IACrB+B,QAAQ,EAAElF,SAAS,CAACmD,IAAI;IACxByB,OAAO,EAAE5E,SAAS,CAACmD,IAAI;IACvB0B,OAAO,EAAE7E,SAAS,CAACmD;EACrB,CAAC,CAAC;EACF;AACF;AACA;EACEgC,KAAK,EAAEnF,SAAS,CAACuE,OAAO,CAACvE,SAAS,CAACyC,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC2C,UAAU;AACtF,CAAC,GAAG,KAAK,CAAC;AACV,SAAS3E,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}