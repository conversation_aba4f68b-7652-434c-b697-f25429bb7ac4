{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, '__esModule', {\n  value: true\n});\nexports.default = escapeHTML;\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nfunction escapeHTML(str) {\n  return str.replace(/</g, '&lt;').replace(/>/g, '&gt;');\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "escapeHTML", "str", "replace"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/pretty-format/build/plugins/lib/escapeHTML.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, '__esModule', {\n  value: true\n});\nexports.default = escapeHTML;\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nfunction escapeHTML(str) {\n  return str.replace(/</g, '&lt;').replace(/>/g, '&gt;');\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,UAAU;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAACC,GAAG,EAAE;EACvB,OAAOA,GAAG,CAACC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}