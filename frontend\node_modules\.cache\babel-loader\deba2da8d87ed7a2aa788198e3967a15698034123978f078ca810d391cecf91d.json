{"ast": null, "code": "import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport function getYearCalendarUtilityClass(slot) {\n  return generateUtilityClass('MuiYearCalendar', slot);\n}\nexport const yearCalendarClasses = generateUtilityClasses('MuiYearCalendar', ['root']);", "map": {"version": 3, "names": ["unstable_generateUtilityClass", "generateUtilityClass", "unstable_generateUtilityClasses", "generateUtilityClasses", "getYearCalendarUtilityClass", "slot", "yearCalendarClasses"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/YearCalendar/yearCalendarClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport function getYearCalendarUtilityClass(slot) {\n  return generateUtilityClass('MuiYearCalendar', slot);\n}\nexport const yearCalendarClasses = generateUtilityClasses('MuiYearCalendar', ['root']);"], "mappings": "AAAA,SAASA,6BAA6B,IAAIC,oBAAoB,EAAEC,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AAC7I,OAAO,SAASC,2BAA2BA,CAACC,IAAI,EAAE;EAChD,OAAOJ,oBAAoB,CAAC,iBAAiB,EAAEI,IAAI,CAAC;AACtD;AACA,OAAO,MAAMC,mBAAmB,GAAGH,sBAAsB,CAAC,iBAAiB,EAAE,CAAC,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}