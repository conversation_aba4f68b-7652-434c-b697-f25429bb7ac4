{"ast": null, "code": "export { default } from './elementAcceptingRef';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/node_modules/@mui/utils/esm/elementAcceptingRef/index.js"], "sourcesContent": ["export { default } from './elementAcceptingRef';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}