{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { applyDefaultDate } from \"../utils/date-utils.js\";\nimport { useUtils, useDefaultDates } from \"./useUtils.js\";\nexport const useDefaultizedDateField = props => {\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  return _extends({}, props, {\n    disablePast: props.disablePast ?? false,\n    disableFuture: props.disableFuture ?? false,\n    format: props.format ?? utils.formats.keyboardDate,\n    minDate: applyDefaultDate(utils, props.minDate, defaultDates.minDate),\n    maxDate: applyDefaultDate(utils, props.maxDate, defaultDates.maxDate)\n  });\n};\nexport const useDefaultizedTimeField = props => {\n  const utils = useUtils();\n  const ampm = props.ampm ?? utils.is12HourCycleInCurrentLocale();\n  const defaultFormat = ampm ? utils.formats.fullTime12h : utils.formats.fullTime24h;\n  return _extends({}, props, {\n    disablePast: props.disablePast ?? false,\n    disableFuture: props.disableFuture ?? false,\n    format: props.format ?? defaultFormat\n  });\n};\nexport const useDefaultizedDateTimeField = props => {\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  const ampm = props.ampm ?? utils.is12HourCycleInCurrentLocale();\n  const defaultFormat = ampm ? utils.formats.keyboardDateTime12h : utils.formats.keyboardDateTime24h;\n  return _extends({}, props, {\n    disablePast: props.disablePast ?? false,\n    disableFuture: props.disableFuture ?? false,\n    format: props.format ?? defaultFormat,\n    disableIgnoringDatePartForTimeValidation: Boolean(props.minDateTime || props.maxDateTime),\n    minDate: applyDefaultDate(utils, props.minDateTime ?? props.minDate, defaultDates.minDate),\n    maxDate: applyDefaultDate(utils, props.maxDateTime ?? props.maxDate, defaultDates.maxDate),\n    minTime: props.minDateTime ?? props.minTime,\n    maxTime: props.maxDateTime ?? props.maxTime\n  });\n};", "map": {"version": 3, "names": ["_extends", "applyDefaultDate", "useUtils", "useDefaultDates", "useDefaultizedDateField", "props", "utils", "defaultDates", "disablePast", "disableFuture", "format", "formats", "keyboardDate", "minDate", "maxDate", "useDefaultizedTimeField", "ampm", "is12HourCycleInCurrentLocale", "defaultFormat", "fullTime12h", "fullTime24h", "useDefaultizedDateTimeField", "keyboardDateTime12h", "keyboardDateTime24h", "disableIgnoringDatePartForTimeValidation", "Boolean", "minDateTime", "maxDateTime", "minTime", "maxTime"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/internals/hooks/defaultizedFieldProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { applyDefaultDate } from \"../utils/date-utils.js\";\nimport { useUtils, useDefaultDates } from \"./useUtils.js\";\nexport const useDefaultizedDateField = props => {\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  return _extends({}, props, {\n    disablePast: props.disablePast ?? false,\n    disableFuture: props.disableFuture ?? false,\n    format: props.format ?? utils.formats.keyboardDate,\n    minDate: applyDefaultDate(utils, props.minDate, defaultDates.minDate),\n    maxDate: applyDefaultDate(utils, props.maxDate, defaultDates.maxDate)\n  });\n};\nexport const useDefaultizedTimeField = props => {\n  const utils = useUtils();\n  const ampm = props.ampm ?? utils.is12HourCycleInCurrentLocale();\n  const defaultFormat = ampm ? utils.formats.fullTime12h : utils.formats.fullTime24h;\n  return _extends({}, props, {\n    disablePast: props.disablePast ?? false,\n    disableFuture: props.disableFuture ?? false,\n    format: props.format ?? defaultFormat\n  });\n};\nexport const useDefaultizedDateTimeField = props => {\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  const ampm = props.ampm ?? utils.is12HourCycleInCurrentLocale();\n  const defaultFormat = ampm ? utils.formats.keyboardDateTime12h : utils.formats.keyboardDateTime24h;\n  return _extends({}, props, {\n    disablePast: props.disablePast ?? false,\n    disableFuture: props.disableFuture ?? false,\n    format: props.format ?? defaultFormat,\n    disableIgnoringDatePartForTimeValidation: Boolean(props.minDateTime || props.maxDateTime),\n    minDate: applyDefaultDate(utils, props.minDateTime ?? props.minDate, defaultDates.minDate),\n    maxDate: applyDefaultDate(utils, props.maxDateTime ?? props.maxDate, defaultDates.maxDate),\n    minTime: props.minDateTime ?? props.minTime,\n    maxTime: props.maxDateTime ?? props.maxTime\n  });\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,QAAQ,EAAEC,eAAe,QAAQ,eAAe;AACzD,OAAO,MAAMC,uBAAuB,GAAGC,KAAK,IAAI;EAC9C,MAAMC,KAAK,GAAGJ,QAAQ,CAAC,CAAC;EACxB,MAAMK,YAAY,GAAGJ,eAAe,CAAC,CAAC;EACtC,OAAOH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACzBG,WAAW,EAAEH,KAAK,CAACG,WAAW,IAAI,KAAK;IACvCC,aAAa,EAAEJ,KAAK,CAACI,aAAa,IAAI,KAAK;IAC3CC,MAAM,EAAEL,KAAK,CAACK,MAAM,IAAIJ,KAAK,CAACK,OAAO,CAACC,YAAY;IAClDC,OAAO,EAAEZ,gBAAgB,CAACK,KAAK,EAAED,KAAK,CAACQ,OAAO,EAAEN,YAAY,CAACM,OAAO,CAAC;IACrEC,OAAO,EAAEb,gBAAgB,CAACK,KAAK,EAAED,KAAK,CAACS,OAAO,EAAEP,YAAY,CAACO,OAAO;EACtE,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMC,uBAAuB,GAAGV,KAAK,IAAI;EAC9C,MAAMC,KAAK,GAAGJ,QAAQ,CAAC,CAAC;EACxB,MAAMc,IAAI,GAAGX,KAAK,CAACW,IAAI,IAAIV,KAAK,CAACW,4BAA4B,CAAC,CAAC;EAC/D,MAAMC,aAAa,GAAGF,IAAI,GAAGV,KAAK,CAACK,OAAO,CAACQ,WAAW,GAAGb,KAAK,CAACK,OAAO,CAACS,WAAW;EAClF,OAAOpB,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACzBG,WAAW,EAAEH,KAAK,CAACG,WAAW,IAAI,KAAK;IACvCC,aAAa,EAAEJ,KAAK,CAACI,aAAa,IAAI,KAAK;IAC3CC,MAAM,EAAEL,KAAK,CAACK,MAAM,IAAIQ;EAC1B,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMG,2BAA2B,GAAGhB,KAAK,IAAI;EAClD,MAAMC,KAAK,GAAGJ,QAAQ,CAAC,CAAC;EACxB,MAAMK,YAAY,GAAGJ,eAAe,CAAC,CAAC;EACtC,MAAMa,IAAI,GAAGX,KAAK,CAACW,IAAI,IAAIV,KAAK,CAACW,4BAA4B,CAAC,CAAC;EAC/D,MAAMC,aAAa,GAAGF,IAAI,GAAGV,KAAK,CAACK,OAAO,CAACW,mBAAmB,GAAGhB,KAAK,CAACK,OAAO,CAACY,mBAAmB;EAClG,OAAOvB,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACzBG,WAAW,EAAEH,KAAK,CAACG,WAAW,IAAI,KAAK;IACvCC,aAAa,EAAEJ,KAAK,CAACI,aAAa,IAAI,KAAK;IAC3CC,MAAM,EAAEL,KAAK,CAACK,MAAM,IAAIQ,aAAa;IACrCM,wCAAwC,EAAEC,OAAO,CAACpB,KAAK,CAACqB,WAAW,IAAIrB,KAAK,CAACsB,WAAW,CAAC;IACzFd,OAAO,EAAEZ,gBAAgB,CAACK,KAAK,EAAED,KAAK,CAACqB,WAAW,IAAIrB,KAAK,CAACQ,OAAO,EAAEN,YAAY,CAACM,OAAO,CAAC;IAC1FC,OAAO,EAAEb,gBAAgB,CAACK,KAAK,EAAED,KAAK,CAACsB,WAAW,IAAItB,KAAK,CAACS,OAAO,EAAEP,YAAY,CAACO,OAAO,CAAC;IAC1Fc,OAAO,EAAEvB,KAAK,CAACqB,WAAW,IAAIrB,KAAK,CAACuB,OAAO;IAC3CC,OAAO,EAAExB,KAAK,CAACsB,WAAW,IAAItB,KAAK,CAACwB;EACtC,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}