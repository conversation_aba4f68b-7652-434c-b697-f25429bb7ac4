{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.replaceBehavior = exports.preKeyupBehavior = exports.preKeydownBehavior = exports.postKeyupBehavior = exports.keyupBehavior = exports.keypressBehavior = exports.keydownBehavior = void 0;\nvar _utils = require(\"../../utils\");\nvar arrowKeys = _interopRequireWildcard(require(\"./arrow\"));\nvar controlKeys = _interopRequireWildcard(require(\"./control\"));\nvar characterKeys = _interopRequireWildcard(require(\"./character\"));\nvar functionalKeys = _interopRequireWildcard(require(\"./functional\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function (nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nconst replaceBehavior = [{\n  matches: (keyDef, element) => keyDef.key === 'selectall' && (0, _utils.isElementType)(element, ['input', 'textarea']),\n  handle: (keyDef, element, options, state) => {\n    var _state$carryValue;\n    (0, _utils.setSelectionRange)(element, 0, ((_state$carryValue = state.carryValue) != null ? _state$carryValue : element.value).length);\n  }\n}];\nexports.replaceBehavior = replaceBehavior;\nconst preKeydownBehavior = [...functionalKeys.preKeydownBehavior];\nexports.preKeydownBehavior = preKeydownBehavior;\nconst keydownBehavior = [...arrowKeys.keydownBehavior, ...controlKeys.keydownBehavior, ...functionalKeys.keydownBehavior];\nexports.keydownBehavior = keydownBehavior;\nconst keypressBehavior = [...functionalKeys.keypressBehavior, ...characterKeys.keypressBehavior];\nexports.keypressBehavior = keypressBehavior;\nconst preKeyupBehavior = [...functionalKeys.preKeyupBehavior];\nexports.preKeyupBehavior = preKeyupBehavior;\nconst keyupBehavior = [...functionalKeys.keyupBehavior];\nexports.keyupBehavior = keyupBehavior;\nconst postKeyupBehavior = [...functionalKeys.postKeyupBehavior];\nexports.postKeyupBehavior = postKeyupBehavior;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preKeyupBehavior", "preKeydownBehavior", "postKeyupBehavior", "keyup<PERSON><PERSON><PERSON><PERSON>", "keypressBehavior", "keydownBehavior", "_utils", "require", "arrowKeys", "_interopRequireWildcard", "controlKeys", "<PERSON><PERSON><PERSON><PERSON>", "functional<PERSON>eys", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "matches", "keyDef", "element", "isElementType", "handle", "options", "state", "_state$carryValue", "setSelectionRange", "carryValue", "length"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/user-event/dist/keyboard/plugins/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.replaceBehavior = exports.preKeyupBehavior = exports.preKeydownBehavior = exports.postKeyupBehavior = exports.keyupBehavior = exports.keypressBehavior = exports.keydownBehavior = void 0;\n\nvar _utils = require(\"../../utils\");\n\nvar arrowKeys = _interopRequireWildcard(require(\"./arrow\"));\n\nvar controlKeys = _interopRequireWildcard(require(\"./control\"));\n\nvar characterKeys = _interopRequireWildcard(require(\"./character\"));\n\nvar functionalKeys = _interopRequireWildcard(require(\"./functional\"));\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nconst replaceBehavior = [{\n  matches: (keyDef, element) => keyDef.key === 'selectall' && (0, _utils.isElementType)(element, ['input', 'textarea']),\n  handle: (keyDef, element, options, state) => {\n    var _state$carryValue;\n\n    (0, _utils.setSelectionRange)(element, 0, ((_state$carryValue = state.carryValue) != null ? _state$carryValue : element.value).length);\n  }\n}];\nexports.replaceBehavior = replaceBehavior;\nconst preKeydownBehavior = [...functionalKeys.preKeydownBehavior];\nexports.preKeydownBehavior = preKeydownBehavior;\nconst keydownBehavior = [...arrowKeys.keydownBehavior, ...controlKeys.keydownBehavior, ...functionalKeys.keydownBehavior];\nexports.keydownBehavior = keydownBehavior;\nconst keypressBehavior = [...functionalKeys.keypressBehavior, ...characterKeys.keypressBehavior];\nexports.keypressBehavior = keypressBehavior;\nconst preKeyupBehavior = [...functionalKeys.preKeyupBehavior];\nexports.preKeyupBehavior = preKeyupBehavior;\nconst keyupBehavior = [...functionalKeys.keyupBehavior];\nexports.keyupBehavior = keyupBehavior;\nconst postKeyupBehavior = [...functionalKeys.postKeyupBehavior];\nexports.postKeyupBehavior = postKeyupBehavior;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,eAAe,GAAGF,OAAO,CAACG,gBAAgB,GAAGH,OAAO,CAACI,kBAAkB,GAAGJ,OAAO,CAACK,iBAAiB,GAAGL,OAAO,CAACM,aAAa,GAAGN,OAAO,CAACO,gBAAgB,GAAGP,OAAO,CAACQ,eAAe,GAAG,KAAK,CAAC;AAEjM,IAAIC,MAAM,GAAGC,OAAO,CAAC,aAAa,CAAC;AAEnC,IAAIC,SAAS,GAAGC,uBAAuB,CAACF,OAAO,CAAC,SAAS,CAAC,CAAC;AAE3D,IAAIG,WAAW,GAAGD,uBAAuB,CAACF,OAAO,CAAC,WAAW,CAAC,CAAC;AAE/D,IAAII,aAAa,GAAGF,uBAAuB,CAACF,OAAO,CAAC,aAAa,CAAC,CAAC;AAEnE,IAAIK,cAAc,GAAGH,uBAAuB,CAACF,OAAO,CAAC,cAAc,CAAC,CAAC;AAErE,SAASM,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAAAA,CAAUC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAEtT,SAASL,uBAAuBA,CAACS,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAEE,OAAO,EAAEF;IAAI,CAAC;EAAE;EAAE,IAAIG,KAAK,GAAGR,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIO,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACJ,GAAG,CAAC,EAAE;IAAE,OAAOG,KAAK,CAACE,GAAG,CAACL,GAAG,CAAC;EAAE;EAAE,IAAIM,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAG9B,MAAM,CAACC,cAAc,IAAID,MAAM,CAAC+B,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIT,GAAG,EAAE;IAAE,IAAIS,GAAG,KAAK,SAAS,IAAIhC,MAAM,CAACiC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACZ,GAAG,EAAES,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAG9B,MAAM,CAAC+B,wBAAwB,CAACR,GAAG,EAAES,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAErC,MAAM,CAACC,cAAc,CAAC4B,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGT,GAAG,CAACS,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAACJ,OAAO,GAAGF,GAAG;EAAE,IAAIG,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACd,GAAG,EAAEM,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAEnyB,MAAMzB,eAAe,GAAG,CAAC;EACvBkC,OAAO,EAAEA,CAACC,MAAM,EAAEC,OAAO,KAAKD,MAAM,CAACP,GAAG,KAAK,WAAW,IAAI,CAAC,CAAC,EAAErB,MAAM,CAAC8B,aAAa,EAAED,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;EACrHE,MAAM,EAAEA,CAACH,MAAM,EAAEC,OAAO,EAAEG,OAAO,EAAEC,KAAK,KAAK;IAC3C,IAAIC,iBAAiB;IAErB,CAAC,CAAC,EAAElC,MAAM,CAACmC,iBAAiB,EAAEN,OAAO,EAAE,CAAC,EAAE,CAAC,CAACK,iBAAiB,GAAGD,KAAK,CAACG,UAAU,KAAK,IAAI,GAAGF,iBAAiB,GAAGL,OAAO,CAACrC,KAAK,EAAE6C,MAAM,CAAC;EACxI;AACF,CAAC,CAAC;AACF9C,OAAO,CAACE,eAAe,GAAGA,eAAe;AACzC,MAAME,kBAAkB,GAAG,CAAC,GAAGW,cAAc,CAACX,kBAAkB,CAAC;AACjEJ,OAAO,CAACI,kBAAkB,GAAGA,kBAAkB;AAC/C,MAAMI,eAAe,GAAG,CAAC,GAAGG,SAAS,CAACH,eAAe,EAAE,GAAGK,WAAW,CAACL,eAAe,EAAE,GAAGO,cAAc,CAACP,eAAe,CAAC;AACzHR,OAAO,CAACQ,eAAe,GAAGA,eAAe;AACzC,MAAMD,gBAAgB,GAAG,CAAC,GAAGQ,cAAc,CAACR,gBAAgB,EAAE,GAAGO,aAAa,CAACP,gBAAgB,CAAC;AAChGP,OAAO,CAACO,gBAAgB,GAAGA,gBAAgB;AAC3C,MAAMJ,gBAAgB,GAAG,CAAC,GAAGY,cAAc,CAACZ,gBAAgB,CAAC;AAC7DH,OAAO,CAACG,gBAAgB,GAAGA,gBAAgB;AAC3C,MAAMG,aAAa,GAAG,CAAC,GAAGS,cAAc,CAACT,aAAa,CAAC;AACvDN,OAAO,CAACM,aAAa,GAAGA,aAAa;AACrC,MAAMD,iBAAiB,GAAG,CAAC,GAAGU,cAAc,CAACV,iBAAiB,CAAC;AAC/DL,OAAO,CAACK,iBAAiB,GAAGA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}