{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"props\", \"ref\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/material/styles';\nimport { usePicker } from \"../usePicker/index.js\";\nimport { LocalizationProvider } from \"../../../LocalizationProvider/index.js\";\nimport { PickersLayout } from \"../../../PickersLayout/index.js\";\nimport { DIALOG_WIDTH } from \"../../constants/dimensions.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst PickerStaticLayout = styled(PickersLayout)(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  minWidth: DIALOG_WIDTH,\n  backgroundColor: (theme.vars || theme).palette.background.paper\n}));\n\n/**\n * Hook managing all the single-date static pickers:\n * - StaticDatePicker\n * - StaticDateTimePicker\n * - StaticTimePicker\n */\nexport const useStaticPicker = _ref => {\n  let {\n      props,\n      ref\n    } = _ref,\n    pickerParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const {\n    localeText,\n    slots,\n    slotProps,\n    className,\n    sx,\n    displayStaticWrapperAs,\n    autoFocus\n  } = props;\n  const {\n    layoutProps,\n    renderCurrentView\n  } = usePicker(_extends({}, pickerParams, {\n    props,\n    autoFocusView: autoFocus ?? false,\n    fieldRef: undefined,\n    additionalViewProps: {},\n    wrapperVariant: displayStaticWrapperAs\n  }));\n  const Layout = slots?.layout ?? PickerStaticLayout;\n  const renderPicker = () => /*#__PURE__*/_jsx(LocalizationProvider, {\n    localeText: localeText,\n    children: /*#__PURE__*/_jsx(Layout, _extends({}, layoutProps, slotProps?.layout, {\n      slots: slots,\n      slotProps: slotProps,\n      sx: [...(Array.isArray(sx) ? sx : [sx]), ...(Array.isArray(slotProps?.layout?.sx) ? slotProps.layout.sx : [slotProps?.layout?.sx])],\n      className: clsx(className, slotProps?.layout?.className),\n      ref: ref,\n      children: renderCurrentView()\n    }))\n  });\n  return {\n    renderPicker\n  };\n};", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "clsx", "styled", "usePicker", "LocalizationProvider", "PickersLayout", "DIALOG_WIDTH", "jsx", "_jsx", "PickerStaticLayout", "theme", "overflow", "min<PERSON><PERSON><PERSON>", "backgroundColor", "vars", "palette", "background", "paper", "useStaticPicker", "_ref", "props", "ref", "pickerParams", "localeText", "slots", "slotProps", "className", "sx", "displayStaticWrapperAs", "autoFocus", "layoutProps", "renderCurrentView", "autoFocusView", "fieldRef", "undefined", "additionalViewProps", "wrapperVariant", "Layout", "layout", "renderPicker", "children", "Array", "isArray"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useStaticPicker/useStaticPicker.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"props\", \"ref\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/material/styles';\nimport { usePicker } from \"../usePicker/index.js\";\nimport { LocalizationProvider } from \"../../../LocalizationProvider/index.js\";\nimport { PickersLayout } from \"../../../PickersLayout/index.js\";\nimport { DIALOG_WIDTH } from \"../../constants/dimensions.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst PickerStaticLayout = styled(PickersLayout)(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  minWidth: DIALOG_WIDTH,\n  backgroundColor: (theme.vars || theme).palette.background.paper\n}));\n\n/**\n * Hook managing all the single-date static pickers:\n * - StaticDatePicker\n * - StaticDateTimePicker\n * - StaticTimePicker\n */\nexport const useStaticPicker = _ref => {\n  let {\n      props,\n      ref\n    } = _ref,\n    pickerParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const {\n    localeText,\n    slots,\n    slotProps,\n    className,\n    sx,\n    displayStaticWrapperAs,\n    autoFocus\n  } = props;\n  const {\n    layoutProps,\n    renderCurrentView\n  } = usePicker(_extends({}, pickerParams, {\n    props,\n    autoFocusView: autoFocus ?? false,\n    fieldRef: undefined,\n    additionalViewProps: {},\n    wrapperVariant: displayStaticWrapperAs\n  }));\n  const Layout = slots?.layout ?? PickerStaticLayout;\n  const renderPicker = () => /*#__PURE__*/_jsx(LocalizationProvider, {\n    localeText: localeText,\n    children: /*#__PURE__*/_jsx(Layout, _extends({}, layoutProps, slotProps?.layout, {\n      slots: slots,\n      slotProps: slotProps,\n      sx: [...(Array.isArray(sx) ? sx : [sx]), ...(Array.isArray(slotProps?.layout?.sx) ? slotProps.layout.sx : [slotProps?.layout?.sx])],\n      className: clsx(className, slotProps?.layout?.className),\n      ref: ref,\n      children: renderCurrentView()\n    }))\n  });\n  return {\n    renderPicker\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,kBAAkB,GAAGP,MAAM,CAACG,aAAa,CAAC,CAAC,CAAC;EAChDK;AACF,CAAC,MAAM;EACLC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAEN,YAAY;EACtBO,eAAe,EAAE,CAACH,KAAK,CAACI,IAAI,IAAIJ,KAAK,EAAEK,OAAO,CAACC,UAAU,CAACC;AAC5D,CAAC,CAAC,CAAC;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAGC,IAAI,IAAI;EACrC,IAAI;MACAC,KAAK;MACLC;IACF,CAAC,GAAGF,IAAI;IACRG,YAAY,GAAGxB,6BAA6B,CAACqB,IAAI,EAAEpB,SAAS,CAAC;EAC/D,MAAM;IACJwB,UAAU;IACVC,KAAK;IACLC,SAAS;IACTC,SAAS;IACTC,EAAE;IACFC,sBAAsB;IACtBC;EACF,CAAC,GAAGT,KAAK;EACT,MAAM;IACJU,WAAW;IACXC;EACF,CAAC,GAAG5B,SAAS,CAACN,QAAQ,CAAC,CAAC,CAAC,EAAEyB,YAAY,EAAE;IACvCF,KAAK;IACLY,aAAa,EAAEH,SAAS,IAAI,KAAK;IACjCI,QAAQ,EAAEC,SAAS;IACnBC,mBAAmB,EAAE,CAAC,CAAC;IACvBC,cAAc,EAAER;EAClB,CAAC,CAAC,CAAC;EACH,MAAMS,MAAM,GAAGb,KAAK,EAAEc,MAAM,IAAI7B,kBAAkB;EAClD,MAAM8B,YAAY,GAAGA,CAAA,KAAM,aAAa/B,IAAI,CAACJ,oBAAoB,EAAE;IACjEmB,UAAU,EAAEA,UAAU;IACtBiB,QAAQ,EAAE,aAAahC,IAAI,CAAC6B,MAAM,EAAExC,QAAQ,CAAC,CAAC,CAAC,EAAEiC,WAAW,EAAEL,SAAS,EAAEa,MAAM,EAAE;MAC/Ed,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA,SAAS;MACpBE,EAAE,EAAE,CAAC,IAAIc,KAAK,CAACC,OAAO,CAACf,EAAE,CAAC,GAAGA,EAAE,GAAG,CAACA,EAAE,CAAC,CAAC,EAAE,IAAIc,KAAK,CAACC,OAAO,CAACjB,SAAS,EAAEa,MAAM,EAAEX,EAAE,CAAC,GAAGF,SAAS,CAACa,MAAM,CAACX,EAAE,GAAG,CAACF,SAAS,EAAEa,MAAM,EAAEX,EAAE,CAAC,CAAC,CAAC;MACnID,SAAS,EAAEzB,IAAI,CAACyB,SAAS,EAAED,SAAS,EAAEa,MAAM,EAAEZ,SAAS,CAAC;MACxDL,GAAG,EAAEA,GAAG;MACRmB,QAAQ,EAAET,iBAAiB,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO;IACLQ;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}