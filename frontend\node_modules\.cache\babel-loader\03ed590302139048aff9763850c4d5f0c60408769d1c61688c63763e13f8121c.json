{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\IdeaProjects\\\\FoodOrdering-main\\\\FoodOrdering-main\\\\frontend\\\\src\\\\Components\\\\Home\\\\MultiItemCarousel.jsx\";\nimport React from \"react\";\nimport \"slick-carousel/slick/slick.css\";\nimport \"slick-carousel/slick/slick-theme.css\";\nimport Slider from \"react-slick\";\nimport { topMeels } from \"./TopMeel\";\nimport CarouselItem from \"./CarouselItem\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const MultiItemCarousel = () => {\n  const settings = {\n    dots: true,\n    infinite: true,\n    speed: 500,\n    slidesToShow: 5,\n    slidesToScroll: 1,\n    autoplay: true,\n    autoplaySpeed: 2500,\n    pauseOnHover: true,\n    responsive: [{\n      breakpoint: 1024,\n      settings: {\n        slidesToShow: 4,\n        slidesToScroll: 1\n      }\n    }, {\n      breakpoint: 768,\n      settings: {\n        slidesToShow: 3,\n        slidesToScroll: 1\n      }\n    }, {\n      breakpoint: 480,\n      settings: {\n        slidesToShow: 2,\n        slidesToScroll: 1\n      }\n    }]\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"carousel-container\",\n    children: /*#__PURE__*/_jsxDEV(Slider, {\n      ...settings,\n      children: topMeels.map((item, index) => /*#__PURE__*/_jsxDEV(CarouselItem, {\n        image: item.image,\n        title: item.title\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 9\n  }, this);\n};\n_c = MultiItemCarousel;\nexport default MultiItemCarousel;\nvar _c;\n$RefreshReg$(_c, \"MultiItemCarousel\");", "map": {"version": 3, "names": ["React", "Slide<PERSON>", "topMeels", "CarouselItem", "jsxDEV", "_jsxDEV", "MultiItemCarousel", "settings", "dots", "infinite", "speed", "slidesToShow", "slidesToScroll", "autoplay", "autoplaySpeed", "pauseOnHover", "responsive", "breakpoint", "className", "children", "map", "item", "index", "image", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/src/Components/Home/MultiItemCarousel.jsx"], "sourcesContent": ["import React from \"react\"\nimport \"slick-carousel/slick/slick.css\";\nimport \"slick-carousel/slick/slick-theme.css\";\nimport Slider from \"react-slick\";\nimport { topMeels } from \"./TopMeel\";\nimport CarouselItem from \"./CarouselItem\";\n\nexport const MultiItemCarousel = () => {\n    const settings = {\n        dots: true,\n        infinite: true,\n        speed: 500,\n        slidesToShow: 5,\n        slidesToScroll: 1,\n        autoplay: true,\n        autoplaySpeed: 2500,\n        pauseOnHover: true,\n        responsive: [\n            {\n                breakpoint: 1024,\n                settings: {\n                    slidesToShow: 4,\n                    slidesToScroll: 1,\n                }\n            },\n            {\n                breakpoint: 768,\n                settings: {\n                    slidesToShow: 3,\n                    slidesToScroll: 1,\n                }\n            },\n            {\n                breakpoint: 480,\n                settings: {\n                    slidesToShow: 2,\n                    slidesToScroll: 1,\n                }\n            }\n        ]\n    };\n\n    return(\n        <div className=\"carousel-container\">\n            <Slider {...settings}>\n                {topMeels.map((item, index) => (\n                    <CarouselItem\n                        key={index}\n                        image={item.image}\n                        title={item.title}\n                    />\n                ))}\n            </Slider>\n        </div>\n    )\n}\n\nexport default MultiItemCarousel"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,gCAAgC;AACvC,OAAO,sCAAsC;AAC7C,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,QAAQ,QAAQ,WAAW;AACpC,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,OAAO,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EACnC,MAAMC,QAAQ,GAAG;IACbC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE,GAAG;IACVC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,QAAQ,EAAE,IAAI;IACdC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,CACR;MACIC,UAAU,EAAE,IAAI;MAChBV,QAAQ,EAAE;QACNI,YAAY,EAAE,CAAC;QACfC,cAAc,EAAE;MACpB;IACJ,CAAC,EACD;MACIK,UAAU,EAAE,GAAG;MACfV,QAAQ,EAAE;QACNI,YAAY,EAAE,CAAC;QACfC,cAAc,EAAE;MACpB;IACJ,CAAC,EACD;MACIK,UAAU,EAAE,GAAG;MACfV,QAAQ,EAAE;QACNI,YAAY,EAAE,CAAC;QACfC,cAAc,EAAE;MACpB;IACJ,CAAC;EAET,CAAC;EAED,oBACIP,OAAA;IAAKa,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eAC/Bd,OAAA,CAACJ,MAAM;MAAA,GAAKM,QAAQ;MAAAY,QAAA,EACfjB,QAAQ,CAACkB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACtBjB,OAAA,CAACF,YAAY;QAEToB,KAAK,EAAEF,IAAI,CAACE,KAAM;QAClBC,KAAK,EAAEH,IAAI,CAACG;MAAM,GAFbF,KAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGb,CACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEd,CAAC;AAAAC,EAAA,GAhDYvB,iBAAiB;AAkD9B,eAAeA,iBAAiB;AAAA,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}