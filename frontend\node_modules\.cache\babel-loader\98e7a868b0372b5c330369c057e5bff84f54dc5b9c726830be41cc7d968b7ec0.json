{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar defaultProps = {\n  accessibility: true,\n  adaptiveHeight: false,\n  afterChange: null,\n  appendDots: function appendDots(dots) {\n    return /*#__PURE__*/_react[\"default\"].createElement(\"ul\", {\n      style: {\n        display: \"block\"\n      }\n    }, dots);\n  },\n  arrows: true,\n  autoplay: false,\n  autoplaySpeed: 3000,\n  beforeChange: null,\n  centerMode: false,\n  centerPadding: \"50px\",\n  className: \"\",\n  cssEase: \"ease\",\n  customPaging: function customPaging(i) {\n    return /*#__PURE__*/_react[\"default\"].createElement(\"button\", null, i + 1);\n  },\n  dots: false,\n  dotsClass: \"slick-dots\",\n  draggable: true,\n  easing: \"linear\",\n  edgeFriction: 0.35,\n  fade: false,\n  focusOnSelect: false,\n  infinite: true,\n  initialSlide: 0,\n  lazyLoad: null,\n  nextArrow: null,\n  onEdge: null,\n  onInit: null,\n  onLazyLoadError: null,\n  onReInit: null,\n  pauseOnDotsHover: false,\n  pauseOnFocus: false,\n  pauseOnHover: true,\n  prevArrow: null,\n  responsive: null,\n  rows: 1,\n  rtl: false,\n  slide: \"div\",\n  slidesPerRow: 1,\n  slidesToScroll: 1,\n  slidesToShow: 1,\n  speed: 500,\n  swipe: true,\n  swipeEvent: null,\n  swipeToSlide: false,\n  touchMove: true,\n  touchThreshold: 5,\n  useCSS: true,\n  useTransform: true,\n  variableWidth: false,\n  vertical: false,\n  waitForAnimate: true,\n  asNavFor: null\n};\nvar _default = exports[\"default\"] = defaultProps;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_react", "_interopRequireDefault", "require", "obj", "__esModule", "defaultProps", "accessibility", "adaptiveHeight", "afterChange", "appendDots", "dots", "createElement", "style", "display", "arrows", "autoplay", "autoplaySpeed", "beforeChange", "centerMode", "centerPadding", "className", "cssEase", "customPaging", "i", "dotsClass", "draggable", "easing", "edgeFriction", "fade", "focusOnSelect", "infinite", "initialSlide", "lazyLoad", "nextArrow", "onEdge", "onInit", "onLazyLoadError", "onReInit", "pauseOnDotsHover", "pauseOnFocus", "pauseOnHover", "prevArrow", "responsive", "rows", "rtl", "slide", "slidesPerRow", "slidesToScroll", "slidesToShow", "speed", "swipe", "swipeEvent", "swipeToSlide", "touchMove", "touchThreshold", "useCSS", "useTransform", "variableWidth", "vertical", "waitForAnimate", "asNavFor", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/react-slick/lib/default-props.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nvar defaultProps = {\n  accessibility: true,\n  adaptiveHeight: false,\n  afterChange: null,\n  appendDots: function appendDots(dots) {\n    return /*#__PURE__*/_react[\"default\"].createElement(\"ul\", {\n      style: {\n        display: \"block\"\n      }\n    }, dots);\n  },\n  arrows: true,\n  autoplay: false,\n  autoplaySpeed: 3000,\n  beforeChange: null,\n  centerMode: false,\n  centerPadding: \"50px\",\n  className: \"\",\n  cssEase: \"ease\",\n  customPaging: function customPaging(i) {\n    return /*#__PURE__*/_react[\"default\"].createElement(\"button\", null, i + 1);\n  },\n  dots: false,\n  dotsClass: \"slick-dots\",\n  draggable: true,\n  easing: \"linear\",\n  edgeFriction: 0.35,\n  fade: false,\n  focusOnSelect: false,\n  infinite: true,\n  initialSlide: 0,\n  lazyLoad: null,\n  nextArrow: null,\n  onEdge: null,\n  onInit: null,\n  onLazyLoadError: null,\n  onReInit: null,\n  pauseOnDotsHover: false,\n  pauseOnFocus: false,\n  pauseOnHover: true,\n  prevArrow: null,\n  responsive: null,\n  rows: 1,\n  rtl: false,\n  slide: \"div\",\n  slidesPerRow: 1,\n  slidesToScroll: 1,\n  slidesToShow: 1,\n  speed: 500,\n  swipe: true,\n  swipeEvent: null,\n  swipeToSlide: false,\n  touchMove: true,\n  touchThreshold: 5,\n  useCSS: true,\n  useTransform: true,\n  variableWidth: false,\n  vertical: false,\n  waitForAnimate: true,\n  asNavFor: null\n};\nvar _default = exports[\"default\"] = defaultProps;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAC3B,IAAIE,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAChG,IAAIE,YAAY,GAAG;EACjBC,aAAa,EAAE,IAAI;EACnBC,cAAc,EAAE,KAAK;EACrBC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE,SAASA,UAAUA,CAACC,IAAI,EAAE;IACpC,OAAO,aAAaV,MAAM,CAAC,SAAS,CAAC,CAACW,aAAa,CAAC,IAAI,EAAE;MACxDC,KAAK,EAAE;QACLC,OAAO,EAAE;MACX;IACF,CAAC,EAAEH,IAAI,CAAC;EACV,CAAC;EACDI,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,KAAK;EACfC,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,KAAK;EACjBC,aAAa,EAAE,MAAM;EACrBC,SAAS,EAAE,EAAE;EACbC,OAAO,EAAE,MAAM;EACfC,YAAY,EAAE,SAASA,YAAYA,CAACC,CAAC,EAAE;IACrC,OAAO,aAAavB,MAAM,CAAC,SAAS,CAAC,CAACW,aAAa,CAAC,QAAQ,EAAE,IAAI,EAAEY,CAAC,GAAG,CAAC,CAAC;EAC5E,CAAC;EACDb,IAAI,EAAE,KAAK;EACXc,SAAS,EAAE,YAAY;EACvBC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,QAAQ;EAChBC,YAAY,EAAE,IAAI;EAClBC,IAAI,EAAE,KAAK;EACXC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,IAAI;EACdC,YAAY,EAAE,CAAC;EACfC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,eAAe,EAAE,IAAI;EACrBC,QAAQ,EAAE,IAAI;EACdC,gBAAgB,EAAE,KAAK;EACvBC,YAAY,EAAE,KAAK;EACnBC,YAAY,EAAE,IAAI;EAClBC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,IAAI;EAChBC,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE,KAAK;EACVC,KAAK,EAAE,KAAK;EACZC,YAAY,EAAE,CAAC;EACfC,cAAc,EAAE,CAAC;EACjBC,YAAY,EAAE,CAAC;EACfC,KAAK,EAAE,GAAG;EACVC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,KAAK;EACnBC,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE,CAAC;EACjBC,MAAM,EAAE,IAAI;EACZC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,KAAK;EACfC,cAAc,EAAE,IAAI;EACpBC,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIC,QAAQ,GAAG/D,OAAO,CAAC,SAAS,CAAC,GAAGO,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}