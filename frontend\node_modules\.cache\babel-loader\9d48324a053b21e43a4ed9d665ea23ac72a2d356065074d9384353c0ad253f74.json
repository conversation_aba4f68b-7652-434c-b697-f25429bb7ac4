{"ast": null, "code": "'use strict';\n\nmodule.exports = ({\n  onlyFirst = false\n} = {}) => {\n  const pattern = ['[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)', '(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-ntqry=><~]))'].join('|');\n  return new RegExp(pattern, onlyFirst ? undefined : 'g');\n};", "map": {"version": 3, "names": ["module", "exports", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "join", "RegExp", "undefined"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/ansi-regex/index.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = ({onlyFirst = false} = {}) => {\n\tconst pattern = [\n\t\t'[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)',\n\t\t'(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-ntqry=><~]))'\n\t].join('|');\n\n\treturn new RegExp(pattern, onlyFirst ? undefined : 'g');\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,CAAC;EAACC,SAAS,GAAG;AAAK,CAAC,GAAG,CAAC,CAAC,KAAK;EAC9C,MAAMC,OAAO,GAAG,CACf,8HAA8H,EAC9H,0DAA0D,CAC1D,CAACC,IAAI,CAAC,GAAG,CAAC;EAEX,OAAO,IAAIC,MAAM,CAACF,OAAO,EAAED,SAAS,GAAGI,SAAS,GAAG,GAAG,CAAC;AACxD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}