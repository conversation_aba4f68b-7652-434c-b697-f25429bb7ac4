{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar documentRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'Device Independence Delivery Unit'\n    }\n  }, {\n    concept: {\n      name: 'html'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure']]\n};\nvar _default = documentRole;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "documentRole", "abstract", "accessibleNameRequired", "baseConcepts", "childrenPresentational", "nameFrom", "prohibitedProps", "props", "relatedConcepts", "concept", "name", "module", "requireContextRole", "requiredContextRole", "requiredOwnedElements", "requiredProps", "superClass", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/etc/roles/literal/documentRole.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar documentRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'Device Independence Delivery Unit'\n    }\n  }, {\n    concept: {\n      name: 'html'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure']]\n};\nvar _default = documentRole;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,YAAY,GAAG;EACjBC,QAAQ,EAAE,KAAK;EACfC,sBAAsB,EAAE,KAAK;EAC7BC,YAAY,EAAE,EAAE;EAChBC,sBAAsB,EAAE,KAAK;EAC7BC,QAAQ,EAAE,CAAC,QAAQ,CAAC;EACpBC,eAAe,EAAE,EAAE;EACnBC,KAAK,EAAE,CAAC,CAAC;EACTC,eAAe,EAAE,CAAC;IAChBC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR;EACF,CAAC,EAAE;IACDD,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,CAAC;EACFC,kBAAkB,EAAE,EAAE;EACtBC,mBAAmB,EAAE,EAAE;EACvBC,qBAAqB,EAAE,EAAE;EACzBC,aAAa,EAAE,CAAC,CAAC;EACjBC,UAAU,EAAE,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC;AACxC,CAAC;AACD,IAAIC,QAAQ,GAAGjB,YAAY;AAC3BH,OAAO,CAACE,OAAO,GAAGkB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}