{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useThemeProps } from '@mui/material/styles';\nimport { useDefaultDates, useUtils } from \"../internals/hooks/useUtils.js\";\nimport { applyDefaultViewProps } from \"../internals/utils/views.js\";\nimport { applyDefaultDate } from \"../internals/utils/date-utils.js\";\nimport { DatePickerToolbar } from \"./DatePickerToolbar.js\";\nexport function useDatePickerDefaultizedProps(props, name) {\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  const localeText = React.useMemo(() => {\n    if (themeProps.localeText?.toolbarTitle == null) {\n      return themeProps.localeText;\n    }\n    return _extends({}, themeProps.localeText, {\n      datePickerToolbarTitle: themeProps.localeText.toolbarTitle\n    });\n  }, [themeProps.localeText]);\n  return _extends({}, themeProps, {\n    localeText\n  }, applyDefaultViewProps({\n    views: themeProps.views,\n    openTo: themeProps.openTo,\n    defaultViews: ['year', 'day'],\n    defaultOpenTo: 'day'\n  }), {\n    disableFuture: themeProps.disableFuture ?? false,\n    disablePast: themeProps.disablePast ?? false,\n    minDate: applyDefaultDate(utils, themeProps.minDate, defaultDates.minDate),\n    maxDate: applyDefaultDate(utils, themeProps.maxDate, defaultDates.maxDate),\n    slots: _extends({\n      toolbar: DatePickerToolbar\n    }, themeProps.slots)\n  });\n}", "map": {"version": 3, "names": ["_extends", "React", "useThemeProps", "useDefaultDates", "useUtils", "applyDefaultViewProps", "applyDefaultDate", "DatePickerToolbar", "useDatePickerDefaultizedProps", "props", "name", "utils", "defaultDates", "themeProps", "localeText", "useMemo", "toolbarTitle", "datePickerToolbarTitle", "views", "openTo", "defaultViews", "defaultOpenTo", "disableFuture", "disablePast", "minDate", "maxDate", "slots", "toolbar"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/DatePicker/shared.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useThemeProps } from '@mui/material/styles';\nimport { useDefaultDates, useUtils } from \"../internals/hooks/useUtils.js\";\nimport { applyDefaultViewProps } from \"../internals/utils/views.js\";\nimport { applyDefaultDate } from \"../internals/utils/date-utils.js\";\nimport { DatePickerToolbar } from \"./DatePickerToolbar.js\";\nexport function useDatePickerDefaultizedProps(props, name) {\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  const localeText = React.useMemo(() => {\n    if (themeProps.localeText?.toolbarTitle == null) {\n      return themeProps.localeText;\n    }\n    return _extends({}, themeProps.localeText, {\n      datePickerToolbarTitle: themeProps.localeText.toolbarTitle\n    });\n  }, [themeProps.localeText]);\n  return _extends({}, themeProps, {\n    localeText\n  }, applyDefaultViewProps({\n    views: themeProps.views,\n    openTo: themeProps.openTo,\n    defaultViews: ['year', 'day'],\n    defaultOpenTo: 'day'\n  }), {\n    disableFuture: themeProps.disableFuture ?? false,\n    disablePast: themeProps.disablePast ?? false,\n    minDate: applyDefaultDate(utils, themeProps.minDate, defaultDates.minDate),\n    maxDate: applyDefaultDate(utils, themeProps.maxDate, defaultDates.maxDate),\n    slots: _extends({\n      toolbar: DatePickerToolbar\n    }, themeProps.slots)\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,eAAe,EAAEC,QAAQ,QAAQ,gCAAgC;AAC1E,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,OAAO,SAASC,6BAA6BA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACzD,MAAMC,KAAK,GAAGP,QAAQ,CAAC,CAAC;EACxB,MAAMQ,YAAY,GAAGT,eAAe,CAAC,CAAC;EACtC,MAAMU,UAAU,GAAGX,aAAa,CAAC;IAC/BO,KAAK;IACLC;EACF,CAAC,CAAC;EACF,MAAMI,UAAU,GAAGb,KAAK,CAACc,OAAO,CAAC,MAAM;IACrC,IAAIF,UAAU,CAACC,UAAU,EAAEE,YAAY,IAAI,IAAI,EAAE;MAC/C,OAAOH,UAAU,CAACC,UAAU;IAC9B;IACA,OAAOd,QAAQ,CAAC,CAAC,CAAC,EAAEa,UAAU,CAACC,UAAU,EAAE;MACzCG,sBAAsB,EAAEJ,UAAU,CAACC,UAAU,CAACE;IAChD,CAAC,CAAC;EACJ,CAAC,EAAE,CAACH,UAAU,CAACC,UAAU,CAAC,CAAC;EAC3B,OAAOd,QAAQ,CAAC,CAAC,CAAC,EAAEa,UAAU,EAAE;IAC9BC;EACF,CAAC,EAAET,qBAAqB,CAAC;IACvBa,KAAK,EAAEL,UAAU,CAACK,KAAK;IACvBC,MAAM,EAAEN,UAAU,CAACM,MAAM;IACzBC,YAAY,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;IAC7BC,aAAa,EAAE;EACjB,CAAC,CAAC,EAAE;IACFC,aAAa,EAAET,UAAU,CAACS,aAAa,IAAI,KAAK;IAChDC,WAAW,EAAEV,UAAU,CAACU,WAAW,IAAI,KAAK;IAC5CC,OAAO,EAAElB,gBAAgB,CAACK,KAAK,EAAEE,UAAU,CAACW,OAAO,EAAEZ,YAAY,CAACY,OAAO,CAAC;IAC1EC,OAAO,EAAEnB,gBAAgB,CAACK,KAAK,EAAEE,UAAU,CAACY,OAAO,EAAEb,YAAY,CAACa,OAAO,CAAC;IAC1EC,KAAK,EAAE1B,QAAQ,CAAC;MACd2B,OAAO,EAAEpB;IACX,CAAC,EAAEM,UAAU,CAACa,KAAK;EACrB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}