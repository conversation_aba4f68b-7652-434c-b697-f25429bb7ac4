{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport { refType } from '@mui/utils';\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { TimeField } from \"../TimeField/index.js\";\nimport { useTimePickerDefaultizedProps } from \"../TimePicker/shared.js\";\nimport { usePickersTranslations } from \"../hooks/usePickersTranslations.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { extractValidationProps, validateTime } from \"../validation/index.js\";\nimport { useMobilePicker } from \"../internals/hooks/useMobilePicker/index.js\";\nimport { renderTimeViewClock } from \"../timeViewRenderers/index.js\";\nimport { resolveTimeFormat } from \"../internals/utils/time-utils.js\";\nimport { buildGetOpenDialogAriaText } from \"../locales/utils/getPickersLocalization.js\";\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [Validation](https://mui.com/x/react-date-pickers/validation/)\n *\n * API:\n *\n * - [MobileTimePicker API](https://mui.com/x/api/date-pickers/mobile-time-picker/)\n */\nconst MobileTimePicker = /*#__PURE__*/React.forwardRef(function MobileTimePicker(inProps, ref) {\n  const translations = usePickersTranslations();\n  const utils = useUtils();\n\n  // Props with the default values common to all time pickers\n  const defaultizedProps = useTimePickerDefaultizedProps(inProps, 'MuiMobileTimePicker');\n  const viewRenderers = _extends({\n    hours: renderTimeViewClock,\n    minutes: renderTimeViewClock,\n    seconds: renderTimeViewClock\n  }, defaultizedProps.viewRenderers);\n  const ampmInClock = defaultizedProps.ampmInClock ?? false;\n\n  // Props with the default values specific to the mobile variant\n  const props = _extends({}, defaultizedProps, {\n    ampmInClock,\n    viewRenderers,\n    format: resolveTimeFormat(utils, defaultizedProps),\n    slots: _extends({\n      field: TimeField\n    }, defaultizedProps.slots),\n    slotProps: _extends({}, defaultizedProps.slotProps, {\n      field: ownerState => _extends({}, resolveComponentProps(defaultizedProps.slotProps?.field, ownerState), extractValidationProps(defaultizedProps), {\n        ref\n      }),\n      toolbar: _extends({\n        hidden: false,\n        ampmInClock\n      }, defaultizedProps.slotProps?.toolbar)\n    })\n  });\n  const {\n    renderPicker\n  } = useMobilePicker({\n    props,\n    valueManager: singleItemValueManager,\n    valueType: 'time',\n    getOpenDialogAriaText: buildGetOpenDialogAriaText({\n      utils,\n      formatKey: 'fullTime',\n      contextTranslation: translations.openTimePickerDialogue,\n      propsTranslation: props.localeText?.openTimePickerDialogue\n    }),\n    validator: validateTime\n  });\n  return renderPicker();\n});\nMobileTimePicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * Display ampm controls under the clock (instead of in the toolbar).\n   * @default true on desktop, false on mobile\n   */\n  ampmInClock: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  className: PropTypes.string,\n  /**\n   * If `true`, the popover or modal will close after submitting the full date.\n   * @default `true` for desktop, `false` for mobile (based on the chosen wrapper and `desktopModeMediaQuery` prop).\n   */\n  closeOnSelect: PropTypes.bool,\n  /**\n   * The default value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the picker and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, the open picker button will not be rendered (renders only the field).\n   * @default false\n   */\n  disableOpenPicker: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * @default false\n   */\n  enableAccessibleFieldDOMStructure: PropTypes.any,\n  /**\n   * Format of the date when rendered in the input(s).\n   * Defaults to localized format based on the used `views`.\n   */\n  format: PropTypes.string,\n  /**\n   * Density of the format when rendered in the input.\n   * Setting `formatDensity` to `\"spacious\"` will add a space before and after each `/`, `-` and `.` character.\n   * @default \"dense\"\n   */\n  formatDensity: PropTypes.oneOf(['dense', 'spacious']),\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * The label content.\n   */\n  label: PropTypes.node,\n  /**\n   * Locale for components texts.\n   * Allows overriding texts coming from `LocalizationProvider` and `theme`.\n   */\n  localeText: PropTypes.object,\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Name attribute used by the `input` element in the Field.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value is accepted.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The value that was just accepted.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onAccept: PropTypes.func,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The new value.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see `open`).\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the error associated with the current value changes.\n   * When a validation error is detected, the `error` parameter contains a non-null value.\n   * This can be used to render an appropriate form error.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TError} error The reason why the current value is not valid.\n   * @param {TValue} value The value associated with the error.\n   */\n  onError: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see `open`).\n   */\n  onOpen: PropTypes.func,\n  /**\n   * Callback fired when the selected sections change.\n   * @param {FieldSelectedSections} newValue The new selected sections.\n   */\n  onSelectedSectionsChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * Control the popup or dialog open state.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * Force rendering in particular orientation.\n   */\n  orientation: PropTypes.oneOf(['landscape', 'portrait']),\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, disable heavy animations.\n   * @default `@media(prefers-reduced-motion: reduce)` || `navigator.userAgent` matches Android <10 or iOS <13\n   */\n  reduceAnimations: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid date-time using the validation props, except callbacks like `shouldDisable<...>`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * The currently selected sections.\n   * This prop accepts four formats:\n   * 1. If a number is provided, the section at this index will be selected.\n   * 2. If a string of type `FieldSectionType` is provided, the first section with that name will be selected.\n   * 3. If `\"all\"` is provided, all the sections will be selected.\n   * 4. If `null` is provided, no section will be selected.\n   * If not provided, the selected sections will be handled internally.\n   */\n  selectedSections: PropTypes.oneOfType([PropTypes.oneOf(['all', 'day', 'empty', 'hours', 'meridiem', 'minutes', 'month', 'seconds', 'weekDay', 'year']), PropTypes.number]),\n  /**\n   * Disable specific time.\n   * @template TDate\n   * @param {TDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * Define custom view renderers for each section.\n   * If `null`, the section will only have field editing.\n   * If `undefined`, internally defined view will be used.\n   */\n  viewRenderers: PropTypes.shape({\n    hours: PropTypes.func,\n    minutes: PropTypes.func,\n    seconds: PropTypes.func\n  }),\n  /**\n   * Available views.\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'minutes', 'seconds']).isRequired)\n};\nexport { MobileTimePicker };", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "resolveComponentProps", "refType", "singleItemValueManager", "TimeField", "useTimePickerDefaultizedProps", "usePickersTranslations", "useUtils", "extractValidationProps", "validateTime", "useMobilePicker", "renderTimeViewClock", "resolveTimeFormat", "buildGetOpenDialogAriaText", "MobileTimePicker", "forwardRef", "inProps", "ref", "translations", "utils", "defaultizedProps", "viewRenderers", "hours", "minutes", "seconds", "ampmInClock", "props", "format", "slots", "field", "slotProps", "ownerState", "toolbar", "hidden", "renderPicker", "valueManager", "valueType", "getOpenDialogAriaText", "formatKey", "contextTranslation", "openTimePickerDialogue", "propsTranslation", "localeText", "validator", "propTypes", "ampm", "bool", "autoFocus", "className", "string", "closeOnSelect", "defaultValue", "object", "disabled", "disableFuture", "disableIgnoringDatePartForTimeValidation", "disableOpenPicker", "disablePast", "enableAccessibleFieldDOMStructure", "any", "formatDensity", "oneOf", "inputRef", "label", "node", "maxTime", "minTime", "minutesStep", "number", "name", "onAccept", "func", "onChange", "onClose", "onError", "onOpen", "onSelectedSectionsChange", "onViewChange", "open", "openTo", "orientation", "readOnly", "reduceAnimations", "referenceDate", "selectedSections", "oneOfType", "shouldDisableTime", "sx", "arrayOf", "timezone", "value", "view", "shape", "views", "isRequired"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/MobileTimePicker/MobileTimePicker.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport { refType } from '@mui/utils';\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { TimeField } from \"../TimeField/index.js\";\nimport { useTimePickerDefaultizedProps } from \"../TimePicker/shared.js\";\nimport { usePickersTranslations } from \"../hooks/usePickersTranslations.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { extractValidationProps, validateTime } from \"../validation/index.js\";\nimport { useMobilePicker } from \"../internals/hooks/useMobilePicker/index.js\";\nimport { renderTimeViewClock } from \"../timeViewRenderers/index.js\";\nimport { resolveTimeFormat } from \"../internals/utils/time-utils.js\";\nimport { buildGetOpenDialogAriaText } from \"../locales/utils/getPickersLocalization.js\";\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [Validation](https://mui.com/x/react-date-pickers/validation/)\n *\n * API:\n *\n * - [MobileTimePicker API](https://mui.com/x/api/date-pickers/mobile-time-picker/)\n */\nconst MobileTimePicker = /*#__PURE__*/React.forwardRef(function MobileTimePicker(inProps, ref) {\n  const translations = usePickersTranslations();\n  const utils = useUtils();\n\n  // Props with the default values common to all time pickers\n  const defaultizedProps = useTimePickerDefaultizedProps(inProps, 'MuiMobileTimePicker');\n  const viewRenderers = _extends({\n    hours: renderTimeViewClock,\n    minutes: renderTimeViewClock,\n    seconds: renderTimeViewClock\n  }, defaultizedProps.viewRenderers);\n  const ampmInClock = defaultizedProps.ampmInClock ?? false;\n\n  // Props with the default values specific to the mobile variant\n  const props = _extends({}, defaultizedProps, {\n    ampmInClock,\n    viewRenderers,\n    format: resolveTimeFormat(utils, defaultizedProps),\n    slots: _extends({\n      field: TimeField\n    }, defaultizedProps.slots),\n    slotProps: _extends({}, defaultizedProps.slotProps, {\n      field: ownerState => _extends({}, resolveComponentProps(defaultizedProps.slotProps?.field, ownerState), extractValidationProps(defaultizedProps), {\n        ref\n      }),\n      toolbar: _extends({\n        hidden: false,\n        ampmInClock\n      }, defaultizedProps.slotProps?.toolbar)\n    })\n  });\n  const {\n    renderPicker\n  } = useMobilePicker({\n    props,\n    valueManager: singleItemValueManager,\n    valueType: 'time',\n    getOpenDialogAriaText: buildGetOpenDialogAriaText({\n      utils,\n      formatKey: 'fullTime',\n      contextTranslation: translations.openTimePickerDialogue,\n      propsTranslation: props.localeText?.openTimePickerDialogue\n    }),\n    validator: validateTime\n  });\n  return renderPicker();\n});\nMobileTimePicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * Display ampm controls under the clock (instead of in the toolbar).\n   * @default true on desktop, false on mobile\n   */\n  ampmInClock: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  className: PropTypes.string,\n  /**\n   * If `true`, the popover or modal will close after submitting the full date.\n   * @default `true` for desktop, `false` for mobile (based on the chosen wrapper and `desktopModeMediaQuery` prop).\n   */\n  closeOnSelect: PropTypes.bool,\n  /**\n   * The default value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the picker and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, the open picker button will not be rendered (renders only the field).\n   * @default false\n   */\n  disableOpenPicker: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * @default false\n   */\n  enableAccessibleFieldDOMStructure: PropTypes.any,\n  /**\n   * Format of the date when rendered in the input(s).\n   * Defaults to localized format based on the used `views`.\n   */\n  format: PropTypes.string,\n  /**\n   * Density of the format when rendered in the input.\n   * Setting `formatDensity` to `\"spacious\"` will add a space before and after each `/`, `-` and `.` character.\n   * @default \"dense\"\n   */\n  formatDensity: PropTypes.oneOf(['dense', 'spacious']),\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * The label content.\n   */\n  label: PropTypes.node,\n  /**\n   * Locale for components texts.\n   * Allows overriding texts coming from `LocalizationProvider` and `theme`.\n   */\n  localeText: PropTypes.object,\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Name attribute used by the `input` element in the Field.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value is accepted.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The value that was just accepted.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onAccept: PropTypes.func,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The new value.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see `open`).\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the error associated with the current value changes.\n   * When a validation error is detected, the `error` parameter contains a non-null value.\n   * This can be used to render an appropriate form error.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TError} error The reason why the current value is not valid.\n   * @param {TValue} value The value associated with the error.\n   */\n  onError: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see `open`).\n   */\n  onOpen: PropTypes.func,\n  /**\n   * Callback fired when the selected sections change.\n   * @param {FieldSelectedSections} newValue The new selected sections.\n   */\n  onSelectedSectionsChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * Control the popup or dialog open state.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * Force rendering in particular orientation.\n   */\n  orientation: PropTypes.oneOf(['landscape', 'portrait']),\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, disable heavy animations.\n   * @default `@media(prefers-reduced-motion: reduce)` || `navigator.userAgent` matches Android <10 or iOS <13\n   */\n  reduceAnimations: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid date-time using the validation props, except callbacks like `shouldDisable<...>`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * The currently selected sections.\n   * This prop accepts four formats:\n   * 1. If a number is provided, the section at this index will be selected.\n   * 2. If a string of type `FieldSectionType` is provided, the first section with that name will be selected.\n   * 3. If `\"all\"` is provided, all the sections will be selected.\n   * 4. If `null` is provided, no section will be selected.\n   * If not provided, the selected sections will be handled internally.\n   */\n  selectedSections: PropTypes.oneOfType([PropTypes.oneOf(['all', 'day', 'empty', 'hours', 'meridiem', 'minutes', 'month', 'seconds', 'weekDay', 'year']), PropTypes.number]),\n  /**\n   * Disable specific time.\n   * @template TDate\n   * @param {TDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * Define custom view renderers for each section.\n   * If `null`, the section will only have field editing.\n   * If `undefined`, internally defined view will be used.\n   */\n  viewRenderers: PropTypes.shape({\n    hours: PropTypes.func,\n    minutes: PropTypes.func,\n    seconds: PropTypes.func\n  }),\n  /**\n   * Available views.\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'minutes', 'seconds']).isRequired)\n};\nexport { MobileTimePicker };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,qBAAqB,MAAM,kCAAkC;AACpE,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,6BAA6B,QAAQ,yBAAyB;AACvE,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,sBAAsB,EAAEC,YAAY,QAAQ,wBAAwB;AAC7E,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,0BAA0B,QAAQ,4CAA4C;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,aAAaf,KAAK,CAACgB,UAAU,CAAC,SAASD,gBAAgBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC7F,MAAMC,YAAY,GAAGZ,sBAAsB,CAAC,CAAC;EAC7C,MAAMa,KAAK,GAAGZ,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAMa,gBAAgB,GAAGf,6BAA6B,CAACW,OAAO,EAAE,qBAAqB,CAAC;EACtF,MAAMK,aAAa,GAAGvB,QAAQ,CAAC;IAC7BwB,KAAK,EAAEX,mBAAmB;IAC1BY,OAAO,EAAEZ,mBAAmB;IAC5Ba,OAAO,EAAEb;EACX,CAAC,EAAES,gBAAgB,CAACC,aAAa,CAAC;EAClC,MAAMI,WAAW,GAAGL,gBAAgB,CAACK,WAAW,IAAI,KAAK;;EAEzD;EACA,MAAMC,KAAK,GAAG5B,QAAQ,CAAC,CAAC,CAAC,EAAEsB,gBAAgB,EAAE;IAC3CK,WAAW;IACXJ,aAAa;IACbM,MAAM,EAAEf,iBAAiB,CAACO,KAAK,EAAEC,gBAAgB,CAAC;IAClDQ,KAAK,EAAE9B,QAAQ,CAAC;MACd+B,KAAK,EAAEzB;IACT,CAAC,EAAEgB,gBAAgB,CAACQ,KAAK,CAAC;IAC1BE,SAAS,EAAEhC,QAAQ,CAAC,CAAC,CAAC,EAAEsB,gBAAgB,CAACU,SAAS,EAAE;MAClDD,KAAK,EAAEE,UAAU,IAAIjC,QAAQ,CAAC,CAAC,CAAC,EAAEG,qBAAqB,CAACmB,gBAAgB,CAACU,SAAS,EAAED,KAAK,EAAEE,UAAU,CAAC,EAAEvB,sBAAsB,CAACY,gBAAgB,CAAC,EAAE;QAChJH;MACF,CAAC,CAAC;MACFe,OAAO,EAAElC,QAAQ,CAAC;QAChBmC,MAAM,EAAE,KAAK;QACbR;MACF,CAAC,EAAEL,gBAAgB,CAACU,SAAS,EAAEE,OAAO;IACxC,CAAC;EACH,CAAC,CAAC;EACF,MAAM;IACJE;EACF,CAAC,GAAGxB,eAAe,CAAC;IAClBgB,KAAK;IACLS,YAAY,EAAEhC,sBAAsB;IACpCiC,SAAS,EAAE,MAAM;IACjBC,qBAAqB,EAAExB,0BAA0B,CAAC;MAChDM,KAAK;MACLmB,SAAS,EAAE,UAAU;MACrBC,kBAAkB,EAAErB,YAAY,CAACsB,sBAAsB;MACvDC,gBAAgB,EAAEf,KAAK,CAACgB,UAAU,EAAEF;IACtC,CAAC,CAAC;IACFG,SAAS,EAAElC;EACb,CAAC,CAAC;EACF,OAAOyB,YAAY,CAAC,CAAC;AACvB,CAAC,CAAC;AACFpB,gBAAgB,CAAC8B,SAAS,GAAG;EAC3B;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEC,IAAI,EAAE7C,SAAS,CAAC8C,IAAI;EACpB;AACF;AACA;AACA;EACErB,WAAW,EAAEzB,SAAS,CAAC8C,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEC,SAAS,EAAE/C,SAAS,CAAC8C,IAAI;EACzBE,SAAS,EAAEhD,SAAS,CAACiD,MAAM;EAC3B;AACF;AACA;AACA;EACEC,aAAa,EAAElD,SAAS,CAAC8C,IAAI;EAC7B;AACF;AACA;AACA;EACEK,YAAY,EAAEnD,SAAS,CAACoD,MAAM;EAC9B;AACF;AACA;AACA;EACEC,QAAQ,EAAErD,SAAS,CAAC8C,IAAI;EACxB;AACF;AACA;AACA;EACEQ,aAAa,EAAEtD,SAAS,CAAC8C,IAAI;EAC7B;AACF;AACA;AACA;EACES,wCAAwC,EAAEvD,SAAS,CAAC8C,IAAI;EACxD;AACF;AACA;AACA;EACEU,iBAAiB,EAAExD,SAAS,CAAC8C,IAAI;EACjC;AACF;AACA;AACA;EACEW,WAAW,EAAEzD,SAAS,CAAC8C,IAAI;EAC3B;AACF;AACA;EACEY,iCAAiC,EAAE1D,SAAS,CAAC2D,GAAG;EAChD;AACF;AACA;AACA;EACEhC,MAAM,EAAE3B,SAAS,CAACiD,MAAM;EACxB;AACF;AACA;AACA;AACA;EACEW,aAAa,EAAE5D,SAAS,CAAC6D,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;EACrD;AACF;AACA;EACEC,QAAQ,EAAE5D,OAAO;EACjB;AACF;AACA;EACE6D,KAAK,EAAE/D,SAAS,CAACgE,IAAI;EACrB;AACF;AACA;AACA;EACEtB,UAAU,EAAE1C,SAAS,CAACoD,MAAM;EAC5B;AACF;AACA;AACA;EACEa,OAAO,EAAEjE,SAAS,CAACoD,MAAM;EACzB;AACF;AACA;AACA;EACEc,OAAO,EAAElE,SAAS,CAACoD,MAAM;EACzB;AACF;AACA;AACA;EACEe,WAAW,EAAEnE,SAAS,CAACoE,MAAM;EAC7B;AACF;AACA;EACEC,IAAI,EAAErE,SAAS,CAACiD,MAAM;EACtB;AACF;AACA;AACA;AACA;AACA;AACA;EACEqB,QAAQ,EAAEtE,SAAS,CAACuE,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,QAAQ,EAAExE,SAAS,CAACuE,IAAI;EACxB;AACF;AACA;AACA;EACEE,OAAO,EAAEzE,SAAS,CAACuE,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,OAAO,EAAE1E,SAAS,CAACuE,IAAI;EACvB;AACF;AACA;AACA;EACEI,MAAM,EAAE3E,SAAS,CAACuE,IAAI;EACtB;AACF;AACA;AACA;EACEK,wBAAwB,EAAE5E,SAAS,CAACuE,IAAI;EACxC;AACF;AACA;AACA;AACA;EACEM,YAAY,EAAE7E,SAAS,CAACuE,IAAI;EAC5B;AACF;AACA;AACA;EACEO,IAAI,EAAE9E,SAAS,CAAC8C,IAAI;EACpB;AACF;AACA;AACA;AACA;EACEiC,MAAM,EAAE/E,SAAS,CAAC6D,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACxD;AACF;AACA;EACEmB,WAAW,EAAEhF,SAAS,CAAC6D,KAAK,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;EACvDoB,QAAQ,EAAEjF,SAAS,CAAC8C,IAAI;EACxB;AACF;AACA;AACA;EACEoC,gBAAgB,EAAElF,SAAS,CAAC8C,IAAI;EAChC;AACF;AACA;AACA;EACEqC,aAAa,EAAEnF,SAAS,CAACoD,MAAM;EAC/B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEgC,gBAAgB,EAAEpF,SAAS,CAACqF,SAAS,CAAC,CAACrF,SAAS,CAAC6D,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,EAAE7D,SAAS,CAACoE,MAAM,CAAC,CAAC;EAC1K;AACF;AACA;AACA;AACA;AACA;AACA;EACEkB,iBAAiB,EAAEtF,SAAS,CAACuE,IAAI;EACjC;AACF;AACA;AACA;EACEzC,SAAS,EAAE9B,SAAS,CAACoD,MAAM;EAC3B;AACF;AACA;AACA;EACExB,KAAK,EAAE5B,SAAS,CAACoD,MAAM;EACvB;AACF;AACA;EACEmC,EAAE,EAAEvF,SAAS,CAACqF,SAAS,CAAC,CAACrF,SAAS,CAACwF,OAAO,CAACxF,SAAS,CAACqF,SAAS,CAAC,CAACrF,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAACoD,MAAM,EAAEpD,SAAS,CAAC8C,IAAI,CAAC,CAAC,CAAC,EAAE9C,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAACoD,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;EACEqC,QAAQ,EAAEzF,SAAS,CAACiD,MAAM;EAC1B;AACF;AACA;AACA;EACEyC,KAAK,EAAE1F,SAAS,CAACoD,MAAM;EACvB;AACF;AACA;AACA;AACA;EACEuC,IAAI,EAAE3F,SAAS,CAAC6D,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACtD;AACF;AACA;AACA;AACA;EACExC,aAAa,EAAErB,SAAS,CAAC4F,KAAK,CAAC;IAC7BtE,KAAK,EAAEtB,SAAS,CAACuE,IAAI;IACrBhD,OAAO,EAAEvB,SAAS,CAACuE,IAAI;IACvB/C,OAAO,EAAExB,SAAS,CAACuE;EACrB,CAAC,CAAC;EACF;AACF;AACA;EACEsB,KAAK,EAAE7F,SAAS,CAACwF,OAAO,CAACxF,SAAS,CAAC6D,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAACiC,UAAU;AACtF,CAAC;AACD,SAAShF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}