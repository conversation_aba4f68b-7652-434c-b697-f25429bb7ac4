{"ast": null, "code": "import freeGlobal from './_freeGlobal.js';\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\nexport default root;", "map": {"version": 3, "names": ["freeGlobal", "freeSelf", "self", "Object", "root", "Function"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/lodash-es/_root.js"], "sourcesContent": ["import freeGlobal from './_freeGlobal.js';\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\nexport default root;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;;AAEzC;AACA,IAAIC,QAAQ,GAAG,OAAOC,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAKA,MAAM,IAAID,IAAI;;AAEhF;AACA,IAAIE,IAAI,GAAGJ,UAAU,IAAIC,QAAQ,IAAII,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;AAE9D,eAAeD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}