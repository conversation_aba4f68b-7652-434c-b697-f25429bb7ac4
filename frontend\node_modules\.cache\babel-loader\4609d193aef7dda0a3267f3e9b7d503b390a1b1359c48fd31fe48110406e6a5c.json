{"ast": null, "code": "var MediaQueryDispatch = require('./MediaQueryDispatch');\nmodule.exports = new MediaQueryDispatch();", "map": {"version": 3, "names": ["MediaQueryDispatch", "require", "module", "exports"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/enquire.js/src/index.js"], "sourcesContent": ["var MediaQueryDispatch = require('./MediaQueryDispatch');\nmodule.exports = new MediaQueryDispatch();\n"], "mappings": "AAAA,IAAIA,kBAAkB,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AACxDC,MAAM,CAACC,OAAO,GAAG,IAAIH,kBAAkB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}