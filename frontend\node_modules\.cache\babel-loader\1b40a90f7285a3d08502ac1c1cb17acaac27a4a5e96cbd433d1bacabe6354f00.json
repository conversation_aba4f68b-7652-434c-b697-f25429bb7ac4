{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getMultiSectionDigitalClockUtilityClass(slot) {\n  return generateUtilityClass('MuiMultiSectionDigitalClock', slot);\n}\nexport const multiSectionDigitalClockClasses = generateUtilityClasses('MuiMultiSectionDigitalClock', ['root']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getMultiSectionDigitalClockUtilityClass", "slot", "multiSectionDigitalClockClasses"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/MultiSectionDigitalClock/multiSectionDigitalClockClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getMultiSectionDigitalClockUtilityClass(slot) {\n  return generateUtilityClass('MuiMultiSectionDigitalClock', slot);\n}\nexport const multiSectionDigitalClockClasses = generateUtilityClasses('MuiMultiSectionDigitalClock', ['root']);"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,uCAAuCA,CAACC,IAAI,EAAE;EAC5D,OAAOH,oBAAoB,CAAC,6BAA6B,EAAEG,IAAI,CAAC;AAClE;AACA,OAAO,MAAMC,+BAA+B,GAAGH,sBAAsB,CAAC,6BAA6B,EAAE,CAAC,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}