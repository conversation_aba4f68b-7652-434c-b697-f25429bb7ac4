{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { MuiPickersAdapterContext } from \"../../LocalizationProvider/LocalizationProvider.js\";\nimport { DEFAULT_LOCALE } from \"../../locales/enUS.js\";\nexport const useLocalizationContext = () => {\n  const localization = React.useContext(MuiPickersAdapterContext);\n  if (localization === null) {\n    throw new Error(['MUI X: Can not find the date and time pickers localization context.', 'It looks like you forgot to wrap your component in LocalizationProvider.', 'This can also happen if you are bundling multiple versions of the `@mui/x-date-pickers` package'].join('\\n'));\n  }\n  if (localization.utils === null) {\n    throw new Error(['MUI X: Can not find the date and time pickers adapter from its localization context.', 'It looks like you forgot to pass a `dateAdapter` to your LocalizationProvider.'].join('\\n'));\n  }\n  const localeText = React.useMemo(() => _extends({}, DEFAULT_LOCALE, localization.localeText), [localization.localeText]);\n  return React.useMemo(() => _extends({}, localization, {\n    localeText\n  }), [localization, localeText]);\n};\nexport const useUtils = () => useLocalizationContext().utils;\nexport const useDefaultDates = () => useLocalizationContext().defaultDates;\nexport const useNow = timezone => {\n  const utils = useUtils();\n  const now = React.useRef();\n  if (now.current === undefined) {\n    now.current = utils.date(undefined, timezone);\n  }\n  return now.current;\n};", "map": {"version": 3, "names": ["_extends", "React", "MuiPickersAdapterContext", "DEFAULT_LOCALE", "useLocalizationContext", "localization", "useContext", "Error", "join", "utils", "localeText", "useMemo", "useUtils", "useDefaultDates", "defaultDates", "useNow", "timezone", "now", "useRef", "current", "undefined", "date"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useUtils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { MuiPickersAdapterContext } from \"../../LocalizationProvider/LocalizationProvider.js\";\nimport { DEFAULT_LOCALE } from \"../../locales/enUS.js\";\nexport const useLocalizationContext = () => {\n  const localization = React.useContext(MuiPickersAdapterContext);\n  if (localization === null) {\n    throw new Error(['MUI X: Can not find the date and time pickers localization context.', 'It looks like you forgot to wrap your component in LocalizationProvider.', 'This can also happen if you are bundling multiple versions of the `@mui/x-date-pickers` package'].join('\\n'));\n  }\n  if (localization.utils === null) {\n    throw new Error(['MUI X: Can not find the date and time pickers adapter from its localization context.', 'It looks like you forgot to pass a `dateAdapter` to your LocalizationProvider.'].join('\\n'));\n  }\n  const localeText = React.useMemo(() => _extends({}, DEFAULT_LOCALE, localization.localeText), [localization.localeText]);\n  return React.useMemo(() => _extends({}, localization, {\n    localeText\n  }), [localization, localeText]);\n};\nexport const useUtils = () => useLocalizationContext().utils;\nexport const useDefaultDates = () => useLocalizationContext().defaultDates;\nexport const useNow = timezone => {\n  const utils = useUtils();\n  const now = React.useRef();\n  if (now.current === undefined) {\n    now.current = utils.date(undefined, timezone);\n  }\n  return now.current;\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,wBAAwB,QAAQ,oDAAoD;AAC7F,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAO,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EAC1C,MAAMC,YAAY,GAAGJ,KAAK,CAACK,UAAU,CAACJ,wBAAwB,CAAC;EAC/D,IAAIG,YAAY,KAAK,IAAI,EAAE;IACzB,MAAM,IAAIE,KAAK,CAAC,CAAC,qEAAqE,EAAE,0EAA0E,EAAE,iGAAiG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACpR;EACA,IAAIH,YAAY,CAACI,KAAK,KAAK,IAAI,EAAE;IAC/B,MAAM,IAAIF,KAAK,CAAC,CAAC,sFAAsF,EAAE,gFAAgF,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACxM;EACA,MAAME,UAAU,GAAGT,KAAK,CAACU,OAAO,CAAC,MAAMX,QAAQ,CAAC,CAAC,CAAC,EAAEG,cAAc,EAAEE,YAAY,CAACK,UAAU,CAAC,EAAE,CAACL,YAAY,CAACK,UAAU,CAAC,CAAC;EACxH,OAAOT,KAAK,CAACU,OAAO,CAAC,MAAMX,QAAQ,CAAC,CAAC,CAAC,EAAEK,YAAY,EAAE;IACpDK;EACF,CAAC,CAAC,EAAE,CAACL,YAAY,EAAEK,UAAU,CAAC,CAAC;AACjC,CAAC;AACD,OAAO,MAAME,QAAQ,GAAGA,CAAA,KAAMR,sBAAsB,CAAC,CAAC,CAACK,KAAK;AAC5D,OAAO,MAAMI,eAAe,GAAGA,CAAA,KAAMT,sBAAsB,CAAC,CAAC,CAACU,YAAY;AAC1E,OAAO,MAAMC,MAAM,GAAGC,QAAQ,IAAI;EAChC,MAAMP,KAAK,GAAGG,QAAQ,CAAC,CAAC;EACxB,MAAMK,GAAG,GAAGhB,KAAK,CAACiB,MAAM,CAAC,CAAC;EAC1B,IAAID,GAAG,CAACE,OAAO,KAAKC,SAAS,EAAE;IAC7BH,GAAG,CAACE,OAAO,GAAGV,KAAK,CAACY,IAAI,CAACD,SAAS,EAAEJ,QAAQ,CAAC;EAC/C;EACA,OAAOC,GAAG,CAACE,OAAO;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}