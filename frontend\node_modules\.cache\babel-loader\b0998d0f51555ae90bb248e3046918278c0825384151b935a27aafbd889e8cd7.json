{"ast": null, "code": "export { PickersShortcuts } from \"./PickersShortcuts.js\";", "map": {"version": 3, "names": ["PickersShortcuts"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/PickersShortcuts/index.js"], "sourcesContent": ["export { PickersShortcuts } from \"./PickersShortcuts.js\";"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}