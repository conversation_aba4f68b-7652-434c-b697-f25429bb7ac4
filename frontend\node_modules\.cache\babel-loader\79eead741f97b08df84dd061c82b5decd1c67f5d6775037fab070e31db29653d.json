{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar checkboxRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-checked': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-invalid': null,\n    'aria-readonly': null,\n    'aria-required': null\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        name: 'type',\n        value: 'checkbox'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'option'\n    },\n    module: 'ARIA'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-checked': null\n  },\n  superClass: [['roletype', 'widget', 'input']]\n};\nvar _default = checkboxRole;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "checkboxRole", "abstract", "accessibleNameRequired", "baseConcepts", "childrenPresentational", "nameFrom", "prohibitedProps", "props", "relatedConcepts", "concept", "attributes", "name", "module", "requireContextRole", "requiredContextRole", "requiredOwnedElements", "requiredProps", "superClass", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/etc/roles/literal/checkboxRole.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar checkboxRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-checked': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-invalid': null,\n    'aria-readonly': null,\n    'aria-required': null\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        name: 'type',\n        value: 'checkbox'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'option'\n    },\n    module: 'ARIA'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-checked': null\n  },\n  superClass: [['roletype', 'widget', 'input']]\n};\nvar _default = checkboxRole;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,YAAY,GAAG;EACjBC,QAAQ,EAAE,KAAK;EACfC,sBAAsB,EAAE,IAAI;EAC5BC,YAAY,EAAE,EAAE;EAChBC,sBAAsB,EAAE,IAAI;EAC5BC,QAAQ,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;EAChCC,eAAe,EAAE,EAAE;EACnBC,KAAK,EAAE;IACL,cAAc,EAAE,IAAI;IACpB,mBAAmB,EAAE,IAAI;IACzB,eAAe,EAAE,IAAI;IACrB,cAAc,EAAE,IAAI;IACpB,eAAe,EAAE,IAAI;IACrB,eAAe,EAAE;EACnB,CAAC;EACDC,eAAe,EAAE,CAAC;IAChBC,OAAO,EAAE;MACPC,UAAU,EAAE,CAAC;QACXC,IAAI,EAAE,MAAM;QACZb,KAAK,EAAE;MACT,CAAC,CAAC;MACFa,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,EAAE;IACDH,OAAO,EAAE;MACPE,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,CAAC;EACFC,kBAAkB,EAAE,EAAE;EACtBC,mBAAmB,EAAE,EAAE;EACvBC,qBAAqB,EAAE,EAAE;EACzBC,aAAa,EAAE;IACb,cAAc,EAAE;EAClB,CAAC;EACDC,UAAU,EAAE,CAAC,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;AAC9C,CAAC;AACD,IAAIC,QAAQ,GAAGlB,YAAY;AAC3BH,OAAO,CAACE,OAAO,GAAGmB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}