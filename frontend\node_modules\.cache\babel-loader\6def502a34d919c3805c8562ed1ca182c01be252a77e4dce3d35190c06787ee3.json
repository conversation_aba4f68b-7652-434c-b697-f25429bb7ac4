{"ast": null, "code": "'use client';\n\nimport { useLocalizationContext } from \"../internals/hooks/useUtils.js\";\nexport const usePickersTranslations = () => useLocalizationContext().localeText;", "map": {"version": 3, "names": ["useLocalizationContext", "usePickersTranslations", "localeText"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/hooks/usePickersTranslations.js"], "sourcesContent": ["'use client';\n\nimport { useLocalizationContext } from \"../internals/hooks/useUtils.js\";\nexport const usePickersTranslations = () => useLocalizationContext().localeText;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,sBAAsB,QAAQ,gCAAgC;AACvE,OAAO,MAAMC,sBAAsB,GAAGA,CAAA,KAAMD,sBAAsB,CAAC,CAAC,CAACE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}