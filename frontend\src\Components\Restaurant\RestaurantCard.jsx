import { Card, Chip, IconButton } from "@mui/material";
import React, { useState } from "react";
import FavoriteBorderIcon from "@mui/icons-material/FavoriteBorder";
import FavoriteIcon from "@mui/icons-material/Favorite";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { addToFavorite } from "../State/Auth/Action";
import { isPresentInFavorites } from "../Config/config";
export const RestaurantCard = ({ item }) => {
    const [isHovered, setIsHovered] = useState(false);
    const [imageLoaded, setImageLoaded] = useState(false);

    const navigate = useNavigate()
    const dispatch = useDispatch()

    const jwt = localStorage.getItem("token")

    const {auth} = useSelector(store=>store)

    const handleAddToFavorite =()=> {
        dispatch(addToFavorite({token: jwt, restaurantId: item.id}));

    }

    const handleNavigateToRestaurant = () => {
        console.log(item.address.city, item.name, item.id);
        //Eğer restaurant açıksa adrese gidiyor kapalı ise gitmez
        if (item.open || !item.open) {
          navigate(`/restaurant/${item.address.city}/${item.name}/${item.id}`);
        }
      };
      
  return (
    <Card
      className="w-[18rem] transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 cursor-pointer group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleNavigateToRestaurant}
    >
      <div className="relative overflow-hidden">
        {!imageLoaded && (
          <div className="w-full h-[10rem] bg-gray-200 animate-pulse rounded-t-md"></div>
        )}
        <img
          className={`w-full h-[10rem] rounded-t-md object-cover transition-transform duration-500 ${
            isHovered ? 'scale-110' : 'scale-100'
          } ${imageLoaded ? 'block' : 'hidden'}`}
          src={item.images[0]}
          alt={item.name}
          onLoad={() => setImageLoaded(true)}
        />

        <div className={`absolute inset-0 bg-black bg-opacity-20 transition-opacity duration-300 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`}></div>

        <Chip
          size="small"
          className={`absolute top-3 left-3 transition-all duration-300 ${
            isHovered ? 'scale-110' : 'scale-100'
          }`}
          color={item.open ? "success" : "error"}
          label={item.open ? "Open" : "Closed"}
        />

        {item.foodType && (
          <Chip
            size="small"
            className="absolute top-3 right-3 bg-white bg-opacity-90"
            label={item.foodType}
          />
        )}
      </div>

      <div className="p-4 textPart lg:flex w-full justify-between">
        <div className="space-y-2 flex-1">
          <h3 className="font-bold text-lg text-gray-800 group-hover:text-orange-600 transition-colors duration-300">
            {item.name || item.title}
          </h3>
          <p className="text-gray-500 text-sm line-clamp-2 leading-relaxed">
            {item.description}
          </p>
          {item.address && (
            <p className="text-gray-400 text-xs">
              📍 {item.address.city}, {item.address.country}
            </p>
          )}
        </div>

        <div className="flex items-start">
          <IconButton
            onClick={(e) => {
              e.stopPropagation();
              handleAddToFavorite();
            }}
            className={`transition-all duration-300 ${
              isHovered ? 'scale-110' : 'scale-100'
            }`}
          >
            {isPresentInFavorites(auth.favorites,item) ?
              <FavoriteIcon className="text-red-500" /> :
              <FavoriteBorderIcon className="text-gray-400 hover:text-red-500" />
            }
          </IconButton>
        </div>
      </div>
    </Card>
  );
};

export default RestaurantCard;
