{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getMultiSectionDigitalClockSectionUtilityClass(slot) {\n  return generateUtilityClass('MuiMultiSectionDigitalClockSection', slot);\n}\nexport const multiSectionDigitalClockSectionClasses = generateUtilityClasses('MuiMultiSectionDigitalClockSection', ['root', 'item']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getMultiSectionDigitalClockSectionUtilityClass", "slot", "multiSectionDigitalClockSectionClasses"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/MultiSectionDigitalClock/multiSectionDigitalClockSectionClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getMultiSectionDigitalClockSectionUtilityClass(slot) {\n  return generateUtilityClass('MuiMultiSectionDigitalClockSection', slot);\n}\nexport const multiSectionDigitalClockSectionClasses = generateUtilityClasses('MuiMultiSectionDigitalClockSection', ['root', 'item']);"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,8CAA8CA,CAACC,IAAI,EAAE;EACnE,OAAOH,oBAAoB,CAAC,oCAAoC,EAAEG,IAAI,CAAC;AACzE;AACA,OAAO,MAAMC,sCAAsC,GAAGH,sBAAsB,CAAC,oCAAoC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}