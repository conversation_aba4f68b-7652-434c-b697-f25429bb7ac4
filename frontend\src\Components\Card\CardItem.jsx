import { Chip, IconButton } from "@mui/material";
import React from "react"
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { removeCardItme, updateCardItem } from "../State/Card/action";

export const CardItem = ({item}) => {

  const {auth,card} = useSelector(store=>store);
  const navigate = useNavigate()
  const dispatch = useDispatch()

  const jwt = localStorage.getItem("token")

  const handleUpdateCardItem = (value)=>{
    if(value === -1 && item.quantity === 1){
      handleRemoveCardItem()
    }

    const data = { cardItemId: item.id, quantity: item.quantity + value };
    dispatch(updateCardItem({ data, token:jwt }));
    
  }

  const handleRemoveCardItem=()=>{
    dispatch(removeCardItme({cardItemId:item.id, token:jwt || auth.token}))
  }
    return (
        <div className="px-5 ">
          <div className="lg:flex items-center lg:space-x-5">
            <div>
              <img
                className="w-[5rem] h-[5rem] object-cover"
                src={item.food.image[0]}
                alt=""
              />
            </div>
    
            <div className="flex items-center justify-between lg:w-[70%]">
              <div className="space-y-1 lg:space-y-3 w-full">
                <p>{item.food.name}</p>
                <div className="flex justify-between  items-center">
                  <div className="flex items-center space-x-1">
                    <IconButton onClick={()=>handleUpdateCardItem(-1)}>
                      <RemoveCircleOutlineIcon />
                    </IconButton>
                    <div className="w-5 h-5 text-sx flex items-center justify-center">
                      {item.quantity}
                    </div>
                    <IconButton onClick={()=>handleUpdateCardItem(1)}>
                      <AddCircleOutlineIcon />
                    </IconButton>
                  </div>
                </div>
              </div>
    
              <p>${item.totalPrice}</p>
            </div>
          </div>
    
          <div className="pt-3 space-x-2">
            {item.ingredients.map((ingredient) => <Chip label={ingredient}/> )}
          </div>
        </div>
      );
    };

export default CardItem