{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = iterationDecorator;\nvar _iteratorProxy = _interopRequireDefault(require(\"./iteratorProxy\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction iterationDecorator(collection, entries) {\n  if (typeof Symbol === 'function' && _typeof(Symbol.iterator) === 'symbol') {\n    Object.defineProperty(collection, Symbol.iterator, {\n      value: _iteratorProxy.default.bind(entries)\n    });\n  }\n  return collection;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "iterationDecorator", "_iteratorProxy", "_interopRequireDefault", "require", "obj", "__esModule", "_typeof", "Symbol", "iterator", "constructor", "prototype", "collection", "entries", "bind"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/util/iterationDecorator.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = iterationDecorator;\nvar _iteratorProxy = _interopRequireDefault(require(\"./iteratorProxy\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction iterationDecorator(collection, entries) {\n  if (typeof Symbol === 'function' && _typeof(Symbol.iterator) === 'symbol') {\n    Object.defineProperty(collection, Symbol.iterator, {\n      value: _iteratorProxy.default.bind(entries)\n    });\n  }\n  return collection;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,kBAAkB;AACpC,IAAIC,cAAc,GAAGC,sBAAsB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AACvE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAC9F,SAASE,OAAOA,CAACF,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOE,OAAO,GAAG,UAAU,IAAI,OAAOC,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUJ,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOG,MAAM,IAAIH,GAAG,CAACK,WAAW,KAAKF,MAAM,IAAIH,GAAG,KAAKG,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAON,GAAG;EAAE,CAAC,EAAEE,OAAO,CAACF,GAAG,CAAC;AAAE;AAC/U,SAASJ,kBAAkBA,CAACW,UAAU,EAAEC,OAAO,EAAE;EAC/C,IAAI,OAAOL,MAAM,KAAK,UAAU,IAAID,OAAO,CAACC,MAAM,CAACC,QAAQ,CAAC,KAAK,QAAQ,EAAE;IACzEb,MAAM,CAACC,cAAc,CAACe,UAAU,EAAEJ,MAAM,CAACC,QAAQ,EAAE;MACjDV,KAAK,EAAEG,cAAc,CAACF,OAAO,CAACc,IAAI,CAACD,OAAO;IAC5C,CAAC,CAAC;EACJ;EACA,OAAOD,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}