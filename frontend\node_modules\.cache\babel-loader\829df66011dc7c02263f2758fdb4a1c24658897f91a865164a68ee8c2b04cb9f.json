{"ast": null, "code": "import * as React from 'react';\nimport { ClockNumber } from \"./ClockNumber.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * @ignore - internal component.\n */\nexport const getHourNumbers = ({\n  ampm,\n  value,\n  getClockNumberText,\n  isDisabled,\n  selectedId,\n  utils\n}) => {\n  const currentHours = value ? utils.getHours(value) : null;\n  const hourNumbers = [];\n  const startHour = ampm ? 1 : 0;\n  const endHour = ampm ? 12 : 23;\n  const isSelected = hour => {\n    if (currentHours === null) {\n      return false;\n    }\n    if (ampm) {\n      if (hour === 12) {\n        return currentHours === 12 || currentHours === 0;\n      }\n      return currentHours === hour || currentHours - 12 === hour;\n    }\n    return currentHours === hour;\n  };\n  for (let hour = startHour; hour <= endHour; hour += 1) {\n    let label = hour.toString();\n    if (hour === 0) {\n      label = '00';\n    }\n    const inner = !ampm && (hour === 0 || hour > 12);\n    label = utils.formatNumber(label);\n    const selected = isSelected(hour);\n    hourNumbers.push(/*#__PURE__*/_jsx(ClockNumber, {\n      id: selected ? selectedId : undefined,\n      index: hour,\n      inner: inner,\n      selected: selected,\n      disabled: isDisabled(hour),\n      label: label,\n      \"aria-label\": getClockNumberText(label)\n    }, hour));\n  }\n  return hourNumbers;\n};\nexport const getMinutesNumbers = ({\n  utils,\n  value,\n  isDisabled,\n  getClockNumberText,\n  selectedId\n}) => {\n  const f = utils.formatNumber;\n  return [[5, f('05')], [10, f('10')], [15, f('15')], [20, f('20')], [25, f('25')], [30, f('30')], [35, f('35')], [40, f('40')], [45, f('45')], [50, f('50')], [55, f('55')], [0, f('00')]].map(([numberValue, label], index) => {\n    const selected = numberValue === value;\n    return /*#__PURE__*/_jsx(ClockNumber, {\n      label: label,\n      id: selected ? selectedId : undefined,\n      index: index + 1,\n      inner: false,\n      disabled: isDisabled(numberValue),\n      selected: selected,\n      \"aria-label\": getClockNumberText(label)\n    }, numberValue);\n  });\n};", "map": {"version": 3, "names": ["React", "ClockNumber", "jsx", "_jsx", "getHourNumbers", "ampm", "value", "getClockNumberText", "isDisabled", "selectedId", "utils", "currentHours", "getHours", "hourNumbers", "startHour", "endHour", "isSelected", "hour", "label", "toString", "inner", "formatNumber", "selected", "push", "id", "undefined", "index", "disabled", "getMinutesNumbers", "f", "map", "numberValue"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/TimeClock/ClockNumbers.js"], "sourcesContent": ["import * as React from 'react';\nimport { ClockNumber } from \"./ClockNumber.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * @ignore - internal component.\n */\nexport const getHourNumbers = ({\n  ampm,\n  value,\n  getClockNumberText,\n  isDisabled,\n  selectedId,\n  utils\n}) => {\n  const currentHours = value ? utils.getHours(value) : null;\n  const hourNumbers = [];\n  const startHour = ampm ? 1 : 0;\n  const endHour = ampm ? 12 : 23;\n  const isSelected = hour => {\n    if (currentHours === null) {\n      return false;\n    }\n    if (ampm) {\n      if (hour === 12) {\n        return currentHours === 12 || currentHours === 0;\n      }\n      return currentHours === hour || currentHours - 12 === hour;\n    }\n    return currentHours === hour;\n  };\n  for (let hour = startHour; hour <= endHour; hour += 1) {\n    let label = hour.toString();\n    if (hour === 0) {\n      label = '00';\n    }\n    const inner = !ampm && (hour === 0 || hour > 12);\n    label = utils.formatNumber(label);\n    const selected = isSelected(hour);\n    hourNumbers.push(/*#__PURE__*/_jsx(ClockNumber, {\n      id: selected ? selectedId : undefined,\n      index: hour,\n      inner: inner,\n      selected: selected,\n      disabled: isDisabled(hour),\n      label: label,\n      \"aria-label\": getClockNumberText(label)\n    }, hour));\n  }\n  return hourNumbers;\n};\nexport const getMinutesNumbers = ({\n  utils,\n  value,\n  isDisabled,\n  getClockNumberText,\n  selectedId\n}) => {\n  const f = utils.formatNumber;\n  return [[5, f('05')], [10, f('10')], [15, f('15')], [20, f('20')], [25, f('25')], [30, f('30')], [35, f('35')], [40, f('40')], [45, f('45')], [50, f('50')], [55, f('55')], [0, f('00')]].map(([numberValue, label], index) => {\n    const selected = numberValue === value;\n    return /*#__PURE__*/_jsx(ClockNumber, {\n      label: label,\n      id: selected ? selectedId : undefined,\n      index: index + 1,\n      inner: false,\n      disabled: isDisabled(numberValue),\n      selected: selected,\n      \"aria-label\": getClockNumberText(label)\n    }, numberValue);\n  });\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAC7BC,IAAI;EACJC,KAAK;EACLC,kBAAkB;EAClBC,UAAU;EACVC,UAAU;EACVC;AACF,CAAC,KAAK;EACJ,MAAMC,YAAY,GAAGL,KAAK,GAAGI,KAAK,CAACE,QAAQ,CAACN,KAAK,CAAC,GAAG,IAAI;EACzD,MAAMO,WAAW,GAAG,EAAE;EACtB,MAAMC,SAAS,GAAGT,IAAI,GAAG,CAAC,GAAG,CAAC;EAC9B,MAAMU,OAAO,GAAGV,IAAI,GAAG,EAAE,GAAG,EAAE;EAC9B,MAAMW,UAAU,GAAGC,IAAI,IAAI;IACzB,IAAIN,YAAY,KAAK,IAAI,EAAE;MACzB,OAAO,KAAK;IACd;IACA,IAAIN,IAAI,EAAE;MACR,IAAIY,IAAI,KAAK,EAAE,EAAE;QACf,OAAON,YAAY,KAAK,EAAE,IAAIA,YAAY,KAAK,CAAC;MAClD;MACA,OAAOA,YAAY,KAAKM,IAAI,IAAIN,YAAY,GAAG,EAAE,KAAKM,IAAI;IAC5D;IACA,OAAON,YAAY,KAAKM,IAAI;EAC9B,CAAC;EACD,KAAK,IAAIA,IAAI,GAAGH,SAAS,EAAEG,IAAI,IAAIF,OAAO,EAAEE,IAAI,IAAI,CAAC,EAAE;IACrD,IAAIC,KAAK,GAAGD,IAAI,CAACE,QAAQ,CAAC,CAAC;IAC3B,IAAIF,IAAI,KAAK,CAAC,EAAE;MACdC,KAAK,GAAG,IAAI;IACd;IACA,MAAME,KAAK,GAAG,CAACf,IAAI,KAAKY,IAAI,KAAK,CAAC,IAAIA,IAAI,GAAG,EAAE,CAAC;IAChDC,KAAK,GAAGR,KAAK,CAACW,YAAY,CAACH,KAAK,CAAC;IACjC,MAAMI,QAAQ,GAAGN,UAAU,CAACC,IAAI,CAAC;IACjCJ,WAAW,CAACU,IAAI,CAAC,aAAapB,IAAI,CAACF,WAAW,EAAE;MAC9CuB,EAAE,EAAEF,QAAQ,GAAGb,UAAU,GAAGgB,SAAS;MACrCC,KAAK,EAAET,IAAI;MACXG,KAAK,EAAEA,KAAK;MACZE,QAAQ,EAAEA,QAAQ;MAClBK,QAAQ,EAAEnB,UAAU,CAACS,IAAI,CAAC;MAC1BC,KAAK,EAAEA,KAAK;MACZ,YAAY,EAAEX,kBAAkB,CAACW,KAAK;IACxC,CAAC,EAAED,IAAI,CAAC,CAAC;EACX;EACA,OAAOJ,WAAW;AACpB,CAAC;AACD,OAAO,MAAMe,iBAAiB,GAAGA,CAAC;EAChClB,KAAK;EACLJ,KAAK;EACLE,UAAU;EACVD,kBAAkB;EAClBE;AACF,CAAC,KAAK;EACJ,MAAMoB,CAAC,GAAGnB,KAAK,CAACW,YAAY;EAC5B,OAAO,CAAC,CAAC,CAAC,EAAEQ,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAEA,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAEA,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAEA,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAEA,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAEA,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAEA,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAEA,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAEA,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAEA,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAEA,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,WAAW,EAAEb,KAAK,CAAC,EAAEQ,KAAK,KAAK;IAC7N,MAAMJ,QAAQ,GAAGS,WAAW,KAAKzB,KAAK;IACtC,OAAO,aAAaH,IAAI,CAACF,WAAW,EAAE;MACpCiB,KAAK,EAAEA,KAAK;MACZM,EAAE,EAAEF,QAAQ,GAAGb,UAAU,GAAGgB,SAAS;MACrCC,KAAK,EAAEA,KAAK,GAAG,CAAC;MAChBN,KAAK,EAAE,KAAK;MACZO,QAAQ,EAAEnB,UAAU,CAACuB,WAAW,CAAC;MACjCT,QAAQ,EAAEA,QAAQ;MAClB,YAAY,EAAEf,kBAAkB,CAACW,KAAK;IACxC,CAAC,EAAEa,WAAW,CAAC;EACjB,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}