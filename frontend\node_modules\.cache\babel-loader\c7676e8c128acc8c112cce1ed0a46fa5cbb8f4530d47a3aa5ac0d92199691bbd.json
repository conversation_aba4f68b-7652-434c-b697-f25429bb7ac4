{"ast": null, "code": "/**\n * Helper function for iterating over a collection\n *\n * @param collection\n * @param fn\n */\nfunction each(collection, fn) {\n  var i = 0,\n    length = collection.length,\n    cont;\n  for (i; i < length; i++) {\n    cont = fn(collection[i], i);\n    if (cont === false) {\n      break; //allow early exit\n    }\n  }\n}\n\n/**\n * Helper function for determining whether target object is an array\n *\n * @param target the object under test\n * @return {Boolean} true if array, false otherwise\n */\nfunction isArray(target) {\n  return Object.prototype.toString.apply(target) === '[object Array]';\n}\n\n/**\n * Helper function for determining whether target object is a function\n *\n * @param target the object under test\n * @return {Boolean} true if function, false otherwise\n */\nfunction isFunction(target) {\n  return typeof target === 'function';\n}\nmodule.exports = {\n  isFunction: isFunction,\n  isArray: isArray,\n  each: each\n};", "map": {"version": 3, "names": ["each", "collection", "fn", "i", "length", "cont", "isArray", "target", "Object", "prototype", "toString", "apply", "isFunction", "module", "exports"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/enquire.js/src/Util.js"], "sourcesContent": ["/**\n * Helper function for iterating over a collection\n *\n * @param collection\n * @param fn\n */\nfunction each(collection, fn) {\n    var i      = 0,\n        length = collection.length,\n        cont;\n\n    for(i; i < length; i++) {\n        cont = fn(collection[i], i);\n        if(cont === false) {\n            break; //allow early exit\n        }\n    }\n}\n\n/**\n * Helper function for determining whether target object is an array\n *\n * @param target the object under test\n * @return {Boolean} true if array, false otherwise\n */\nfunction isArray(target) {\n    return Object.prototype.toString.apply(target) === '[object Array]';\n}\n\n/**\n * Helper function for determining whether target object is a function\n *\n * @param target the object under test\n * @return {Boolean} true if function, false otherwise\n */\nfunction isFunction(target) {\n    return typeof target === 'function';\n}\n\nmodule.exports = {\n    isFunction : isFunction,\n    isArray : isArray,\n    each : each\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAIA,CAACC,UAAU,EAAEC,EAAE,EAAE;EAC1B,IAAIC,CAAC,GAAQ,CAAC;IACVC,MAAM,GAAGH,UAAU,CAACG,MAAM;IAC1BC,IAAI;EAER,KAAIF,CAAC,EAAEA,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAAE;IACpBE,IAAI,GAAGH,EAAE,CAACD,UAAU,CAACE,CAAC,CAAC,EAAEA,CAAC,CAAC;IAC3B,IAAGE,IAAI,KAAK,KAAK,EAAE;MACf,MAAM,CAAC;IACX;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,MAAM,EAAE;EACrB,OAAOC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,KAAK,CAACJ,MAAM,CAAC,KAAK,gBAAgB;AACvE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,UAAUA,CAACL,MAAM,EAAE;EACxB,OAAO,OAAOA,MAAM,KAAK,UAAU;AACvC;AAEAM,MAAM,CAACC,OAAO,GAAG;EACbF,UAAU,EAAGA,UAAU;EACvBN,OAAO,EAAGA,OAAO;EACjBN,IAAI,EAAGA;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}