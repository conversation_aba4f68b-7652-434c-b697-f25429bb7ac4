{"ast": null, "code": "/* Use it instead of .includes method for IE support */\nexport function arrayIncludes(array, itemOrItems) {\n  if (Array.isArray(itemOrItems)) {\n    return itemOrItems.every(item => array.indexOf(item) !== -1);\n  }\n  return array.indexOf(itemOrItems) !== -1;\n}\nexport const onSpaceOrEnter = (innerFn, externalEvent) => event => {\n  if (event.key === 'Enter' || event.key === ' ') {\n    innerFn(event);\n\n    // prevent any side effects\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  if (externalEvent) {\n    externalEvent(event);\n  }\n};\nexport const executeInTheNextEventLoopTick = fn => {\n  setTimeout(fn, 0);\n};\n\n// https://www.abeautifulsite.net/posts/finding-the-active-element-in-a-shadow-root/\nexport const getActiveElement = (root = document) => {\n  const activeEl = root.activeElement;\n  if (!activeEl) {\n    return null;\n  }\n  if (activeEl.shadowRoot) {\n    return getActiveElement(activeEl.shadowRoot);\n  }\n  return activeEl;\n};\nexport const DEFAULT_DESKTOP_MODE_MEDIA_QUERY = '@media (pointer: fine)';", "map": {"version": 3, "names": ["arrayIncludes", "array", "itemOrItems", "Array", "isArray", "every", "item", "indexOf", "onSpaceOrEnter", "innerFn", "externalEvent", "event", "key", "preventDefault", "stopPropagation", "executeInTheNextEventLoopTick", "fn", "setTimeout", "getActiveElement", "root", "document", "activeEl", "activeElement", "shadowRoot", "DEFAULT_DESKTOP_MODE_MEDIA_QUERY"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/internals/utils/utils.js"], "sourcesContent": ["/* Use it instead of .includes method for IE support */\nexport function arrayIncludes(array, itemOrItems) {\n  if (Array.isArray(itemOrItems)) {\n    return itemOrItems.every(item => array.indexOf(item) !== -1);\n  }\n  return array.indexOf(itemOrItems) !== -1;\n}\nexport const onSpaceOrEnter = (innerFn, externalEvent) => event => {\n  if (event.key === 'Enter' || event.key === ' ') {\n    innerFn(event);\n\n    // prevent any side effects\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  if (externalEvent) {\n    externalEvent(event);\n  }\n};\nexport const executeInTheNextEventLoopTick = fn => {\n  setTimeout(fn, 0);\n};\n\n// https://www.abeautifulsite.net/posts/finding-the-active-element-in-a-shadow-root/\nexport const getActiveElement = (root = document) => {\n  const activeEl = root.activeElement;\n  if (!activeEl) {\n    return null;\n  }\n  if (activeEl.shadowRoot) {\n    return getActiveElement(activeEl.shadowRoot);\n  }\n  return activeEl;\n};\nexport const DEFAULT_DESKTOP_MODE_MEDIA_QUERY = '@media (pointer: fine)';"], "mappings": "AAAA;AACA,OAAO,SAASA,aAAaA,CAACC,KAAK,EAAEC,WAAW,EAAE;EAChD,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;IAC9B,OAAOA,WAAW,CAACG,KAAK,CAACC,IAAI,IAAIL,KAAK,CAACM,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9D;EACA,OAAOL,KAAK,CAACM,OAAO,CAACL,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1C;AACA,OAAO,MAAMM,cAAc,GAAGA,CAACC,OAAO,EAAEC,aAAa,KAAKC,KAAK,IAAI;EACjE,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAID,KAAK,CAACC,GAAG,KAAK,GAAG,EAAE;IAC9CH,OAAO,CAACE,KAAK,CAAC;;IAEd;IACAA,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBF,KAAK,CAACG,eAAe,CAAC,CAAC;EACzB;EACA,IAAIJ,aAAa,EAAE;IACjBA,aAAa,CAACC,KAAK,CAAC;EACtB;AACF,CAAC;AACD,OAAO,MAAMI,6BAA6B,GAAGC,EAAE,IAAI;EACjDC,UAAU,CAACD,EAAE,EAAE,CAAC,CAAC;AACnB,CAAC;;AAED;AACA,OAAO,MAAME,gBAAgB,GAAGA,CAACC,IAAI,GAAGC,QAAQ,KAAK;EACnD,MAAMC,QAAQ,GAAGF,IAAI,CAACG,aAAa;EACnC,IAAI,CAACD,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EACA,IAAIA,QAAQ,CAACE,UAAU,EAAE;IACvB,OAAOL,gBAAgB,CAACG,QAAQ,CAACE,UAAU,CAAC;EAC9C;EACA,OAAOF,QAAQ;AACjB,CAAC;AACD,OAAO,MAAMG,gCAAgC,GAAG,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}