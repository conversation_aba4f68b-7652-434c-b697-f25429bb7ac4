{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.fireInputEvent = fireInputEvent;\nvar _dom = require(\"@testing-library/dom\");\nvar _utils = require(\"../../utils\");\nfunction fireInputEvent(element, {\n  newValue,\n  newSelectionStart,\n  eventOverrides\n}) {\n  // apply the changes before firing the input event, so that input handlers can access the altered dom and selection\n  if ((0, _utils.isContentEditable)(element)) {\n    applyNative(element, 'textContent', newValue);\n  } else /* istanbul ignore else */\n    if ((0, _utils.isElementType)(element, ['input', 'textarea'])) {\n      applyNative(element, 'value', newValue);\n    } else {\n      // TODO: properly type guard\n      throw new Error('Invalid Element');\n    }\n  setSelectionRangeAfterInput(element, newSelectionStart);\n  _dom.fireEvent.input(element, {\n    ...eventOverrides\n  });\n  setSelectionRangeAfterInputHandler(element, newValue, newSelectionStart);\n}\nfunction setSelectionRangeAfterInput(element, newSelectionStart) {\n  (0, _utils.setSelectionRange)(element, newSelectionStart, newSelectionStart);\n}\nfunction setSelectionRangeAfterInputHandler(element, newValue, newSelectionStart) {\n  const value = (0, _utils.getValue)(element); // don't apply this workaround on elements that don't necessarily report the visible value - e.g. number\n  // TODO: this could probably be only applied when there is keyboardState.carryValue\n\n  const isUnreliableValue = value === '' && (0, _utils.hasUnreliableEmptyValue)(element);\n  if (!isUnreliableValue && value === newValue) {\n    const {\n      selectionStart\n    } = (0, _utils.getSelectionRange)(element);\n    if (selectionStart === value.length) {\n      // The value was changed as expected, but the cursor was moved to the end\n      // TODO: this could probably be only applied when we work around a framework setter on the element in applyNative\n      (0, _utils.setSelectionRange)(element, newSelectionStart, newSelectionStart);\n    }\n  }\n}\nconst initial = Symbol('initial input value/textContent');\nconst onBlur = Symbol('onBlur');\n\n/**\n * React tracks the changes on element properties.\n * This workaround tries to alter the DOM element without React noticing,\n * so that it later picks up the change.\n *\n * @see https://github.com/facebook/react/blob/148f8e497c7d37a3c7ab99f01dec2692427272b1/packages/react-dom/src/client/inputValueTracking.js#L51-L104\n */\nfunction applyNative(element, propName, propValue) {\n  const descriptor = Object.getOwnPropertyDescriptor(element, propName);\n  const nativeDescriptor = Object.getOwnPropertyDescriptor(element.constructor.prototype, propName);\n  if (descriptor && nativeDescriptor) {\n    Object.defineProperty(element, propName, nativeDescriptor);\n  } // Keep track of the initial value to determine if a change event should be dispatched.\n  // CONSTRAINT: We can not determine what happened between focus event and our first API call.\n\n  if (element[initial] === undefined) {\n    element[initial] = String(element[propName]);\n  }\n  element[propName] = propValue; // Add an event listener for the blur event to the capture phase on the window.\n  // CONSTRAINT: Currently there is no cross-platform solution to unshift the event handler stack.\n  // Our change event might occur after other event handlers on the blur event have been processed.\n\n  if (!element[onBlur]) {\n    var _element$ownerDocumen;\n    (_element$ownerDocumen = element.ownerDocument.defaultView) == null ? void 0 : _element$ownerDocumen.addEventListener('blur', element[onBlur] = () => {\n      const initV = element[initial]; // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n\n      delete element[onBlur]; // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n\n      delete element[initial];\n      if (String(element[propName]) !== initV) {\n        _dom.fireEvent.change(element);\n      }\n    }, {\n      capture: true,\n      once: true\n    });\n  }\n  if (descriptor) {\n    Object.defineProperty(element, propName, descriptor);\n  }\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "fireInputEvent", "_dom", "require", "_utils", "element", "newValue", "newSelectionStart", "eventOverrides", "isContentEditable", "applyNative", "isElementType", "Error", "setSelectionRangeAfterInput", "fireEvent", "input", "setSelectionRangeAfterInputHandler", "setSelectionRange", "getValue", "isUnreliableValue", "hasUnreliableEmptyValue", "selectionStart", "getSelectionRange", "length", "initial", "Symbol", "onBlur", "propName", "propValue", "descriptor", "getOwnPropertyDescriptor", "nativeDescriptor", "constructor", "prototype", "undefined", "String", "_element$ownerDocumen", "ownerDocument", "defaultView", "addEventListener", "initV", "change", "capture", "once"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/user-event/dist/keyboard/shared/fireInputEvent.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.fireInputEvent = fireInputEvent;\n\nvar _dom = require(\"@testing-library/dom\");\n\nvar _utils = require(\"../../utils\");\n\nfunction fireInputEvent(element, {\n  newValue,\n  newSelectionStart,\n  eventOverrides\n}) {\n  // apply the changes before firing the input event, so that input handlers can access the altered dom and selection\n  if ((0, _utils.isContentEditable)(element)) {\n    applyNative(element, 'textContent', newValue);\n  } else\n    /* istanbul ignore else */\n    if ((0, _utils.isElementType)(element, ['input', 'textarea'])) {\n      applyNative(element, 'value', newValue);\n    } else {\n      // TODO: properly type guard\n      throw new Error('Invalid Element');\n    }\n\n  setSelectionRangeAfterInput(element, newSelectionStart);\n\n  _dom.fireEvent.input(element, { ...eventOverrides\n  });\n\n  setSelectionRangeAfterInputHandler(element, newValue, newSelectionStart);\n}\n\nfunction setSelectionRangeAfterInput(element, newSelectionStart) {\n  (0, _utils.setSelectionRange)(element, newSelectionStart, newSelectionStart);\n}\n\nfunction setSelectionRangeAfterInputHandler(element, newValue, newSelectionStart) {\n  const value = (0, _utils.getValue)(element); // don't apply this workaround on elements that don't necessarily report the visible value - e.g. number\n  // TODO: this could probably be only applied when there is keyboardState.carryValue\n\n  const isUnreliableValue = value === '' && (0, _utils.hasUnreliableEmptyValue)(element);\n\n  if (!isUnreliableValue && value === newValue) {\n    const {\n      selectionStart\n    } = (0, _utils.getSelectionRange)(element);\n\n    if (selectionStart === value.length) {\n      // The value was changed as expected, but the cursor was moved to the end\n      // TODO: this could probably be only applied when we work around a framework setter on the element in applyNative\n      (0, _utils.setSelectionRange)(element, newSelectionStart, newSelectionStart);\n    }\n  }\n}\n\nconst initial = Symbol('initial input value/textContent');\nconst onBlur = Symbol('onBlur');\n\n/**\n * React tracks the changes on element properties.\n * This workaround tries to alter the DOM element without React noticing,\n * so that it later picks up the change.\n *\n * @see https://github.com/facebook/react/blob/148f8e497c7d37a3c7ab99f01dec2692427272b1/packages/react-dom/src/client/inputValueTracking.js#L51-L104\n */\nfunction applyNative(element, propName, propValue) {\n  const descriptor = Object.getOwnPropertyDescriptor(element, propName);\n  const nativeDescriptor = Object.getOwnPropertyDescriptor(element.constructor.prototype, propName);\n\n  if (descriptor && nativeDescriptor) {\n    Object.defineProperty(element, propName, nativeDescriptor);\n  } // Keep track of the initial value to determine if a change event should be dispatched.\n  // CONSTRAINT: We can not determine what happened between focus event and our first API call.\n\n\n  if (element[initial] === undefined) {\n    element[initial] = String(element[propName]);\n  }\n\n  element[propName] = propValue; // Add an event listener for the blur event to the capture phase on the window.\n  // CONSTRAINT: Currently there is no cross-platform solution to unshift the event handler stack.\n  // Our change event might occur after other event handlers on the blur event have been processed.\n\n  if (!element[onBlur]) {\n    var _element$ownerDocumen;\n\n    (_element$ownerDocumen = element.ownerDocument.defaultView) == null ? void 0 : _element$ownerDocumen.addEventListener('blur', element[onBlur] = () => {\n      const initV = element[initial]; // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n\n      delete element[onBlur]; // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n\n      delete element[initial];\n\n      if (String(element[propName]) !== initV) {\n        _dom.fireEvent.change(element);\n      }\n    }, {\n      capture: true,\n      once: true\n    });\n  }\n\n  if (descriptor) {\n    Object.defineProperty(element, propName, descriptor);\n  }\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,cAAc,GAAGA,cAAc;AAEvC,IAAIC,IAAI,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAE1C,IAAIC,MAAM,GAAGD,OAAO,CAAC,aAAa,CAAC;AAEnC,SAASF,cAAcA,CAACI,OAAO,EAAE;EAC/BC,QAAQ;EACRC,iBAAiB;EACjBC;AACF,CAAC,EAAE;EACD;EACA,IAAI,CAAC,CAAC,EAAEJ,MAAM,CAACK,iBAAiB,EAAEJ,OAAO,CAAC,EAAE;IAC1CK,WAAW,CAACL,OAAO,EAAE,aAAa,EAAEC,QAAQ,CAAC;EAC/C,CAAC,MACC;IACA,IAAI,CAAC,CAAC,EAAEF,MAAM,CAACO,aAAa,EAAEN,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;MAC7DK,WAAW,CAACL,OAAO,EAAE,OAAO,EAAEC,QAAQ,CAAC;IACzC,CAAC,MAAM;MACL;MACA,MAAM,IAAIM,KAAK,CAAC,iBAAiB,CAAC;IACpC;EAEFC,2BAA2B,CAACR,OAAO,EAAEE,iBAAiB,CAAC;EAEvDL,IAAI,CAACY,SAAS,CAACC,KAAK,CAACV,OAAO,EAAE;IAAE,GAAGG;EACnC,CAAC,CAAC;EAEFQ,kCAAkC,CAACX,OAAO,EAAEC,QAAQ,EAAEC,iBAAiB,CAAC;AAC1E;AAEA,SAASM,2BAA2BA,CAACR,OAAO,EAAEE,iBAAiB,EAAE;EAC/D,CAAC,CAAC,EAAEH,MAAM,CAACa,iBAAiB,EAAEZ,OAAO,EAAEE,iBAAiB,EAAEA,iBAAiB,CAAC;AAC9E;AAEA,SAASS,kCAAkCA,CAACX,OAAO,EAAEC,QAAQ,EAAEC,iBAAiB,EAAE;EAChF,MAAMP,KAAK,GAAG,CAAC,CAAC,EAAEI,MAAM,CAACc,QAAQ,EAAEb,OAAO,CAAC,CAAC,CAAC;EAC7C;;EAEA,MAAMc,iBAAiB,GAAGnB,KAAK,KAAK,EAAE,IAAI,CAAC,CAAC,EAAEI,MAAM,CAACgB,uBAAuB,EAAEf,OAAO,CAAC;EAEtF,IAAI,CAACc,iBAAiB,IAAInB,KAAK,KAAKM,QAAQ,EAAE;IAC5C,MAAM;MACJe;IACF,CAAC,GAAG,CAAC,CAAC,EAAEjB,MAAM,CAACkB,iBAAiB,EAAEjB,OAAO,CAAC;IAE1C,IAAIgB,cAAc,KAAKrB,KAAK,CAACuB,MAAM,EAAE;MACnC;MACA;MACA,CAAC,CAAC,EAAEnB,MAAM,CAACa,iBAAiB,EAAEZ,OAAO,EAAEE,iBAAiB,EAAEA,iBAAiB,CAAC;IAC9E;EACF;AACF;AAEA,MAAMiB,OAAO,GAAGC,MAAM,CAAC,iCAAiC,CAAC;AACzD,MAAMC,MAAM,GAAGD,MAAM,CAAC,QAAQ,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASf,WAAWA,CAACL,OAAO,EAAEsB,QAAQ,EAAEC,SAAS,EAAE;EACjD,MAAMC,UAAU,GAAGhC,MAAM,CAACiC,wBAAwB,CAACzB,OAAO,EAAEsB,QAAQ,CAAC;EACrE,MAAMI,gBAAgB,GAAGlC,MAAM,CAACiC,wBAAwB,CAACzB,OAAO,CAAC2B,WAAW,CAACC,SAAS,EAAEN,QAAQ,CAAC;EAEjG,IAAIE,UAAU,IAAIE,gBAAgB,EAAE;IAClClC,MAAM,CAACC,cAAc,CAACO,OAAO,EAAEsB,QAAQ,EAAEI,gBAAgB,CAAC;EAC5D,CAAC,CAAC;EACF;;EAGA,IAAI1B,OAAO,CAACmB,OAAO,CAAC,KAAKU,SAAS,EAAE;IAClC7B,OAAO,CAACmB,OAAO,CAAC,GAAGW,MAAM,CAAC9B,OAAO,CAACsB,QAAQ,CAAC,CAAC;EAC9C;EAEAtB,OAAO,CAACsB,QAAQ,CAAC,GAAGC,SAAS,CAAC,CAAC;EAC/B;EACA;;EAEA,IAAI,CAACvB,OAAO,CAACqB,MAAM,CAAC,EAAE;IACpB,IAAIU,qBAAqB;IAEzB,CAACA,qBAAqB,GAAG/B,OAAO,CAACgC,aAAa,CAACC,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,qBAAqB,CAACG,gBAAgB,CAAC,MAAM,EAAElC,OAAO,CAACqB,MAAM,CAAC,GAAG,MAAM;MACpJ,MAAMc,KAAK,GAAGnC,OAAO,CAACmB,OAAO,CAAC,CAAC,CAAC;;MAEhC,OAAOnB,OAAO,CAACqB,MAAM,CAAC,CAAC,CAAC;;MAExB,OAAOrB,OAAO,CAACmB,OAAO,CAAC;MAEvB,IAAIW,MAAM,CAAC9B,OAAO,CAACsB,QAAQ,CAAC,CAAC,KAAKa,KAAK,EAAE;QACvCtC,IAAI,CAACY,SAAS,CAAC2B,MAAM,CAACpC,OAAO,CAAC;MAChC;IACF,CAAC,EAAE;MACDqC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;EAEA,IAAId,UAAU,EAAE;IACdhC,MAAM,CAACC,cAAc,CAACO,OAAO,EAAEsB,QAAQ,EAAEE,UAAU,CAAC;EACtD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}