{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.keydownBehavior = void 0;\nvar _utils = require(\"../../utils\");\nvar _shared = require(\"../shared\");\n\n/**\n * This file should contain behavior for arrow keys as described here:\n * https://w3c.github.io/uievents-code/#key-controlpad-section\n */\nconst keydownBehavior = [{\n  matches: (keyDef, element) => (keyDef.key === 'Home' || keyDef.key === 'End') && ((0, _utils.isElementType)(element, ['input', 'textarea']) || (0, _utils.isContentEditable)(element)),\n  handle: (keyDef, element) => {\n    // This could probably been improved by collapsing a selection range\n    if (keyDef.key === 'Home') {\n      (0, _utils.setSelectionRange)(element, 0, 0);\n    } else {\n      var _getValue$length, _getValue;\n      const newPos = (_getValue$length = (_getValue = (0, _utils.getValue)(element)) == null ? void 0 : _getValue.length) != null ? _getValue$length : /* istanbul ignore next */\n      0;\n      (0, _utils.setSelectionRange)(element, newPos, newPos);\n    }\n  }\n}, {\n  matches: (keyDef, element) => (keyDef.key === 'PageUp' || keyDef.key === 'PageDown') && (0, _utils.isElementType)(element, ['input']),\n  handle: (keyDef, element) => {\n    // This could probably been improved by collapsing a selection range\n    if (keyDef.key === 'PageUp') {\n      (0, _utils.setSelectionRange)(element, 0, 0);\n    } else {\n      var _getValue$length2, _getValue2;\n      const newPos = (_getValue$length2 = (_getValue2 = (0, _utils.getValue)(element)) == null ? void 0 : _getValue2.length) != null ? _getValue$length2 : /* istanbul ignore next */\n      0;\n      (0, _utils.setSelectionRange)(element, newPos, newPos);\n    }\n  }\n}, {\n  matches: (keyDef, element) => keyDef.key === 'Delete' && (0, _utils.isEditable)(element) && !(0, _utils.isCursorAtEnd)(element),\n  handle: (keDef, element, options, state) => {\n    const {\n      newValue,\n      newSelectionStart\n    } = (0, _utils.calculateNewValue)('', element, state.carryValue, undefined, 'forward');\n    (0, _shared.fireInputEvent)(element, {\n      newValue,\n      newSelectionStart,\n      eventOverrides: {\n        inputType: 'deleteContentForward'\n      }\n    });\n    (0, _shared.carryValue)(element, state, newValue);\n  }\n}];\nexports.keydownBehavior = keydownBehavior;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "keydownBehavior", "_utils", "require", "_shared", "matches", "keyDef", "element", "key", "isElementType", "isContentEditable", "handle", "setSelectionRange", "_getValue$length", "_getValue", "newPos", "getValue", "length", "_getValue$length2", "_getValue2", "isEditable", "isCursorAtEnd", "keDef", "options", "state", "newValue", "newSelectionStart", "calculateNewValue", "carryValue", "undefined", "fireInputEvent", "eventOverrides", "inputType"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/user-event/dist/keyboard/plugins/control.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.keydownBehavior = void 0;\n\nvar _utils = require(\"../../utils\");\n\nvar _shared = require(\"../shared\");\n\n/**\n * This file should contain behavior for arrow keys as described here:\n * https://w3c.github.io/uievents-code/#key-controlpad-section\n */\nconst keydownBehavior = [{\n  matches: (keyDef, element) => (keyDef.key === 'Home' || keyDef.key === 'End') && ((0, _utils.isElementType)(element, ['input', 'textarea']) || (0, _utils.isContentEditable)(element)),\n  handle: (keyDef, element) => {\n    // This could probably been improved by collapsing a selection range\n    if (keyDef.key === 'Home') {\n      (0, _utils.setSelectionRange)(element, 0, 0);\n    } else {\n      var _getValue$length, _getValue;\n\n      const newPos = (_getValue$length = (_getValue = (0, _utils.getValue)(element)) == null ? void 0 : _getValue.length) != null ? _getValue$length :\n      /* istanbul ignore next */\n      0;\n      (0, _utils.setSelectionRange)(element, newPos, newPos);\n    }\n  }\n}, {\n  matches: (keyDef, element) => (keyDef.key === 'PageUp' || keyDef.key === 'PageDown') && (0, _utils.isElementType)(element, ['input']),\n  handle: (keyDef, element) => {\n    // This could probably been improved by collapsing a selection range\n    if (keyDef.key === 'PageUp') {\n      (0, _utils.setSelectionRange)(element, 0, 0);\n    } else {\n      var _getValue$length2, _getValue2;\n\n      const newPos = (_getValue$length2 = (_getValue2 = (0, _utils.getValue)(element)) == null ? void 0 : _getValue2.length) != null ? _getValue$length2 :\n      /* istanbul ignore next */\n      0;\n      (0, _utils.setSelectionRange)(element, newPos, newPos);\n    }\n  }\n}, {\n  matches: (keyDef, element) => keyDef.key === 'Delete' && (0, _utils.isEditable)(element) && !(0, _utils.isCursorAtEnd)(element),\n  handle: (keDef, element, options, state) => {\n    const {\n      newValue,\n      newSelectionStart\n    } = (0, _utils.calculateNewValue)('', element, state.carryValue, undefined, 'forward');\n    (0, _shared.fireInputEvent)(element, {\n      newValue,\n      newSelectionStart,\n      eventOverrides: {\n        inputType: 'deleteContentForward'\n      }\n    });\n    (0, _shared.carryValue)(element, state, newValue);\n  }\n}];\nexports.keydownBehavior = keydownBehavior;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,eAAe,GAAG,KAAK,CAAC;AAEhC,IAAIC,MAAM,GAAGC,OAAO,CAAC,aAAa,CAAC;AAEnC,IAAIC,OAAO,GAAGD,OAAO,CAAC,WAAW,CAAC;;AAElC;AACA;AACA;AACA;AACA,MAAMF,eAAe,GAAG,CAAC;EACvBI,OAAO,EAAEA,CAACC,MAAM,EAAEC,OAAO,KAAK,CAACD,MAAM,CAACE,GAAG,KAAK,MAAM,IAAIF,MAAM,CAACE,GAAG,KAAK,KAAK,MAAM,CAAC,CAAC,EAAEN,MAAM,CAACO,aAAa,EAAEF,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEL,MAAM,CAACQ,iBAAiB,EAAEH,OAAO,CAAC,CAAC;EACtLI,MAAM,EAAEA,CAACL,MAAM,EAAEC,OAAO,KAAK;IAC3B;IACA,IAAID,MAAM,CAACE,GAAG,KAAK,MAAM,EAAE;MACzB,CAAC,CAAC,EAAEN,MAAM,CAACU,iBAAiB,EAAEL,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9C,CAAC,MAAM;MACL,IAAIM,gBAAgB,EAAEC,SAAS;MAE/B,MAAMC,MAAM,GAAG,CAACF,gBAAgB,GAAG,CAACC,SAAS,GAAG,CAAC,CAAC,EAAEZ,MAAM,CAACc,QAAQ,EAAET,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGO,SAAS,CAACG,MAAM,KAAK,IAAI,GAAGJ,gBAAgB,GAC9I;MACA,CAAC;MACD,CAAC,CAAC,EAAEX,MAAM,CAACU,iBAAiB,EAAEL,OAAO,EAAEQ,MAAM,EAAEA,MAAM,CAAC;IACxD;EACF;AACF,CAAC,EAAE;EACDV,OAAO,EAAEA,CAACC,MAAM,EAAEC,OAAO,KAAK,CAACD,MAAM,CAACE,GAAG,KAAK,QAAQ,IAAIF,MAAM,CAACE,GAAG,KAAK,UAAU,KAAK,CAAC,CAAC,EAAEN,MAAM,CAACO,aAAa,EAAEF,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;EACrII,MAAM,EAAEA,CAACL,MAAM,EAAEC,OAAO,KAAK;IAC3B;IACA,IAAID,MAAM,CAACE,GAAG,KAAK,QAAQ,EAAE;MAC3B,CAAC,CAAC,EAAEN,MAAM,CAACU,iBAAiB,EAAEL,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9C,CAAC,MAAM;MACL,IAAIW,iBAAiB,EAAEC,UAAU;MAEjC,MAAMJ,MAAM,GAAG,CAACG,iBAAiB,GAAG,CAACC,UAAU,GAAG,CAAC,CAAC,EAAEjB,MAAM,CAACc,QAAQ,EAAET,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGY,UAAU,CAACF,MAAM,KAAK,IAAI,GAAGC,iBAAiB,GAClJ;MACA,CAAC;MACD,CAAC,CAAC,EAAEhB,MAAM,CAACU,iBAAiB,EAAEL,OAAO,EAAEQ,MAAM,EAAEA,MAAM,CAAC;IACxD;EACF;AACF,CAAC,EAAE;EACDV,OAAO,EAAEA,CAACC,MAAM,EAAEC,OAAO,KAAKD,MAAM,CAACE,GAAG,KAAK,QAAQ,IAAI,CAAC,CAAC,EAAEN,MAAM,CAACkB,UAAU,EAAEb,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEL,MAAM,CAACmB,aAAa,EAAEd,OAAO,CAAC;EAC/HI,MAAM,EAAEA,CAACW,KAAK,EAAEf,OAAO,EAAEgB,OAAO,EAAEC,KAAK,KAAK;IAC1C,MAAM;MACJC,QAAQ;MACRC;IACF,CAAC,GAAG,CAAC,CAAC,EAAExB,MAAM,CAACyB,iBAAiB,EAAE,EAAE,EAAEpB,OAAO,EAAEiB,KAAK,CAACI,UAAU,EAAEC,SAAS,EAAE,SAAS,CAAC;IACtF,CAAC,CAAC,EAAEzB,OAAO,CAAC0B,cAAc,EAAEvB,OAAO,EAAE;MACnCkB,QAAQ;MACRC,iBAAiB;MACjBK,cAAc,EAAE;QACdC,SAAS,EAAE;MACb;IACF,CAAC,CAAC;IACF,CAAC,CAAC,EAAE5B,OAAO,CAACwB,UAAU,EAAErB,OAAO,EAAEiB,KAAK,EAAEC,QAAQ,CAAC;EACnD;AACF,CAAC,CAAC;AACF1B,OAAO,CAACE,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}