{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\IdeaProjects\\\\FoodOrdering-main\\\\FoodOrdering-main\\\\frontend\\\\src\\\\Components\\\\Restaurant\\\\RestaurantCard.jsx\",\n  _s = $RefreshSig$();\nimport { Card, Chip, IconButton } from \"@mui/material\";\nimport React, { useState } from \"react\";\nimport FavoriteBorderIcon from \"@mui/icons-material/FavoriteBorder\";\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { addToFavorite } from \"../State/Auth/Action\";\nimport { isPresentInFavorites } from \"../Config/config\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const RestaurantCard = ({\n  item\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const jwt = localStorage.getItem(\"token\");\n  const {\n    auth\n  } = useSelector(store => store);\n  const handleAddToFavorite = () => {\n    dispatch(addToFavorite({\n      token: jwt,\n      restaurantId: item.id\n    }));\n  };\n  const handleNavigateToRestaurant = () => {\n    console.log(item.address.city, item.name, item.id);\n    //Eğer restaurant açıksa adrese gidiyor kapalı ise gitmez\n    if (item.open || !item.open) {\n      navigate(`/restaurant/${item.address.city}/${item.name}/${item.id}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"w-[18rem]\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${true ? \"cursor-pointer \" : \"cursor-not-allowed\"} relative`,\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        className: \"w-full h-[1prem] rounded-t-md object-cover\",\n        src: item.images[0],\n        alt: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        size: \"small\",\n        className: \"absolute top-2 left-2\",\n        color: item.open ? \"success\" : \"error\",\n        label: item.open ? \"open\" : \"closed\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 textPart lg:flex w-full justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          onClick: handleNavigateToRestaurant,\n          className: \"font-semibold text-lg cursor-pointer\",\n          children: item.name || item.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm\",\n          children: item.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleAddToFavorite,\n          children: isPresentInFavorites(auth.favorites, item) ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 58\n          }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 77\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_s(RestaurantCard, \"QwScIm/j+ZI+QhI+xvClI9dysJo=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = RestaurantCard;\nexport default RestaurantCard;\nvar _c;\n$RefreshReg$(_c, \"RestaurantCard\");", "map": {"version": 3, "names": ["Card", "Chip", "IconButton", "React", "useState", "FavoriteBorderIcon", "FavoriteIcon", "useNavigate", "useDispatch", "useSelector", "addToFavorite", "isPresentInFavorites", "jsxDEV", "_jsxDEV", "RestaurantCard", "item", "_s", "navigate", "dispatch", "jwt", "localStorage", "getItem", "auth", "store", "handleAddToFavorite", "token", "restaurantId", "id", "handleNavigateToRestaurant", "console", "log", "address", "city", "name", "open", "className", "children", "src", "images", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "color", "label", "onClick", "title", "description", "favorites", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/src/Components/Restaurant/RestaurantCard.jsx"], "sourcesContent": ["import { Card, Chip, IconButton } from \"@mui/material\";\nimport React, { useState } from \"react\";\nimport FavoriteBorderIcon from \"@mui/icons-material/FavoriteBorder\";\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { addToFavorite } from \"../State/Auth/Action\";\nimport { isPresentInFavorites } from \"../Config/config\";\nexport const RestaurantCard = ({ item }) => {\n\n    const navigate = useNavigate()\n    const dispatch = useDispatch()\n\n    const jwt = localStorage.getItem(\"token\")\n\n    const {auth} = useSelector(store=>store)\n\n    const handleAddToFavorite =()=> {\n        dispatch(addToFavorite({token: jwt, restaurantId: item.id}));\n\n    }\n\n    const handleNavigateToRestaurant = () => {\n        console.log(item.address.city, item.name, item.id);\n        //Eğer restaurant açıksa adrese gidiyor kapalı ise gitmez\n        if (item.open || !item.open) {\n          navigate(`/restaurant/${item.address.city}/${item.name}/${item.id}`);\n        }\n      };\n      \n  return (\n    <Card className=\"w-[18rem]\">\n      <div\n        className={`${\n          true ? \"cursor-pointer \" : \"cursor-not-allowed\"\n        } relative`}\n      >\n        <img\n          className=\"w-full h-[1prem] rounded-t-md object-cover\"\n          src={item.images[0]}\n          alt=\"\"\n        />\n\n        <Chip\n          size=\"small\"\n          className=\"absolute top-2 left-2\"\n          color={item.open ? \"success\" : \"error\"}\n          label={item.open ? \"open\" : \"closed\"}\n        />\n      </div>\n\n      <div className=\"p-4 textPart lg:flex w-full justify-between\">\n        <div className=\"space-y-1\">\n          <p onClick={handleNavigateToRestaurant} className=\"font-semibold text-lg cursor-pointer\">{item.name || item.title}</p>\n          <p className=\"text-gray-400 text-sm\">\n            {item.description}\n          </p>\n        </div>\n\n        <div className=\"\">\n          <IconButton onClick={handleAddToFavorite}>\n            {isPresentInFavorites(auth.favorites,item) ? <FavoriteIcon /> : <FavoriteBorderIcon />}\n          </IconButton>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default RestaurantCard;\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,UAAU,QAAQ,eAAe;AACtD,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,oBAAoB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACxD,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAExC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,GAAG,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAEzC,MAAM;IAACC;EAAI,CAAC,GAAGb,WAAW,CAACc,KAAK,IAAEA,KAAK,CAAC;EAExC,MAAMC,mBAAmB,GAAEA,CAAA,KAAK;IAC5BN,QAAQ,CAACR,aAAa,CAAC;MAACe,KAAK,EAAEN,GAAG;MAAEO,YAAY,EAAEX,IAAI,CAACY;IAAE,CAAC,CAAC,CAAC;EAEhE,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACrCC,OAAO,CAACC,GAAG,CAACf,IAAI,CAACgB,OAAO,CAACC,IAAI,EAAEjB,IAAI,CAACkB,IAAI,EAAElB,IAAI,CAACY,EAAE,CAAC;IAClD;IACA,IAAIZ,IAAI,CAACmB,IAAI,IAAI,CAACnB,IAAI,CAACmB,IAAI,EAAE;MAC3BjB,QAAQ,CAAC,eAAeF,IAAI,CAACgB,OAAO,CAACC,IAAI,IAAIjB,IAAI,CAACkB,IAAI,IAAIlB,IAAI,CAACY,EAAE,EAAE,CAAC;IACtE;EACF,CAAC;EAEL,oBACEd,OAAA,CAACb,IAAI;IAACmC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACzBvB,OAAA;MACEsB,SAAS,EAAE,GACT,IAAI,GAAG,iBAAiB,GAAG,oBAAoB,WACrC;MAAAC,QAAA,gBAEZvB,OAAA;QACEsB,SAAS,EAAC,4CAA4C;QACtDE,GAAG,EAAEtB,IAAI,CAACuB,MAAM,CAAC,CAAC,CAAE;QACpBC,GAAG,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAEF9B,OAAA,CAACZ,IAAI;QACH2C,IAAI,EAAC,OAAO;QACZT,SAAS,EAAC,uBAAuB;QACjCU,KAAK,EAAE9B,IAAI,CAACmB,IAAI,GAAG,SAAS,GAAG,OAAQ;QACvCY,KAAK,EAAE/B,IAAI,CAACmB,IAAI,GAAG,MAAM,GAAG;MAAS;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN9B,OAAA;MAAKsB,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1DvB,OAAA;QAAKsB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBvB,OAAA;UAAGkC,OAAO,EAAEnB,0BAA2B;UAACO,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAErB,IAAI,CAACkB,IAAI,IAAIlB,IAAI,CAACiC;QAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtH9B,OAAA;UAAGsB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EACjCrB,IAAI,CAACkC;QAAW;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN9B,OAAA;QAAKsB,SAAS,EAAC,EAAE;QAAAC,QAAA,eACfvB,OAAA,CAACX,UAAU;UAAC6C,OAAO,EAAEvB,mBAAoB;UAAAY,QAAA,EACtCzB,oBAAoB,CAACW,IAAI,CAAC4B,SAAS,EAACnC,IAAI,CAAC,gBAAGF,OAAA,CAACP,YAAY;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG9B,OAAA,CAACR,kBAAkB;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAAC3B,EAAA,CA3DWF,cAAc;EAAA,QAENP,WAAW,EACXC,WAAW,EAIbC,WAAW;AAAA;AAAA0C,EAAA,GAPjBrC,cAAc;AA6D3B,eAAeA,cAAc;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}