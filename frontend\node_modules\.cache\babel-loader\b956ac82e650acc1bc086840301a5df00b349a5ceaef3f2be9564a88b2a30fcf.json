{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar sectionheadRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure']]\n};\nvar _default = sectionheadRole;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "sectionheadRole", "abstract", "accessibleNameRequired", "baseConcepts", "childrenPresentational", "nameFrom", "prohibitedProps", "props", "relatedConcepts", "requireContextRole", "requiredContextRole", "requiredOwnedElements", "requiredProps", "superClass", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/etc/roles/abstract/sectionheadRole.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar sectionheadRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure']]\n};\nvar _default = sectionheadRole;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,eAAe,GAAG;EACpBC,QAAQ,EAAE,IAAI;EACdC,sBAAsB,EAAE,KAAK;EAC7BC,YAAY,EAAE,EAAE;EAChBC,sBAAsB,EAAE,KAAK;EAC7BC,QAAQ,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;EAChCC,eAAe,EAAE,EAAE;EACnBC,KAAK,EAAE,CAAC,CAAC;EACTC,eAAe,EAAE,EAAE;EACnBC,kBAAkB,EAAE,EAAE;EACtBC,mBAAmB,EAAE,EAAE;EACvBC,qBAAqB,EAAE,EAAE;EACzBC,aAAa,EAAE,CAAC,CAAC;EACjBC,UAAU,EAAE,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC;AACxC,CAAC;AACD,IAAIC,QAAQ,GAAGd,eAAe;AAC9BH,OAAO,CAACE,OAAO,GAAGe,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}