{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"elementType\", \"externalSlotProps\", \"ownerState\", \"skipResolvingSlotProps\"];\nimport useForkRef from '../useForkRef';\nimport appendOwnerState from '../appendOwnerState';\nimport mergeSlotProps from '../mergeSlotProps';\nimport resolveComponentProps from '../resolveComponentProps';\n/**\n * @ignore - do not document.\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nfunction useSlotProps(parameters) {\n  var _parameters$additiona;\n  const {\n      elementType,\n      externalSlotProps,\n      ownerState,\n      skipResolvingSlotProps = false\n    } = parameters,\n    rest = _objectWithoutPropertiesLoose(parameters, _excluded);\n  const resolvedComponentsProps = skipResolvingSlotProps ? {} : resolveComponentProps(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = mergeSlotProps(_extends({}, rest, {\n    externalSlotProps: resolvedComponentsProps\n  }));\n  const ref = useForkRef(internalRef, resolvedComponentsProps == null ? void 0 : resolvedComponentsProps.ref, (_parameters$additiona = parameters.additionalProps) == null ? void 0 : _parameters$additiona.ref);\n  const props = appendOwnerState(elementType, _extends({}, mergedProps, {\n    ref\n  }), ownerState);\n  return props;\n}\nexport default useSlotProps;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "useForkRef", "appendOwnerState", "mergeSlotProps", "resolveComponentProps", "useSlotProps", "parameters", "_parameters$additiona", "elementType", "externalSlotProps", "ownerState", "skipResolvingSlotProps", "rest", "resolvedComponentsProps", "props", "mergedProps", "internalRef", "ref", "additionalProps"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"elementType\", \"externalSlotProps\", \"ownerState\", \"skipResolvingSlotProps\"];\nimport useForkRef from '../useForkRef';\nimport appendOwnerState from '../appendOwnerState';\nimport mergeSlotProps from '../mergeSlotProps';\nimport resolveComponentProps from '../resolveComponentProps';\n/**\n * @ignore - do not document.\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nfunction useSlotProps(parameters) {\n  var _parameters$additiona;\n  const {\n      elementType,\n      externalSlotProps,\n      ownerState,\n      skipResolvingSlotProps = false\n    } = parameters,\n    rest = _objectWithoutPropertiesLoose(parameters, _excluded);\n  const resolvedComponentsProps = skipResolvingSlotProps ? {} : resolveComponentProps(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = mergeSlotProps(_extends({}, rest, {\n    externalSlotProps: resolvedComponentsProps\n  }));\n  const ref = useForkRef(internalRef, resolvedComponentsProps == null ? void 0 : resolvedComponentsProps.ref, (_parameters$additiona = parameters.additionalProps) == null ? void 0 : _parameters$additiona.ref);\n  const props = appendOwnerState(elementType, _extends({}, mergedProps, {\n    ref\n  }), ownerState);\n  return props;\n}\nexport default useSlotProps;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,aAAa,EAAE,mBAAmB,EAAE,YAAY,EAAE,wBAAwB,CAAC;AAC9F,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,qBAAqB,MAAM,0BAA0B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,UAAU,EAAE;EAChC,IAAIC,qBAAqB;EACzB,MAAM;MACFC,WAAW;MACXC,iBAAiB;MACjBC,UAAU;MACVC,sBAAsB,GAAG;IAC3B,CAAC,GAAGL,UAAU;IACdM,IAAI,GAAGb,6BAA6B,CAACO,UAAU,EAAEN,SAAS,CAAC;EAC7D,MAAMa,uBAAuB,GAAGF,sBAAsB,GAAG,CAAC,CAAC,GAAGP,qBAAqB,CAACK,iBAAiB,EAAEC,UAAU,CAAC;EAClH,MAAM;IACJI,KAAK,EAAEC,WAAW;IAClBC;EACF,CAAC,GAAGb,cAAc,CAACL,QAAQ,CAAC,CAAC,CAAC,EAAEc,IAAI,EAAE;IACpCH,iBAAiB,EAAEI;EACrB,CAAC,CAAC,CAAC;EACH,MAAMI,GAAG,GAAGhB,UAAU,CAACe,WAAW,EAAEH,uBAAuB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,uBAAuB,CAACI,GAAG,EAAE,CAACV,qBAAqB,GAAGD,UAAU,CAACY,eAAe,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGX,qBAAqB,CAACU,GAAG,CAAC;EAC9M,MAAMH,KAAK,GAAGZ,gBAAgB,CAACM,WAAW,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAEiB,WAAW,EAAE;IACpEE;EACF,CAAC,CAAC,EAAEP,UAAU,CAAC;EACf,OAAOI,KAAK;AACd;AACA,eAAeT,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}