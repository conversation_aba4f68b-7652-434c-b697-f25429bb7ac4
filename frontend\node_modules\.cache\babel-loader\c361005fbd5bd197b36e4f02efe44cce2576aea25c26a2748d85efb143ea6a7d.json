{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_advancedFormat = t();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, t) {\n    var r = t.prototype,\n      n = r.format;\n    r.format = function (e) {\n      var t = this,\n        r = this.$locale();\n      if (!this.isValid()) return n.bind(this)(e);\n      var s = this.$utils(),\n        a = (e || \"YYYY-MM-DDTHH:mm:ssZ\").replace(/\\[([^\\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g, function (e) {\n          switch (e) {\n            case \"Q\":\n              return Math.ceil((t.$M + 1) / 3);\n            case \"Do\":\n              return r.ordinal(t.$D);\n            case \"gggg\":\n              return t.weekYear();\n            case \"GGGG\":\n              return t.isoWeekYear();\n            case \"wo\":\n              return r.ordinal(t.week(), \"W\");\n            case \"w\":\n            case \"ww\":\n              return s.s(t.week(), \"w\" === e ? 1 : 2, \"0\");\n            case \"W\":\n            case \"WW\":\n              return s.s(t.isoWeek(), \"W\" === e ? 1 : 2, \"0\");\n            case \"k\":\n            case \"kk\":\n              return s.s(String(0 === t.$H ? 24 : t.$H), \"k\" === e ? 1 : 2, \"0\");\n            case \"X\":\n              return Math.floor(t.$d.getTime() / 1e3);\n            case \"x\":\n              return t.$d.getTime();\n            case \"z\":\n              return \"[\" + t.offsetName() + \"]\";\n            case \"zzz\":\n              return \"[\" + t.offsetName(\"long\") + \"]\";\n            default:\n              return e;\n          }\n        });\n      return n.bind(this)(a);\n    };\n  };\n});", "map": {"version": 3, "names": ["e", "t", "exports", "module", "define", "amd", "globalThis", "self", "dayjs_plugin_advancedFormat", "r", "prototype", "n", "format", "$locale", "<PERSON><PERSON><PERSON><PERSON>", "bind", "s", "$utils", "a", "replace", "Math", "ceil", "$M", "ordinal", "$D", "weekYear", "isoWeekYear", "week", "isoWeek", "String", "$H", "floor", "$d", "getTime", "offsetName"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/dayjs/plugin/advancedFormat.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_advancedFormat=t()}(this,(function(){\"use strict\";return function(e,t){var r=t.prototype,n=r.format;r.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return n.bind(this)(e);var s=this.$utils(),a=(e||\"YYYY-MM-DDTHH:mm:ssZ\").replace(/\\[([^\\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,(function(e){switch(e){case\"Q\":return Math.ceil((t.$M+1)/3);case\"Do\":return r.ordinal(t.$D);case\"gggg\":return t.weekYear();case\"GGGG\":return t.isoWeekYear();case\"wo\":return r.ordinal(t.week(),\"W\");case\"w\":case\"ww\":return s.s(t.week(),\"w\"===e?1:2,\"0\");case\"W\":case\"WW\":return s.s(t.isoWeek(),\"W\"===e?1:2,\"0\");case\"k\":case\"kk\":return s.s(String(0===t.$H?24:t.$H),\"k\"===e?1:2,\"0\");case\"X\":return Math.floor(t.$d.getTime()/1e3);case\"x\":return t.$d.getTime();case\"z\":return\"[\"+t.offsetName()+\"]\";case\"zzz\":return\"[\"+t.offsetName(\"long\")+\"]\";default:return e}}));return n.bind(this)(a)}}}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAACH,CAAC,CAAC,GAAC,CAACD,CAAC,GAAC,WAAW,IAAE,OAAOM,UAAU,GAACA,UAAU,GAACN,CAAC,IAAEO,IAAI,EAAEC,2BAA2B,GAACP,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC,IAAI,EAAE,YAAU;EAAC,YAAY;;EAAC,OAAO,UAASD,CAAC,EAACC,CAAC,EAAC;IAAC,IAAIQ,CAAC,GAACR,CAAC,CAACS,SAAS;MAACC,CAAC,GAACF,CAAC,CAACG,MAAM;IAACH,CAAC,CAACG,MAAM,GAAC,UAASZ,CAAC,EAAC;MAAC,IAAIC,CAAC,GAAC,IAAI;QAACQ,CAAC,GAAC,IAAI,CAACI,OAAO,CAAC,CAAC;MAAC,IAAG,CAAC,IAAI,CAACC,OAAO,CAAC,CAAC,EAAC,OAAOH,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,CAACf,CAAC,CAAC;MAAC,IAAIgB,CAAC,GAAC,IAAI,CAACC,MAAM,CAAC,CAAC;QAACC,CAAC,GAAC,CAAClB,CAAC,IAAE,sBAAsB,EAAEmB,OAAO,CAAC,6DAA6D,EAAE,UAASnB,CAAC,EAAC;UAAC,QAAOA,CAAC;YAAE,KAAI,GAAG;cAAC,OAAOoB,IAAI,CAACC,IAAI,CAAC,CAACpB,CAAC,CAACqB,EAAE,GAAC,CAAC,IAAE,CAAC,CAAC;YAAC,KAAI,IAAI;cAAC,OAAOb,CAAC,CAACc,OAAO,CAACtB,CAAC,CAACuB,EAAE,CAAC;YAAC,KAAI,MAAM;cAAC,OAAOvB,CAAC,CAACwB,QAAQ,CAAC,CAAC;YAAC,KAAI,MAAM;cAAC,OAAOxB,CAAC,CAACyB,WAAW,CAAC,CAAC;YAAC,KAAI,IAAI;cAAC,OAAOjB,CAAC,CAACc,OAAO,CAACtB,CAAC,CAAC0B,IAAI,CAAC,CAAC,EAAC,GAAG,CAAC;YAAC,KAAI,GAAG;YAAC,KAAI,IAAI;cAAC,OAAOX,CAAC,CAACA,CAAC,CAACf,CAAC,CAAC0B,IAAI,CAAC,CAAC,EAAC,GAAG,KAAG3B,CAAC,GAAC,CAAC,GAAC,CAAC,EAAC,GAAG,CAAC;YAAC,KAAI,GAAG;YAAC,KAAI,IAAI;cAAC,OAAOgB,CAAC,CAACA,CAAC,CAACf,CAAC,CAAC2B,OAAO,CAAC,CAAC,EAAC,GAAG,KAAG5B,CAAC,GAAC,CAAC,GAAC,CAAC,EAAC,GAAG,CAAC;YAAC,KAAI,GAAG;YAAC,KAAI,IAAI;cAAC,OAAOgB,CAAC,CAACA,CAAC,CAACa,MAAM,CAAC,CAAC,KAAG5B,CAAC,CAAC6B,EAAE,GAAC,EAAE,GAAC7B,CAAC,CAAC6B,EAAE,CAAC,EAAC,GAAG,KAAG9B,CAAC,GAAC,CAAC,GAAC,CAAC,EAAC,GAAG,CAAC;YAAC,KAAI,GAAG;cAAC,OAAOoB,IAAI,CAACW,KAAK,CAAC9B,CAAC,CAAC+B,EAAE,CAACC,OAAO,CAAC,CAAC,GAAC,GAAG,CAAC;YAAC,KAAI,GAAG;cAAC,OAAOhC,CAAC,CAAC+B,EAAE,CAACC,OAAO,CAAC,CAAC;YAAC,KAAI,GAAG;cAAC,OAAM,GAAG,GAAChC,CAAC,CAACiC,UAAU,CAAC,CAAC,GAAC,GAAG;YAAC,KAAI,KAAK;cAAC,OAAM,GAAG,GAACjC,CAAC,CAACiC,UAAU,CAAC,MAAM,CAAC,GAAC,GAAG;YAAC;cAAQ,OAAOlC,CAAC;UAAA;QAAC,CAAE,CAAC;MAAC,OAAOW,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,CAACG,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}