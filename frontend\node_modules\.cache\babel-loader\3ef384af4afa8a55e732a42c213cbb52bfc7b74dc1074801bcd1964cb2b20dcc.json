{"ast": null, "code": "import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar Set = getNative(root, 'Set');\nexport default Set;", "map": {"version": 3, "names": ["getNative", "root", "Set"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/lodash-es/_Set.js"], "sourcesContent": ["import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar Set = getNative(root, 'Set');\n\nexport default Set;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,IAAI,MAAM,YAAY;;AAE7B;AACA,IAAIC,GAAG,GAAGF,SAAS,CAACC,IAAI,EAAE,KAAK,CAAC;AAEhC,eAAeC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}