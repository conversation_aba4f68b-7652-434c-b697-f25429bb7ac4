export const menuSeedData = {
  "Bella Italia": [
    {
      name: "Margherita Pizza",
      description: "Classic pizza with fresh mozzarella, tomato sauce, and basil",
      price: 1899,
      category: "Pizza",
      images: ["https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=600&h=400&fit=crop"],
      vegetarian: true,
      seasonal: false,
      ingredients: ["Mozzarella", "Tomato Sauce", "Basil", "Olive Oil"]
    },
    {
      name: "Spaghetti Carbonara",
      description: "Creamy pasta with pancetta, eggs, and parmesan cheese",
      price: 2299,
      category: "Pasta",
      images: ["https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=600&h=400&fit=crop"],
      vegetarian: false,
      seasonal: false,
      ingredients: ["Spaghetti", "Pancetta", "Eggs", "Parmesan", "Black Pepper"]
    },
    {
      name: "Tiramisu",
      description: "Classic Italian dessert with coffee-soaked ladyfingers and mascarpone",
      price: 899,
      category: "Desserts",
      images: ["https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=600&h=400&fit=crop"],
      vegetarian: true,
      seasonal: false,
      ingredients: ["Mascarpone", "Coffee", "Ladyfingers", "Cocoa"]
    }
  ],
  "Dragon Palace": [
    {
      name: "Peking Duck",
      description: "Traditional roasted duck served with pancakes, scallions, and hoisin sauce",
      price: 3899,
      category: "Main Course",
      images: ["https://images.unsplash.com/photo-1563379091339-03246963d51a?w=600&h=400&fit=crop"],
      vegetarian: false,
      seasonal: false,
      ingredients: ["Duck", "Pancakes", "Scallions", "Hoisin Sauce"]
    },
    {
      name: "Dim Sum Platter",
      description: "Assorted steamed dumplings with pork, shrimp, and vegetables",
      price: 2699,
      category: "Appetizers",
      images: ["https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=600&h=400&fit=crop"],
      vegetarian: false,
      seasonal: false,
      ingredients: ["Pork", "Shrimp", "Vegetables", "Dumpling Wrapper"]
    },
    {
      name: "Kung Pao Chicken",
      description: "Spicy stir-fried chicken with peanuts and vegetables",
      price: 2199,
      category: "Main Course",
      images: ["https://images.unsplash.com/photo-1526318896980-cf78c088247c?w=600&h=400&fit=crop"],
      vegetarian: false,
      seasonal: false,
      ingredients: ["Chicken", "Peanuts", "Bell Peppers", "Chili Peppers"]
    }
  ],
  "Taco Fiesta": [
    {
      name: "Street Tacos",
      description: "Authentic Mexican tacos with carnitas, onions, and cilantro",
      price: 1599,
      category: "Tacos",
      images: ["https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=600&h=400&fit=crop"],
      vegetarian: false,
      seasonal: false,
      ingredients: ["Carnitas", "Corn Tortillas", "Onions", "Cilantro"]
    },
    {
      name: "Guacamole & Chips",
      description: "Fresh avocado dip with lime, jalapeños, and crispy tortilla chips",
      price: 1299,
      category: "Appetizers",
      images: ["https://images.unsplash.com/photo-1541544181051-e46607bc22a4?w=600&h=400&fit=crop"],
      vegetarian: true,
      seasonal: false,
      ingredients: ["Avocado", "Lime", "Jalapeños", "Tortilla Chips"]
    },
    {
      name: "Chicken Quesadilla",
      description: "Grilled tortilla filled with chicken, cheese, and peppers",
      price: 1899,
      category: "Main Course",
      images: ["https://images.unsplash.com/photo-1551504734-5ee1c4a1479b?w=600&h=400&fit=crop"],
      vegetarian: false,
      seasonal: false,
      ingredients: ["Chicken", "Cheese", "Flour Tortilla", "Bell Peppers"]
    }
  ],
  "Sakura Sushi": [
    {
      name: "Salmon Sashimi",
      description: "Fresh Atlantic salmon sliced to perfection",
      price: 2899,
      category: "Sashimi",
      images: ["https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=600&h=400&fit=crop"],
      vegetarian: false,
      seasonal: false,
      ingredients: ["Fresh Salmon", "Wasabi", "Pickled Ginger"]
    },
    {
      name: "California Roll",
      description: "Crab, avocado, and cucumber wrapped in nori and rice",
      price: 1699,
      category: "Sushi Rolls",
      images: ["https://images.unsplash.com/photo-1553621042-f6e147245754?w=600&h=400&fit=crop"],
      vegetarian: false,
      seasonal: false,
      ingredients: ["Crab", "Avocado", "Cucumber", "Nori", "Sushi Rice"]
    },
    {
      name: "Miso Soup",
      description: "Traditional Japanese soup with tofu and seaweed",
      price: 699,
      category: "Soup",
      images: ["https://images.unsplash.com/photo-1547592166-23ac45744acd?w=600&h=400&fit=crop"],
      vegetarian: true,
      seasonal: false,
      ingredients: ["Miso Paste", "Tofu", "Seaweed", "Scallions"]
    }
  ],
  "Le Petit Bistro": [
    {
      name: "Coq au Vin",
      description: "Classic French chicken braised in red wine with mushrooms",
      price: 3299,
      category: "Main Course",
      images: ["https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=600&h=400&fit=crop"],
      vegetarian: false,
      seasonal: false,
      ingredients: ["Chicken", "Red Wine", "Mushrooms", "Pearl Onions"]
    },
    {
      name: "French Onion Soup",
      description: "Rich onion soup topped with gruyere cheese and croutons",
      price: 1499,
      category: "Soup",
      images: ["https://images.unsplash.com/photo-1547592166-23ac45744acd?w=600&h=400&fit=crop"],
      vegetarian: true,
      seasonal: false,
      ingredients: ["Onions", "Gruyere Cheese", "Beef Broth", "Croutons"]
    },
    {
      name: "Crème Brûlée",
      description: "Vanilla custard with caramelized sugar crust",
      price: 1199,
      category: "Desserts",
      images: ["https://images.unsplash.com/photo-1551024506-0bccd828d307?w=600&h=400&fit=crop"],
      vegetarian: true,
      seasonal: false,
      ingredients: ["Cream", "Vanilla", "Eggs", "Sugar"]
    }
  ]
};
