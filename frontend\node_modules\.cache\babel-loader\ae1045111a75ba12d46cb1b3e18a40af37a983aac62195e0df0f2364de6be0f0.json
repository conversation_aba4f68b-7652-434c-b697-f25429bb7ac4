{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docBacklinkRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'referrer [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'command', 'link']]\n};\nvar _default = docBacklinkRole;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "docBacklinkRole", "abstract", "accessibleNameRequired", "baseConcepts", "childrenPresentational", "nameFrom", "prohibitedProps", "props", "relatedConcepts", "concept", "name", "module", "requireContextRole", "requiredContextRole", "requiredOwnedElements", "requiredProps", "superClass", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/etc/roles/dpub/docBacklinkRole.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docBacklinkRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'referrer [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'command', 'link']]\n};\nvar _default = docBacklinkRole;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,eAAe,GAAG;EACpBC,QAAQ,EAAE,KAAK;EACfC,sBAAsB,EAAE,IAAI;EAC5BC,YAAY,EAAE,EAAE;EAChBC,sBAAsB,EAAE,KAAK;EAC7BC,QAAQ,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;EAChCC,eAAe,EAAE,EAAE;EACnBC,KAAK,EAAE;IACL,mBAAmB,EAAE,IAAI;IACzB,cAAc,EAAE;EAClB,CAAC;EACDC,eAAe,EAAE,CAAC;IAChBC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,CAAC;EACFC,kBAAkB,EAAE,EAAE;EACtBC,mBAAmB,EAAE,EAAE;EACvBC,qBAAqB,EAAE,EAAE;EACzBC,aAAa,EAAE,CAAC,CAAC;EACjBC,UAAU,EAAE,CAAC,CAAC,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC;AACxD,CAAC;AACD,IAAIC,QAAQ,GAAGjB,eAAe;AAC9BH,OAAO,CAACE,OAAO,GAAGkB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}