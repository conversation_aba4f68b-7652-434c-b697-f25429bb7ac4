{"ast": null, "code": "export { useDesktopPicker } from \"./useDesktopPicker.js\";", "map": {"version": 3, "names": ["useDesktopPicker"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useDesktopPicker/index.js"], "sourcesContent": ["export { useDesktopPicker } from \"./useDesktopPicker.js\";"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}