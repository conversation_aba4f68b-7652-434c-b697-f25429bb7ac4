{"ast": null, "code": "import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport { useUtils } from \"./useUtils.js\";\n/**\n * Hooks making sure that:\n * - The value returned by `onChange` always have the timezone of `props.value` or `props.defaultValue` if defined\n * - The value rendered is always the one from `props.timezone` if defined\n */\nexport const useValueWithTimezone = ({\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  onChange,\n  valueManager\n}) => {\n  const utils = useUtils();\n  const firstDefaultValue = React.useRef(defaultValue);\n  const inputValue = valueProp ?? firstDefaultValue.current ?? valueManager.emptyValue;\n  const inputTimezone = React.useMemo(() => valueManager.getTimezone(utils, inputValue), [utils, valueManager, inputValue]);\n  const setInputTimezone = useEventCallback(newValue => {\n    if (inputTimezone == null) {\n      return newValue;\n    }\n    return valueManager.setTimezone(utils, inputTimezone, newValue);\n  });\n  const timezoneToRender = timezoneProp ?? inputTimezone ?? 'default';\n  const valueWithTimezoneToRender = React.useMemo(() => valueManager.setTimezone(utils, timezoneToRender, inputValue), [valueManager, utils, timezoneToRender, inputValue]);\n  const handleValueChange = useEventCallback((newValue, ...otherParams) => {\n    const newValueWithInputTimezone = setInputTimezone(newValue);\n    onChange?.(newValueWithInputTimezone, ...otherParams);\n  });\n  return {\n    value: valueWithTimezoneToRender,\n    handleValueChange,\n    timezone: timezoneToRender\n  };\n};\n\n/**\n * Wrapper around `useControlled` and `useValueWithTimezone`\n */\nexport const useControlledValueWithTimezone = ({\n  name,\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  onChange: onChangeProp,\n  valueManager\n}) => {\n  const [valueWithInputTimezone, setValue] = useControlled({\n    name,\n    state: 'value',\n    controlled: valueProp,\n    default: defaultValue ?? valueManager.emptyValue\n  });\n  const onChange = useEventCallback((newValue, ...otherParams) => {\n    setValue(newValue);\n    onChangeProp?.(newValue, ...otherParams);\n  });\n  return useValueWithTimezone({\n    timezone: timezoneProp,\n    value: valueWithInputTimezone,\n    defaultValue: undefined,\n    onChange,\n    valueManager\n  });\n};", "map": {"version": 3, "names": ["React", "useEventCallback", "useControlled", "useUtils", "useValueWithTimezone", "timezone", "timezoneProp", "value", "valueProp", "defaultValue", "onChange", "valueManager", "utils", "firstDefaultValue", "useRef", "inputValue", "current", "emptyValue", "inputTimezone", "useMemo", "getTimezone", "setInputTimezone", "newValue", "setTimezone", "timezoneToRender", "valueWithTimezoneToRender", "handleValueChange", "otherParams", "newValueWithInputTimezone", "useControlledValueWithTimezone", "name", "onChangeProp", "valueWithInputTimezone", "setValue", "state", "controlled", "default", "undefined"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useValueWithTimezone.js"], "sourcesContent": ["import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport { useUtils } from \"./useUtils.js\";\n/**\n * Hooks making sure that:\n * - The value returned by `onChange` always have the timezone of `props.value` or `props.defaultValue` if defined\n * - The value rendered is always the one from `props.timezone` if defined\n */\nexport const useValueWithTimezone = ({\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  onChange,\n  valueManager\n}) => {\n  const utils = useUtils();\n  const firstDefaultValue = React.useRef(defaultValue);\n  const inputValue = valueProp ?? firstDefaultValue.current ?? valueManager.emptyValue;\n  const inputTimezone = React.useMemo(() => valueManager.getTimezone(utils, inputValue), [utils, valueManager, inputValue]);\n  const setInputTimezone = useEventCallback(newValue => {\n    if (inputTimezone == null) {\n      return newValue;\n    }\n    return valueManager.setTimezone(utils, inputTimezone, newValue);\n  });\n  const timezoneToRender = timezoneProp ?? inputTimezone ?? 'default';\n  const valueWithTimezoneToRender = React.useMemo(() => valueManager.setTimezone(utils, timezoneToRender, inputValue), [valueManager, utils, timezoneToRender, inputValue]);\n  const handleValueChange = useEventCallback((newValue, ...otherParams) => {\n    const newValueWithInputTimezone = setInputTimezone(newValue);\n    onChange?.(newValueWithInputTimezone, ...otherParams);\n  });\n  return {\n    value: valueWithTimezoneToRender,\n    handleValueChange,\n    timezone: timezoneToRender\n  };\n};\n\n/**\n * Wrapper around `useControlled` and `useValueWithTimezone`\n */\nexport const useControlledValueWithTimezone = ({\n  name,\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  onChange: onChangeProp,\n  valueManager\n}) => {\n  const [valueWithInputTimezone, setValue] = useControlled({\n    name,\n    state: 'value',\n    controlled: valueProp,\n    default: defaultValue ?? valueManager.emptyValue\n  });\n  const onChange = useEventCallback((newValue, ...otherParams) => {\n    setValue(newValue);\n    onChangeProp?.(newValue, ...otherParams);\n  });\n  return useValueWithTimezone({\n    timezone: timezoneProp,\n    value: valueWithInputTimezone,\n    defaultValue: undefined,\n    onChange,\n    valueManager\n  });\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,QAAQ,QAAQ,eAAe;AACxC;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,GAAGA,CAAC;EACnCC,QAAQ,EAAEC,YAAY;EACtBC,KAAK,EAAEC,SAAS;EAChBC,YAAY;EACZC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,MAAMC,KAAK,GAAGT,QAAQ,CAAC,CAAC;EACxB,MAAMU,iBAAiB,GAAGb,KAAK,CAACc,MAAM,CAACL,YAAY,CAAC;EACpD,MAAMM,UAAU,GAAGP,SAAS,IAAIK,iBAAiB,CAACG,OAAO,IAAIL,YAAY,CAACM,UAAU;EACpF,MAAMC,aAAa,GAAGlB,KAAK,CAACmB,OAAO,CAAC,MAAMR,YAAY,CAACS,WAAW,CAACR,KAAK,EAAEG,UAAU,CAAC,EAAE,CAACH,KAAK,EAAED,YAAY,EAAEI,UAAU,CAAC,CAAC;EACzH,MAAMM,gBAAgB,GAAGpB,gBAAgB,CAACqB,QAAQ,IAAI;IACpD,IAAIJ,aAAa,IAAI,IAAI,EAAE;MACzB,OAAOI,QAAQ;IACjB;IACA,OAAOX,YAAY,CAACY,WAAW,CAACX,KAAK,EAAEM,aAAa,EAAEI,QAAQ,CAAC;EACjE,CAAC,CAAC;EACF,MAAME,gBAAgB,GAAGlB,YAAY,IAAIY,aAAa,IAAI,SAAS;EACnE,MAAMO,yBAAyB,GAAGzB,KAAK,CAACmB,OAAO,CAAC,MAAMR,YAAY,CAACY,WAAW,CAACX,KAAK,EAAEY,gBAAgB,EAAET,UAAU,CAAC,EAAE,CAACJ,YAAY,EAAEC,KAAK,EAAEY,gBAAgB,EAAET,UAAU,CAAC,CAAC;EACzK,MAAMW,iBAAiB,GAAGzB,gBAAgB,CAAC,CAACqB,QAAQ,EAAE,GAAGK,WAAW,KAAK;IACvE,MAAMC,yBAAyB,GAAGP,gBAAgB,CAACC,QAAQ,CAAC;IAC5DZ,QAAQ,GAAGkB,yBAAyB,EAAE,GAAGD,WAAW,CAAC;EACvD,CAAC,CAAC;EACF,OAAO;IACLpB,KAAK,EAAEkB,yBAAyB;IAChCC,iBAAiB;IACjBrB,QAAQ,EAAEmB;EACZ,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMK,8BAA8B,GAAGA,CAAC;EAC7CC,IAAI;EACJzB,QAAQ,EAAEC,YAAY;EACtBC,KAAK,EAAEC,SAAS;EAChBC,YAAY;EACZC,QAAQ,EAAEqB,YAAY;EACtBpB;AACF,CAAC,KAAK;EACJ,MAAM,CAACqB,sBAAsB,EAAEC,QAAQ,CAAC,GAAG/B,aAAa,CAAC;IACvD4B,IAAI;IACJI,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE3B,SAAS;IACrB4B,OAAO,EAAE3B,YAAY,IAAIE,YAAY,CAACM;EACxC,CAAC,CAAC;EACF,MAAMP,QAAQ,GAAGT,gBAAgB,CAAC,CAACqB,QAAQ,EAAE,GAAGK,WAAW,KAAK;IAC9DM,QAAQ,CAACX,QAAQ,CAAC;IAClBS,YAAY,GAAGT,QAAQ,EAAE,GAAGK,WAAW,CAAC;EAC1C,CAAC,CAAC;EACF,OAAOvB,oBAAoB,CAAC;IAC1BC,QAAQ,EAAEC,YAAY;IACtBC,KAAK,EAAEyB,sBAAsB;IAC7BvB,YAAY,EAAE4B,SAAS;IACvB3B,QAAQ;IACRC;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}