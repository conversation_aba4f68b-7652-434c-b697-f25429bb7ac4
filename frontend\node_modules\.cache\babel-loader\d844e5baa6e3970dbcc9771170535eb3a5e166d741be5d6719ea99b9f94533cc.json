{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useOpenState } from \"../useOpenState.js\";\nimport { useLocalizationContext, useUtils } from \"../useUtils.js\";\nimport { useValidation } from \"../../../validation/index.js\";\nimport { useValueWithTimezone } from \"../useValueWithTimezone.js\";\n\n/**\n * Decide if the new value should be published\n * The published value will be passed to `onChange` if defined.\n */\nconst shouldPublishValue = params => {\n  const {\n    action,\n    hasChanged,\n    dateState,\n    isControlled\n  } = params;\n  const isCurrentValueTheDefaultValue = !isControlled && !dateState.hasBeenModifiedSinceMount;\n\n  // The field is responsible for only calling `onChange` when needed.\n  if (action.name === 'setValueFromField') {\n    return true;\n  }\n  if (action.name === 'setValueFromAction') {\n    // If the component is not controlled, and the value has not been modified since the mount,\n    // Then we want to publish the default value whenever the user pressed the \"Accept\", \"Today\" or \"Clear\" button.\n    if (isCurrentValueTheDefaultValue && ['accept', 'today', 'clear'].includes(action.pickerAction)) {\n      return true;\n    }\n    return hasChanged(dateState.lastPublishedValue);\n  }\n  if (action.name === 'setValueFromView' && action.selectionState !== 'shallow') {\n    // On the first view,\n    // If the value is not controlled, then clicking on any value (including the one equal to `defaultValue`) should call `onChange`\n    if (isCurrentValueTheDefaultValue) {\n      return true;\n    }\n    return hasChanged(dateState.lastPublishedValue);\n  }\n  if (action.name === 'setValueFromShortcut') {\n    // On the first view,\n    // If the value is not controlled, then clicking on any value (including the one equal to `defaultValue`) should call `onChange`\n    if (isCurrentValueTheDefaultValue) {\n      return true;\n    }\n    return hasChanged(dateState.lastPublishedValue);\n  }\n  return false;\n};\n\n/**\n * Decide if the new value should be committed.\n * The committed value will be passed to `onAccept` if defined.\n * It will also be used as a reset target when calling the `cancel` picker action (when clicking on the \"Cancel\" button).\n */\nconst shouldCommitValue = params => {\n  const {\n    action,\n    hasChanged,\n    dateState,\n    isControlled,\n    closeOnSelect\n  } = params;\n  const isCurrentValueTheDefaultValue = !isControlled && !dateState.hasBeenModifiedSinceMount;\n  if (action.name === 'setValueFromAction') {\n    // If the component is not controlled, and the value has not been modified since the mount,\n    // Then we want to commit the default value whenever the user pressed the \"Accept\", \"Today\" or \"Clear\" button.\n    if (isCurrentValueTheDefaultValue && ['accept', 'today', 'clear'].includes(action.pickerAction)) {\n      return true;\n    }\n    return hasChanged(dateState.lastCommittedValue);\n  }\n  if (action.name === 'setValueFromView' && action.selectionState === 'finish' && closeOnSelect) {\n    // On picker where the 1st view is also the last view,\n    // If the value is not controlled, then clicking on any value (including the one equal to `defaultValue`) should call `onAccept`\n    if (isCurrentValueTheDefaultValue) {\n      return true;\n    }\n    return hasChanged(dateState.lastCommittedValue);\n  }\n  if (action.name === 'setValueFromShortcut') {\n    return action.changeImportance === 'accept' && hasChanged(dateState.lastCommittedValue);\n  }\n  return false;\n};\n\n/**\n * Decide if the picker should be closed after the value is updated.\n */\nconst shouldClosePicker = params => {\n  const {\n    action,\n    closeOnSelect\n  } = params;\n  if (action.name === 'setValueFromAction') {\n    return true;\n  }\n  if (action.name === 'setValueFromView') {\n    return action.selectionState === 'finish' && closeOnSelect;\n  }\n  if (action.name === 'setValueFromShortcut') {\n    return action.changeImportance === 'accept';\n  }\n  return false;\n};\n\n/**\n * Manage the value lifecycle of all the pickers.\n */\nexport const usePickerValue = ({\n  props,\n  valueManager,\n  valueType,\n  wrapperVariant,\n  validator\n}) => {\n  const {\n    onAccept,\n    onChange,\n    value: inValueWithoutRenderTimezone,\n    defaultValue: inDefaultValue,\n    closeOnSelect = wrapperVariant === 'desktop',\n    timezone: timezoneProp\n  } = props;\n  const {\n    current: defaultValue\n  } = React.useRef(inDefaultValue);\n  const {\n    current: isControlled\n  } = React.useRef(inValueWithoutRenderTimezone !== undefined);\n\n  /* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isControlled !== (inValueWithoutRenderTimezone !== undefined)) {\n        console.error([`MUI X: A component is changing the ${isControlled ? '' : 'un'}controlled value of a picker to be ${isControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled value` + 'for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [inValueWithoutRenderTimezone]);\n    React.useEffect(() => {\n      if (!isControlled && defaultValue !== inDefaultValue) {\n        console.error([`MUI X: A component is changing the defaultValue of an uncontrolled picker after being initialized. ` + `To suppress this warning opt to use a controlled value.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultValue)]);\n  }\n  /* eslint-enable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n\n  const utils = useUtils();\n  const adapter = useLocalizationContext();\n  const {\n    isOpen,\n    setIsOpen\n  } = useOpenState(props);\n  const {\n    timezone,\n    value: inValueWithTimezoneToRender,\n    handleValueChange\n  } = useValueWithTimezone({\n    timezone: timezoneProp,\n    value: inValueWithoutRenderTimezone,\n    defaultValue,\n    onChange,\n    valueManager\n  });\n  const [dateState, setDateState] = React.useState(() => {\n    let initialValue;\n    if (inValueWithTimezoneToRender !== undefined) {\n      initialValue = inValueWithTimezoneToRender;\n    } else if (defaultValue !== undefined) {\n      initialValue = defaultValue;\n    } else {\n      initialValue = valueManager.emptyValue;\n    }\n    return {\n      draft: initialValue,\n      lastPublishedValue: initialValue,\n      lastCommittedValue: initialValue,\n      lastControlledValue: inValueWithTimezoneToRender,\n      hasBeenModifiedSinceMount: false\n    };\n  });\n  const {\n    getValidationErrorForNewValue\n  } = useValidation({\n    props,\n    validator,\n    timezone,\n    value: dateState.draft,\n    onError: props.onError\n  });\n  const updateDate = useEventCallback(action => {\n    const updaterParams = {\n      action,\n      dateState,\n      hasChanged: comparison => !valueManager.areValuesEqual(utils, action.value, comparison),\n      isControlled,\n      closeOnSelect\n    };\n    const shouldPublish = shouldPublishValue(updaterParams);\n    const shouldCommit = shouldCommitValue(updaterParams);\n    const shouldClose = shouldClosePicker(updaterParams);\n    setDateState(prev => _extends({}, prev, {\n      draft: action.value,\n      lastPublishedValue: shouldPublish ? action.value : prev.lastPublishedValue,\n      lastCommittedValue: shouldCommit ? action.value : prev.lastCommittedValue,\n      hasBeenModifiedSinceMount: true\n    }));\n    let cachedContext = null;\n    const getContext = () => {\n      if (!cachedContext) {\n        const validationError = action.name === 'setValueFromField' ? action.context.validationError : getValidationErrorForNewValue(action.value);\n        cachedContext = {\n          validationError\n        };\n        if (action.name === 'setValueFromShortcut') {\n          cachedContext.shortcut = action.shortcut;\n        }\n      }\n      return cachedContext;\n    };\n    if (shouldPublish) {\n      handleValueChange(action.value, getContext());\n    }\n    if (shouldCommit && onAccept) {\n      onAccept(action.value, getContext());\n    }\n    if (shouldClose) {\n      setIsOpen(false);\n    }\n  });\n  if (inValueWithTimezoneToRender !== undefined && (dateState.lastControlledValue === undefined || !valueManager.areValuesEqual(utils, dateState.lastControlledValue, inValueWithTimezoneToRender))) {\n    const isUpdateComingFromPicker = valueManager.areValuesEqual(utils, dateState.draft, inValueWithTimezoneToRender);\n    setDateState(prev => _extends({}, prev, {\n      lastControlledValue: inValueWithTimezoneToRender\n    }, isUpdateComingFromPicker ? {} : {\n      lastCommittedValue: inValueWithTimezoneToRender,\n      lastPublishedValue: inValueWithTimezoneToRender,\n      draft: inValueWithTimezoneToRender,\n      hasBeenModifiedSinceMount: true\n    }));\n  }\n  const handleClear = useEventCallback(() => {\n    updateDate({\n      value: valueManager.emptyValue,\n      name: 'setValueFromAction',\n      pickerAction: 'clear'\n    });\n  });\n  const handleAccept = useEventCallback(() => {\n    updateDate({\n      value: dateState.lastPublishedValue,\n      name: 'setValueFromAction',\n      pickerAction: 'accept'\n    });\n  });\n  const handleDismiss = useEventCallback(() => {\n    updateDate({\n      value: dateState.lastPublishedValue,\n      name: 'setValueFromAction',\n      pickerAction: 'dismiss'\n    });\n  });\n  const handleCancel = useEventCallback(() => {\n    updateDate({\n      value: dateState.lastCommittedValue,\n      name: 'setValueFromAction',\n      pickerAction: 'cancel'\n    });\n  });\n  const handleSetToday = useEventCallback(() => {\n    updateDate({\n      value: valueManager.getTodayValue(utils, timezone, valueType),\n      name: 'setValueFromAction',\n      pickerAction: 'today'\n    });\n  });\n  const handleOpen = useEventCallback(event => {\n    event.preventDefault();\n    setIsOpen(true);\n  });\n  const handleClose = useEventCallback(event => {\n    event?.preventDefault();\n    setIsOpen(false);\n  });\n  const handleChange = useEventCallback((newValue, selectionState = 'partial') => updateDate({\n    name: 'setValueFromView',\n    value: newValue,\n    selectionState\n  }));\n  const handleSelectShortcut = useEventCallback((newValue, changeImportance, shortcut) => updateDate({\n    name: 'setValueFromShortcut',\n    value: newValue,\n    changeImportance,\n    shortcut\n  }));\n  const handleChangeFromField = useEventCallback((newValue, context) => updateDate({\n    name: 'setValueFromField',\n    value: newValue,\n    context\n  }));\n  const actions = {\n    onClear: handleClear,\n    onAccept: handleAccept,\n    onDismiss: handleDismiss,\n    onCancel: handleCancel,\n    onSetToday: handleSetToday,\n    onOpen: handleOpen,\n    onClose: handleClose\n  };\n  const fieldResponse = {\n    value: dateState.draft,\n    onChange: handleChangeFromField\n  };\n  const viewValue = React.useMemo(() => valueManager.cleanValue(utils, dateState.draft), [utils, valueManager, dateState.draft]);\n  const viewResponse = {\n    value: viewValue,\n    onChange: handleChange,\n    onClose: handleClose,\n    open: isOpen\n  };\n  const isValid = testedValue => {\n    const error = validator({\n      adapter,\n      value: testedValue,\n      timezone,\n      props\n    });\n    return !valueManager.hasError(error);\n  };\n  const layoutResponse = _extends({}, actions, {\n    value: viewValue,\n    onChange: handleChange,\n    onSelectShortcut: handleSelectShortcut,\n    isValid\n  });\n  return {\n    open: isOpen,\n    fieldProps: fieldResponse,\n    viewProps: viewResponse,\n    layoutProps: layoutResponse,\n    actions\n  };\n};", "map": {"version": 3, "names": ["_extends", "React", "useEventCallback", "useOpenState", "useLocalizationContext", "useUtils", "useValidation", "useValueWithTimezone", "shouldPublishValue", "params", "action", "has<PERSON><PERSON>ed", "dateState", "isControlled", "isCurrentValueTheDefaultValue", "hasBeenModifiedSinceMount", "name", "includes", "pickerAction", "lastPublishedValue", "selectionState", "shouldCommitValue", "closeOnSelect", "lastCommittedValue", "changeImportance", "shouldClosePicker", "usePickerValue", "props", "valueManager", "valueType", "wrapperVariant", "validator", "onAccept", "onChange", "value", "inValueWithoutRenderTimezone", "defaultValue", "inDefaultValue", "timezone", "timezoneProp", "current", "useRef", "undefined", "process", "env", "NODE_ENV", "useEffect", "console", "error", "join", "JSON", "stringify", "utils", "adapter", "isOpen", "setIsOpen", "inValueWithTimezoneToRender", "handleValueChange", "setDateState", "useState", "initialValue", "emptyValue", "draft", "lastControlledValue", "getValidationErrorForNewValue", "onError", "updateDate", "updaterParams", "comparison", "areValuesEqual", "shouldPublish", "shouldCommit", "shouldClose", "prev", "cachedContext", "getContext", "validationError", "context", "shortcut", "isUpdateComingFromPicker", "handleClear", "handleAccept", "handle<PERSON><PERSON><PERSON>", "handleCancel", "handleSetToday", "getTodayValue", "handleOpen", "event", "preventDefault", "handleClose", "handleChange", "newValue", "handleSelectShortcut", "handleChangeFromField", "actions", "onClear", "on<PERSON><PERSON><PERSON>", "onCancel", "onSetToday", "onOpen", "onClose", "fieldResponse", "viewValue", "useMemo", "cleanValue", "viewResponse", "open", "<PERSON><PERSON><PERSON><PERSON>", "testedValue", "<PERSON><PERSON><PERSON><PERSON>", "layoutResponse", "onSelectShortcut", "fieldProps", "viewProps", "layoutProps"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/internals/hooks/usePicker/usePickerValue.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useOpenState } from \"../useOpenState.js\";\nimport { useLocalizationContext, useUtils } from \"../useUtils.js\";\nimport { useValidation } from \"../../../validation/index.js\";\nimport { useValueWithTimezone } from \"../useValueWithTimezone.js\";\n\n/**\n * Decide if the new value should be published\n * The published value will be passed to `onChange` if defined.\n */\nconst shouldPublishValue = params => {\n  const {\n    action,\n    hasChanged,\n    dateState,\n    isControlled\n  } = params;\n  const isCurrentValueTheDefaultValue = !isControlled && !dateState.hasBeenModifiedSinceMount;\n\n  // The field is responsible for only calling `onChange` when needed.\n  if (action.name === 'setValueFromField') {\n    return true;\n  }\n  if (action.name === 'setValueFromAction') {\n    // If the component is not controlled, and the value has not been modified since the mount,\n    // Then we want to publish the default value whenever the user pressed the \"Accept\", \"Today\" or \"Clear\" button.\n    if (isCurrentValueTheDefaultValue && ['accept', 'today', 'clear'].includes(action.pickerAction)) {\n      return true;\n    }\n    return hasChanged(dateState.lastPublishedValue);\n  }\n  if (action.name === 'setValueFromView' && action.selectionState !== 'shallow') {\n    // On the first view,\n    // If the value is not controlled, then clicking on any value (including the one equal to `defaultValue`) should call `onChange`\n    if (isCurrentValueTheDefaultValue) {\n      return true;\n    }\n    return hasChanged(dateState.lastPublishedValue);\n  }\n  if (action.name === 'setValueFromShortcut') {\n    // On the first view,\n    // If the value is not controlled, then clicking on any value (including the one equal to `defaultValue`) should call `onChange`\n    if (isCurrentValueTheDefaultValue) {\n      return true;\n    }\n    return hasChanged(dateState.lastPublishedValue);\n  }\n  return false;\n};\n\n/**\n * Decide if the new value should be committed.\n * The committed value will be passed to `onAccept` if defined.\n * It will also be used as a reset target when calling the `cancel` picker action (when clicking on the \"Cancel\" button).\n */\nconst shouldCommitValue = params => {\n  const {\n    action,\n    hasChanged,\n    dateState,\n    isControlled,\n    closeOnSelect\n  } = params;\n  const isCurrentValueTheDefaultValue = !isControlled && !dateState.hasBeenModifiedSinceMount;\n  if (action.name === 'setValueFromAction') {\n    // If the component is not controlled, and the value has not been modified since the mount,\n    // Then we want to commit the default value whenever the user pressed the \"Accept\", \"Today\" or \"Clear\" button.\n    if (isCurrentValueTheDefaultValue && ['accept', 'today', 'clear'].includes(action.pickerAction)) {\n      return true;\n    }\n    return hasChanged(dateState.lastCommittedValue);\n  }\n  if (action.name === 'setValueFromView' && action.selectionState === 'finish' && closeOnSelect) {\n    // On picker where the 1st view is also the last view,\n    // If the value is not controlled, then clicking on any value (including the one equal to `defaultValue`) should call `onAccept`\n    if (isCurrentValueTheDefaultValue) {\n      return true;\n    }\n    return hasChanged(dateState.lastCommittedValue);\n  }\n  if (action.name === 'setValueFromShortcut') {\n    return action.changeImportance === 'accept' && hasChanged(dateState.lastCommittedValue);\n  }\n  return false;\n};\n\n/**\n * Decide if the picker should be closed after the value is updated.\n */\nconst shouldClosePicker = params => {\n  const {\n    action,\n    closeOnSelect\n  } = params;\n  if (action.name === 'setValueFromAction') {\n    return true;\n  }\n  if (action.name === 'setValueFromView') {\n    return action.selectionState === 'finish' && closeOnSelect;\n  }\n  if (action.name === 'setValueFromShortcut') {\n    return action.changeImportance === 'accept';\n  }\n  return false;\n};\n\n/**\n * Manage the value lifecycle of all the pickers.\n */\nexport const usePickerValue = ({\n  props,\n  valueManager,\n  valueType,\n  wrapperVariant,\n  validator\n}) => {\n  const {\n    onAccept,\n    onChange,\n    value: inValueWithoutRenderTimezone,\n    defaultValue: inDefaultValue,\n    closeOnSelect = wrapperVariant === 'desktop',\n    timezone: timezoneProp\n  } = props;\n  const {\n    current: defaultValue\n  } = React.useRef(inDefaultValue);\n  const {\n    current: isControlled\n  } = React.useRef(inValueWithoutRenderTimezone !== undefined);\n\n  /* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isControlled !== (inValueWithoutRenderTimezone !== undefined)) {\n        console.error([`MUI X: A component is changing the ${isControlled ? '' : 'un'}controlled value of a picker to be ${isControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled value` + 'for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [inValueWithoutRenderTimezone]);\n    React.useEffect(() => {\n      if (!isControlled && defaultValue !== inDefaultValue) {\n        console.error([`MUI X: A component is changing the defaultValue of an uncontrolled picker after being initialized. ` + `To suppress this warning opt to use a controlled value.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultValue)]);\n  }\n  /* eslint-enable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n\n  const utils = useUtils();\n  const adapter = useLocalizationContext();\n  const {\n    isOpen,\n    setIsOpen\n  } = useOpenState(props);\n  const {\n    timezone,\n    value: inValueWithTimezoneToRender,\n    handleValueChange\n  } = useValueWithTimezone({\n    timezone: timezoneProp,\n    value: inValueWithoutRenderTimezone,\n    defaultValue,\n    onChange,\n    valueManager\n  });\n  const [dateState, setDateState] = React.useState(() => {\n    let initialValue;\n    if (inValueWithTimezoneToRender !== undefined) {\n      initialValue = inValueWithTimezoneToRender;\n    } else if (defaultValue !== undefined) {\n      initialValue = defaultValue;\n    } else {\n      initialValue = valueManager.emptyValue;\n    }\n    return {\n      draft: initialValue,\n      lastPublishedValue: initialValue,\n      lastCommittedValue: initialValue,\n      lastControlledValue: inValueWithTimezoneToRender,\n      hasBeenModifiedSinceMount: false\n    };\n  });\n  const {\n    getValidationErrorForNewValue\n  } = useValidation({\n    props,\n    validator,\n    timezone,\n    value: dateState.draft,\n    onError: props.onError\n  });\n  const updateDate = useEventCallback(action => {\n    const updaterParams = {\n      action,\n      dateState,\n      hasChanged: comparison => !valueManager.areValuesEqual(utils, action.value, comparison),\n      isControlled,\n      closeOnSelect\n    };\n    const shouldPublish = shouldPublishValue(updaterParams);\n    const shouldCommit = shouldCommitValue(updaterParams);\n    const shouldClose = shouldClosePicker(updaterParams);\n    setDateState(prev => _extends({}, prev, {\n      draft: action.value,\n      lastPublishedValue: shouldPublish ? action.value : prev.lastPublishedValue,\n      lastCommittedValue: shouldCommit ? action.value : prev.lastCommittedValue,\n      hasBeenModifiedSinceMount: true\n    }));\n    let cachedContext = null;\n    const getContext = () => {\n      if (!cachedContext) {\n        const validationError = action.name === 'setValueFromField' ? action.context.validationError : getValidationErrorForNewValue(action.value);\n        cachedContext = {\n          validationError\n        };\n        if (action.name === 'setValueFromShortcut') {\n          cachedContext.shortcut = action.shortcut;\n        }\n      }\n      return cachedContext;\n    };\n    if (shouldPublish) {\n      handleValueChange(action.value, getContext());\n    }\n    if (shouldCommit && onAccept) {\n      onAccept(action.value, getContext());\n    }\n    if (shouldClose) {\n      setIsOpen(false);\n    }\n  });\n  if (inValueWithTimezoneToRender !== undefined && (dateState.lastControlledValue === undefined || !valueManager.areValuesEqual(utils, dateState.lastControlledValue, inValueWithTimezoneToRender))) {\n    const isUpdateComingFromPicker = valueManager.areValuesEqual(utils, dateState.draft, inValueWithTimezoneToRender);\n    setDateState(prev => _extends({}, prev, {\n      lastControlledValue: inValueWithTimezoneToRender\n    }, isUpdateComingFromPicker ? {} : {\n      lastCommittedValue: inValueWithTimezoneToRender,\n      lastPublishedValue: inValueWithTimezoneToRender,\n      draft: inValueWithTimezoneToRender,\n      hasBeenModifiedSinceMount: true\n    }));\n  }\n  const handleClear = useEventCallback(() => {\n    updateDate({\n      value: valueManager.emptyValue,\n      name: 'setValueFromAction',\n      pickerAction: 'clear'\n    });\n  });\n  const handleAccept = useEventCallback(() => {\n    updateDate({\n      value: dateState.lastPublishedValue,\n      name: 'setValueFromAction',\n      pickerAction: 'accept'\n    });\n  });\n  const handleDismiss = useEventCallback(() => {\n    updateDate({\n      value: dateState.lastPublishedValue,\n      name: 'setValueFromAction',\n      pickerAction: 'dismiss'\n    });\n  });\n  const handleCancel = useEventCallback(() => {\n    updateDate({\n      value: dateState.lastCommittedValue,\n      name: 'setValueFromAction',\n      pickerAction: 'cancel'\n    });\n  });\n  const handleSetToday = useEventCallback(() => {\n    updateDate({\n      value: valueManager.getTodayValue(utils, timezone, valueType),\n      name: 'setValueFromAction',\n      pickerAction: 'today'\n    });\n  });\n  const handleOpen = useEventCallback(event => {\n    event.preventDefault();\n    setIsOpen(true);\n  });\n  const handleClose = useEventCallback(event => {\n    event?.preventDefault();\n    setIsOpen(false);\n  });\n  const handleChange = useEventCallback((newValue, selectionState = 'partial') => updateDate({\n    name: 'setValueFromView',\n    value: newValue,\n    selectionState\n  }));\n  const handleSelectShortcut = useEventCallback((newValue, changeImportance, shortcut) => updateDate({\n    name: 'setValueFromShortcut',\n    value: newValue,\n    changeImportance,\n    shortcut\n  }));\n  const handleChangeFromField = useEventCallback((newValue, context) => updateDate({\n    name: 'setValueFromField',\n    value: newValue,\n    context\n  }));\n  const actions = {\n    onClear: handleClear,\n    onAccept: handleAccept,\n    onDismiss: handleDismiss,\n    onCancel: handleCancel,\n    onSetToday: handleSetToday,\n    onOpen: handleOpen,\n    onClose: handleClose\n  };\n  const fieldResponse = {\n    value: dateState.draft,\n    onChange: handleChangeFromField\n  };\n  const viewValue = React.useMemo(() => valueManager.cleanValue(utils, dateState.draft), [utils, valueManager, dateState.draft]);\n  const viewResponse = {\n    value: viewValue,\n    onChange: handleChange,\n    onClose: handleClose,\n    open: isOpen\n  };\n  const isValid = testedValue => {\n    const error = validator({\n      adapter,\n      value: testedValue,\n      timezone,\n      props\n    });\n    return !valueManager.hasError(error);\n  };\n  const layoutResponse = _extends({}, actions, {\n    value: viewValue,\n    onChange: handleChange,\n    onSelectShortcut: handleSelectShortcut,\n    isValid\n  });\n  return {\n    open: isOpen,\n    fieldProps: fieldResponse,\n    viewProps: viewResponse,\n    layoutProps: layoutResponse,\n    actions\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,sBAAsB,EAAEC,QAAQ,QAAQ,gBAAgB;AACjE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,oBAAoB,QAAQ,4BAA4B;;AAEjE;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAGC,MAAM,IAAI;EACnC,MAAM;IACJC,MAAM;IACNC,UAAU;IACVC,SAAS;IACTC;EACF,CAAC,GAAGJ,MAAM;EACV,MAAMK,6BAA6B,GAAG,CAACD,YAAY,IAAI,CAACD,SAAS,CAACG,yBAAyB;;EAE3F;EACA,IAAIL,MAAM,CAACM,IAAI,KAAK,mBAAmB,EAAE;IACvC,OAAO,IAAI;EACb;EACA,IAAIN,MAAM,CAACM,IAAI,KAAK,oBAAoB,EAAE;IACxC;IACA;IACA,IAAIF,6BAA6B,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAACG,QAAQ,CAACP,MAAM,CAACQ,YAAY,CAAC,EAAE;MAC/F,OAAO,IAAI;IACb;IACA,OAAOP,UAAU,CAACC,SAAS,CAACO,kBAAkB,CAAC;EACjD;EACA,IAAIT,MAAM,CAACM,IAAI,KAAK,kBAAkB,IAAIN,MAAM,CAACU,cAAc,KAAK,SAAS,EAAE;IAC7E;IACA;IACA,IAAIN,6BAA6B,EAAE;MACjC,OAAO,IAAI;IACb;IACA,OAAOH,UAAU,CAACC,SAAS,CAACO,kBAAkB,CAAC;EACjD;EACA,IAAIT,MAAM,CAACM,IAAI,KAAK,sBAAsB,EAAE;IAC1C;IACA;IACA,IAAIF,6BAA6B,EAAE;MACjC,OAAO,IAAI;IACb;IACA,OAAOH,UAAU,CAACC,SAAS,CAACO,kBAAkB,CAAC;EACjD;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAME,iBAAiB,GAAGZ,MAAM,IAAI;EAClC,MAAM;IACJC,MAAM;IACNC,UAAU;IACVC,SAAS;IACTC,YAAY;IACZS;EACF,CAAC,GAAGb,MAAM;EACV,MAAMK,6BAA6B,GAAG,CAACD,YAAY,IAAI,CAACD,SAAS,CAACG,yBAAyB;EAC3F,IAAIL,MAAM,CAACM,IAAI,KAAK,oBAAoB,EAAE;IACxC;IACA;IACA,IAAIF,6BAA6B,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAACG,QAAQ,CAACP,MAAM,CAACQ,YAAY,CAAC,EAAE;MAC/F,OAAO,IAAI;IACb;IACA,OAAOP,UAAU,CAACC,SAAS,CAACW,kBAAkB,CAAC;EACjD;EACA,IAAIb,MAAM,CAACM,IAAI,KAAK,kBAAkB,IAAIN,MAAM,CAACU,cAAc,KAAK,QAAQ,IAAIE,aAAa,EAAE;IAC7F;IACA;IACA,IAAIR,6BAA6B,EAAE;MACjC,OAAO,IAAI;IACb;IACA,OAAOH,UAAU,CAACC,SAAS,CAACW,kBAAkB,CAAC;EACjD;EACA,IAAIb,MAAM,CAACM,IAAI,KAAK,sBAAsB,EAAE;IAC1C,OAAON,MAAM,CAACc,gBAAgB,KAAK,QAAQ,IAAIb,UAAU,CAACC,SAAS,CAACW,kBAAkB,CAAC;EACzF;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA,MAAME,iBAAiB,GAAGhB,MAAM,IAAI;EAClC,MAAM;IACJC,MAAM;IACNY;EACF,CAAC,GAAGb,MAAM;EACV,IAAIC,MAAM,CAACM,IAAI,KAAK,oBAAoB,EAAE;IACxC,OAAO,IAAI;EACb;EACA,IAAIN,MAAM,CAACM,IAAI,KAAK,kBAAkB,EAAE;IACtC,OAAON,MAAM,CAACU,cAAc,KAAK,QAAQ,IAAIE,aAAa;EAC5D;EACA,IAAIZ,MAAM,CAACM,IAAI,KAAK,sBAAsB,EAAE;IAC1C,OAAON,MAAM,CAACc,gBAAgB,KAAK,QAAQ;EAC7C;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAME,cAAc,GAAGA,CAAC;EAC7BC,KAAK;EACLC,YAAY;EACZC,SAAS;EACTC,cAAc;EACdC;AACF,CAAC,KAAK;EACJ,MAAM;IACJC,QAAQ;IACRC,QAAQ;IACRC,KAAK,EAAEC,4BAA4B;IACnCC,YAAY,EAAEC,cAAc;IAC5Bf,aAAa,GAAGQ,cAAc,KAAK,SAAS;IAC5CQ,QAAQ,EAAEC;EACZ,CAAC,GAAGZ,KAAK;EACT,MAAM;IACJa,OAAO,EAAEJ;EACX,CAAC,GAAGnC,KAAK,CAACwC,MAAM,CAACJ,cAAc,CAAC;EAChC,MAAM;IACJG,OAAO,EAAE3B;EACX,CAAC,GAAGZ,KAAK,CAACwC,MAAM,CAACN,4BAA4B,KAAKO,SAAS,CAAC;;EAE5D;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC5C,KAAK,CAAC6C,SAAS,CAAC,MAAM;MACpB,IAAIjC,YAAY,MAAMsB,4BAA4B,KAAKO,SAAS,CAAC,EAAE;QACjEK,OAAO,CAACC,KAAK,CAAC,CAAC,sCAAsCnC,YAAY,GAAG,EAAE,GAAG,IAAI,sCAAsCA,YAAY,GAAG,IAAI,GAAG,EAAE,aAAa,EAAE,6EAA6E,EAAE,yDAAyD,GAAG,oCAAoC,EAAE,4HAA4H,EAAE,sDAAsD,CAAC,CAACoC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC9gB;IACF,CAAC,EAAE,CAACd,4BAA4B,CAAC,CAAC;IAClClC,KAAK,CAAC6C,SAAS,CAAC,MAAM;MACpB,IAAI,CAACjC,YAAY,IAAIuB,YAAY,KAAKC,cAAc,EAAE;QACpDU,OAAO,CAACC,KAAK,CAAC,CAAC,qGAAqG,GAAG,yDAAyD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/L;IACF,CAAC,EAAE,CAACC,IAAI,CAACC,SAAS,CAACf,YAAY,CAAC,CAAC,CAAC;EACpC;EACA;;EAEA,MAAMgB,KAAK,GAAG/C,QAAQ,CAAC,CAAC;EACxB,MAAMgD,OAAO,GAAGjD,sBAAsB,CAAC,CAAC;EACxC,MAAM;IACJkD,MAAM;IACNC;EACF,CAAC,GAAGpD,YAAY,CAACwB,KAAK,CAAC;EACvB,MAAM;IACJW,QAAQ;IACRJ,KAAK,EAAEsB,2BAA2B;IAClCC;EACF,CAAC,GAAGlD,oBAAoB,CAAC;IACvB+B,QAAQ,EAAEC,YAAY;IACtBL,KAAK,EAAEC,4BAA4B;IACnCC,YAAY;IACZH,QAAQ;IACRL;EACF,CAAC,CAAC;EACF,MAAM,CAAChB,SAAS,EAAE8C,YAAY,CAAC,GAAGzD,KAAK,CAAC0D,QAAQ,CAAC,MAAM;IACrD,IAAIC,YAAY;IAChB,IAAIJ,2BAA2B,KAAKd,SAAS,EAAE;MAC7CkB,YAAY,GAAGJ,2BAA2B;IAC5C,CAAC,MAAM,IAAIpB,YAAY,KAAKM,SAAS,EAAE;MACrCkB,YAAY,GAAGxB,YAAY;IAC7B,CAAC,MAAM;MACLwB,YAAY,GAAGhC,YAAY,CAACiC,UAAU;IACxC;IACA,OAAO;MACLC,KAAK,EAAEF,YAAY;MACnBzC,kBAAkB,EAAEyC,YAAY;MAChCrC,kBAAkB,EAAEqC,YAAY;MAChCG,mBAAmB,EAAEP,2BAA2B;MAChDzC,yBAAyB,EAAE;IAC7B,CAAC;EACH,CAAC,CAAC;EACF,MAAM;IACJiD;EACF,CAAC,GAAG1D,aAAa,CAAC;IAChBqB,KAAK;IACLI,SAAS;IACTO,QAAQ;IACRJ,KAAK,EAAEtB,SAAS,CAACkD,KAAK;IACtBG,OAAO,EAAEtC,KAAK,CAACsC;EACjB,CAAC,CAAC;EACF,MAAMC,UAAU,GAAGhE,gBAAgB,CAACQ,MAAM,IAAI;IAC5C,MAAMyD,aAAa,GAAG;MACpBzD,MAAM;MACNE,SAAS;MACTD,UAAU,EAAEyD,UAAU,IAAI,CAACxC,YAAY,CAACyC,cAAc,CAACjB,KAAK,EAAE1C,MAAM,CAACwB,KAAK,EAAEkC,UAAU,CAAC;MACvFvD,YAAY;MACZS;IACF,CAAC;IACD,MAAMgD,aAAa,GAAG9D,kBAAkB,CAAC2D,aAAa,CAAC;IACvD,MAAMI,YAAY,GAAGlD,iBAAiB,CAAC8C,aAAa,CAAC;IACrD,MAAMK,WAAW,GAAG/C,iBAAiB,CAAC0C,aAAa,CAAC;IACpDT,YAAY,CAACe,IAAI,IAAIzE,QAAQ,CAAC,CAAC,CAAC,EAAEyE,IAAI,EAAE;MACtCX,KAAK,EAAEpD,MAAM,CAACwB,KAAK;MACnBf,kBAAkB,EAAEmD,aAAa,GAAG5D,MAAM,CAACwB,KAAK,GAAGuC,IAAI,CAACtD,kBAAkB;MAC1EI,kBAAkB,EAAEgD,YAAY,GAAG7D,MAAM,CAACwB,KAAK,GAAGuC,IAAI,CAAClD,kBAAkB;MACzER,yBAAyB,EAAE;IAC7B,CAAC,CAAC,CAAC;IACH,IAAI2D,aAAa,GAAG,IAAI;IACxB,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAI,CAACD,aAAa,EAAE;QAClB,MAAME,eAAe,GAAGlE,MAAM,CAACM,IAAI,KAAK,mBAAmB,GAAGN,MAAM,CAACmE,OAAO,CAACD,eAAe,GAAGZ,6BAA6B,CAACtD,MAAM,CAACwB,KAAK,CAAC;QAC1IwC,aAAa,GAAG;UACdE;QACF,CAAC;QACD,IAAIlE,MAAM,CAACM,IAAI,KAAK,sBAAsB,EAAE;UAC1C0D,aAAa,CAACI,QAAQ,GAAGpE,MAAM,CAACoE,QAAQ;QAC1C;MACF;MACA,OAAOJ,aAAa;IACtB,CAAC;IACD,IAAIJ,aAAa,EAAE;MACjBb,iBAAiB,CAAC/C,MAAM,CAACwB,KAAK,EAAEyC,UAAU,CAAC,CAAC,CAAC;IAC/C;IACA,IAAIJ,YAAY,IAAIvC,QAAQ,EAAE;MAC5BA,QAAQ,CAACtB,MAAM,CAACwB,KAAK,EAAEyC,UAAU,CAAC,CAAC,CAAC;IACtC;IACA,IAAIH,WAAW,EAAE;MACfjB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC,CAAC;EACF,IAAIC,2BAA2B,KAAKd,SAAS,KAAK9B,SAAS,CAACmD,mBAAmB,KAAKrB,SAAS,IAAI,CAACd,YAAY,CAACyC,cAAc,CAACjB,KAAK,EAAExC,SAAS,CAACmD,mBAAmB,EAAEP,2BAA2B,CAAC,CAAC,EAAE;IACjM,MAAMuB,wBAAwB,GAAGnD,YAAY,CAACyC,cAAc,CAACjB,KAAK,EAAExC,SAAS,CAACkD,KAAK,EAAEN,2BAA2B,CAAC;IACjHE,YAAY,CAACe,IAAI,IAAIzE,QAAQ,CAAC,CAAC,CAAC,EAAEyE,IAAI,EAAE;MACtCV,mBAAmB,EAAEP;IACvB,CAAC,EAAEuB,wBAAwB,GAAG,CAAC,CAAC,GAAG;MACjCxD,kBAAkB,EAAEiC,2BAA2B;MAC/CrC,kBAAkB,EAAEqC,2BAA2B;MAC/CM,KAAK,EAAEN,2BAA2B;MAClCzC,yBAAyB,EAAE;IAC7B,CAAC,CAAC,CAAC;EACL;EACA,MAAMiE,WAAW,GAAG9E,gBAAgB,CAAC,MAAM;IACzCgE,UAAU,CAAC;MACThC,KAAK,EAAEN,YAAY,CAACiC,UAAU;MAC9B7C,IAAI,EAAE,oBAAoB;MAC1BE,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAM+D,YAAY,GAAG/E,gBAAgB,CAAC,MAAM;IAC1CgE,UAAU,CAAC;MACThC,KAAK,EAAEtB,SAAS,CAACO,kBAAkB;MACnCH,IAAI,EAAE,oBAAoB;MAC1BE,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMgE,aAAa,GAAGhF,gBAAgB,CAAC,MAAM;IAC3CgE,UAAU,CAAC;MACThC,KAAK,EAAEtB,SAAS,CAACO,kBAAkB;MACnCH,IAAI,EAAE,oBAAoB;MAC1BE,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMiE,YAAY,GAAGjF,gBAAgB,CAAC,MAAM;IAC1CgE,UAAU,CAAC;MACThC,KAAK,EAAEtB,SAAS,CAACW,kBAAkB;MACnCP,IAAI,EAAE,oBAAoB;MAC1BE,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMkE,cAAc,GAAGlF,gBAAgB,CAAC,MAAM;IAC5CgE,UAAU,CAAC;MACThC,KAAK,EAAEN,YAAY,CAACyD,aAAa,CAACjC,KAAK,EAAEd,QAAQ,EAAET,SAAS,CAAC;MAC7Db,IAAI,EAAE,oBAAoB;MAC1BE,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMoE,UAAU,GAAGpF,gBAAgB,CAACqF,KAAK,IAAI;IAC3CA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBjC,SAAS,CAAC,IAAI,CAAC;EACjB,CAAC,CAAC;EACF,MAAMkC,WAAW,GAAGvF,gBAAgB,CAACqF,KAAK,IAAI;IAC5CA,KAAK,EAAEC,cAAc,CAAC,CAAC;IACvBjC,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC,CAAC;EACF,MAAMmC,YAAY,GAAGxF,gBAAgB,CAAC,CAACyF,QAAQ,EAAEvE,cAAc,GAAG,SAAS,KAAK8C,UAAU,CAAC;IACzFlD,IAAI,EAAE,kBAAkB;IACxBkB,KAAK,EAAEyD,QAAQ;IACfvE;EACF,CAAC,CAAC,CAAC;EACH,MAAMwE,oBAAoB,GAAG1F,gBAAgB,CAAC,CAACyF,QAAQ,EAAEnE,gBAAgB,EAAEsD,QAAQ,KAAKZ,UAAU,CAAC;IACjGlD,IAAI,EAAE,sBAAsB;IAC5BkB,KAAK,EAAEyD,QAAQ;IACfnE,gBAAgB;IAChBsD;EACF,CAAC,CAAC,CAAC;EACH,MAAMe,qBAAqB,GAAG3F,gBAAgB,CAAC,CAACyF,QAAQ,EAAEd,OAAO,KAAKX,UAAU,CAAC;IAC/ElD,IAAI,EAAE,mBAAmB;IACzBkB,KAAK,EAAEyD,QAAQ;IACfd;EACF,CAAC,CAAC,CAAC;EACH,MAAMiB,OAAO,GAAG;IACdC,OAAO,EAAEf,WAAW;IACpBhD,QAAQ,EAAEiD,YAAY;IACtBe,SAAS,EAAEd,aAAa;IACxBe,QAAQ,EAAEd,YAAY;IACtBe,UAAU,EAAEd,cAAc;IAC1Be,MAAM,EAAEb,UAAU;IAClBc,OAAO,EAAEX;EACX,CAAC;EACD,MAAMY,aAAa,GAAG;IACpBnE,KAAK,EAAEtB,SAAS,CAACkD,KAAK;IACtB7B,QAAQ,EAAE4D;EACZ,CAAC;EACD,MAAMS,SAAS,GAAGrG,KAAK,CAACsG,OAAO,CAAC,MAAM3E,YAAY,CAAC4E,UAAU,CAACpD,KAAK,EAAExC,SAAS,CAACkD,KAAK,CAAC,EAAE,CAACV,KAAK,EAAExB,YAAY,EAAEhB,SAAS,CAACkD,KAAK,CAAC,CAAC;EAC9H,MAAM2C,YAAY,GAAG;IACnBvE,KAAK,EAAEoE,SAAS;IAChBrE,QAAQ,EAAEyD,YAAY;IACtBU,OAAO,EAAEX,WAAW;IACpBiB,IAAI,EAAEpD;EACR,CAAC;EACD,MAAMqD,OAAO,GAAGC,WAAW,IAAI;IAC7B,MAAM5D,KAAK,GAAGjB,SAAS,CAAC;MACtBsB,OAAO;MACPnB,KAAK,EAAE0E,WAAW;MAClBtE,QAAQ;MACRX;IACF,CAAC,CAAC;IACF,OAAO,CAACC,YAAY,CAACiF,QAAQ,CAAC7D,KAAK,CAAC;EACtC,CAAC;EACD,MAAM8D,cAAc,GAAG9G,QAAQ,CAAC,CAAC,CAAC,EAAE8F,OAAO,EAAE;IAC3C5D,KAAK,EAAEoE,SAAS;IAChBrE,QAAQ,EAAEyD,YAAY;IACtBqB,gBAAgB,EAAEnB,oBAAoB;IACtCe;EACF,CAAC,CAAC;EACF,OAAO;IACLD,IAAI,EAAEpD,MAAM;IACZ0D,UAAU,EAAEX,aAAa;IACzBY,SAAS,EAAER,YAAY;IACvBS,WAAW,EAAEJ,cAAc;IAC3BhB;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}