{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.preKeyupBehavior = exports.preKeydownBehavior = exports.postKeyupBehavior = exports.keyupBehavior = exports.keypressBehavior = exports.keydownBehavior = void 0;\nvar _dom = require(\"@testing-library/dom\");\nvar _utils = require(\"../../utils\");\nvar _getEventProps = require(\"../getEventProps\");\nvar _shared = require(\"../shared\");\n\n/**\n * This file should contain behavior for functional keys as described here:\n * https://w3c.github.io/uievents-code/#key-alphanumeric-functional\n */\nconst modifierKeys = {\n  Alt: 'alt',\n  Control: 'ctrl',\n  Shift: 'shift',\n  Meta: 'meta'\n};\nconst preKeydownBehavior = [\n// modifierKeys switch on the modifier BEFORE the keydown event\n...Object.entries(modifierKeys).map(([key, modKey]) => ({\n  matches: keyDef => keyDef.key === key,\n  handle: (keyDef, element, options, state) => {\n    state.modifiers[modKey] = true;\n  }\n})),\n// AltGraph produces an extra keydown for Control\n// The modifier does not change\n{\n  matches: keyDef => keyDef.key === 'AltGraph',\n  handle: (keyDef, element, options, state) => {\n    var _options$keyboardMap$;\n    const ctrlKeyDef = (_options$keyboardMap$ = options.keyboardMap.find(k => k.key === 'Control')) != null ? _options$keyboardMap$ : /* istanbul ignore next */\n    {\n      key: 'Control',\n      code: 'Control'\n    };\n    _dom.fireEvent.keyDown(element, (0, _getEventProps.getKeyEventProps)(ctrlKeyDef, state));\n  }\n}];\nexports.preKeydownBehavior = preKeydownBehavior;\nconst keydownBehavior = [{\n  matches: keyDef => keyDef.key === 'CapsLock',\n  handle: (keyDef, element, options, state) => {\n    state.modifiers.caps = !state.modifiers.caps;\n  }\n}, {\n  matches: (keyDef, element) => keyDef.key === 'Backspace' && (0, _utils.isEditable)(element) && !(0, _utils.isCursorAtStart)(element),\n  handle: (keyDef, element, options, state) => {\n    const {\n      newValue,\n      newSelectionStart\n    } = (0, _utils.calculateNewValue)('', element, state.carryValue, undefined, 'backward');\n    (0, _shared.fireInputEvent)(element, {\n      newValue,\n      newSelectionStart,\n      eventOverrides: {\n        inputType: 'deleteContentBackward'\n      }\n    });\n    (0, _shared.carryValue)(element, state, newValue);\n  }\n}];\nexports.keydownBehavior = keydownBehavior;\nconst keypressBehavior = [{\n  matches: (keyDef, element) => keyDef.key === 'Enter' && (0, _utils.isElementType)(element, 'input') && ['checkbox', 'radio'].includes(element.type),\n  handle: (keyDef, element) => {\n    const form = element.form;\n    if ((0, _utils.hasFormSubmit)(form)) {\n      _dom.fireEvent.submit(form);\n    }\n  }\n}, {\n  matches: (keyDef, element) => keyDef.key === 'Enter' && ((0, _utils.isClickableInput)(element) ||\n  // Links with href defined should handle Enter the same as a click\n  (0, _utils.isElementType)(element, 'a') && Boolean(element.href)),\n  handle: (keyDef, element, options, state) => {\n    _dom.fireEvent.click(element, (0, _getEventProps.getMouseEventProps)(state));\n  }\n}, {\n  matches: (keyDef, element) => keyDef.key === 'Enter' && (0, _utils.isElementType)(element, 'input'),\n  handle: (keyDef, element) => {\n    const form = element.form;\n    if (form && (form.querySelectorAll('input').length === 1 || (0, _utils.hasFormSubmit)(form))) {\n      _dom.fireEvent.submit(form);\n    }\n  }\n}];\nexports.keypressBehavior = keypressBehavior;\nconst preKeyupBehavior = [\n// modifierKeys switch off the modifier BEFORE the keyup event\n...Object.entries(modifierKeys).map(([key, modKey]) => ({\n  matches: keyDef => keyDef.key === key,\n  handle: (keyDef, element, options, state) => {\n    state.modifiers[modKey] = false;\n  }\n}))];\nexports.preKeyupBehavior = preKeyupBehavior;\nconst keyupBehavior = [{\n  matches: (keyDef, element) => keyDef.key === ' ' && (0, _utils.isClickableInput)(element),\n  handle: (keyDef, element, options, state) => {\n    _dom.fireEvent.click(element, (0, _getEventProps.getMouseEventProps)(state));\n  }\n}];\nexports.keyupBehavior = keyupBehavior;\nconst postKeyupBehavior = [\n// AltGraph produces an extra keyup for Control\n// The modifier does not change\n{\n  matches: keyDef => keyDef.key === 'AltGraph',\n  handle: (keyDef, element, options, state) => {\n    var _options$keyboardMap$2;\n    const ctrlKeyDef = (_options$keyboardMap$2 = options.keyboardMap.find(k => k.key === 'Control')) != null ? _options$keyboardMap$2 : /* istanbul ignore next */\n    {\n      key: 'Control',\n      code: 'Control'\n    };\n    _dom.fireEvent.keyUp(element, (0, _getEventProps.getKeyEventProps)(ctrlKeyDef, state));\n  }\n}];\nexports.postKeyupBehavior = postKeyupBehavior;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "preKeyupBehavior", "preKeydownBehavior", "postKeyupBehavior", "keyup<PERSON><PERSON><PERSON><PERSON>", "keypressBehavior", "keydownBehavior", "_dom", "require", "_utils", "_getEventProps", "_shared", "modifierKeys", "Alt", "Control", "Shift", "Meta", "entries", "map", "key", "mod<PERSON>ey", "matches", "keyDef", "handle", "element", "options", "state", "modifiers", "_options$keyboardMap$", "ctrlKeyDef", "keyboardMap", "find", "k", "code", "fireEvent", "keyDown", "getKeyEventProps", "caps", "isEditable", "isCursorAtStart", "newValue", "newSelectionStart", "calculateNewValue", "carryValue", "undefined", "fireInputEvent", "eventOverrides", "inputType", "isElementType", "includes", "type", "form", "hasFormSubmit", "submit", "isClickableInput", "Boolean", "href", "click", "getMouseEventProps", "querySelectorAll", "length", "_options$keyboardMap$2", "keyUp"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/user-event/dist/keyboard/plugins/functional.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.preKeyupBehavior = exports.preKeydownBehavior = exports.postKeyupBehavior = exports.keyupBehavior = exports.keypressBehavior = exports.keydownBehavior = void 0;\n\nvar _dom = require(\"@testing-library/dom\");\n\nvar _utils = require(\"../../utils\");\n\nvar _getEventProps = require(\"../getEventProps\");\n\nvar _shared = require(\"../shared\");\n\n/**\n * This file should contain behavior for functional keys as described here:\n * https://w3c.github.io/uievents-code/#key-alphanumeric-functional\n */\nconst modifierKeys = {\n  Alt: 'alt',\n  Control: 'ctrl',\n  Shift: 'shift',\n  Meta: 'meta'\n};\nconst preKeydownBehavior = [// modifierKeys switch on the modifier BEFORE the keydown event\n...Object.entries(modifierKeys).map(([key, modKey]) => ({\n  matches: keyDef => keyDef.key === key,\n  handle: (keyDef, element, options, state) => {\n    state.modifiers[modKey] = true;\n  }\n})), // AltGraph produces an extra keydown for Control\n// The modifier does not change\n{\n  matches: keyDef => keyDef.key === 'AltGraph',\n  handle: (keyDef, element, options, state) => {\n    var _options$keyboardMap$;\n\n    const ctrlKeyDef = (_options$keyboardMap$ = options.keyboardMap.find(k => k.key === 'Control')) != null ? _options$keyboardMap$ :\n    /* istanbul ignore next */\n    {\n      key: 'Control',\n      code: 'Control'\n    };\n\n    _dom.fireEvent.keyDown(element, (0, _getEventProps.getKeyEventProps)(ctrlKeyDef, state));\n  }\n}];\nexports.preKeydownBehavior = preKeydownBehavior;\nconst keydownBehavior = [{\n  matches: keyDef => keyDef.key === 'CapsLock',\n  handle: (keyDef, element, options, state) => {\n    state.modifiers.caps = !state.modifiers.caps;\n  }\n}, {\n  matches: (keyDef, element) => keyDef.key === 'Backspace' && (0, _utils.isEditable)(element) && !(0, _utils.isCursorAtStart)(element),\n  handle: (keyDef, element, options, state) => {\n    const {\n      newValue,\n      newSelectionStart\n    } = (0, _utils.calculateNewValue)('', element, state.carryValue, undefined, 'backward');\n    (0, _shared.fireInputEvent)(element, {\n      newValue,\n      newSelectionStart,\n      eventOverrides: {\n        inputType: 'deleteContentBackward'\n      }\n    });\n    (0, _shared.carryValue)(element, state, newValue);\n  }\n}];\nexports.keydownBehavior = keydownBehavior;\nconst keypressBehavior = [{\n  matches: (keyDef, element) => keyDef.key === 'Enter' && (0, _utils.isElementType)(element, 'input') && ['checkbox', 'radio'].includes(element.type),\n  handle: (keyDef, element) => {\n    const form = element.form;\n\n    if ((0, _utils.hasFormSubmit)(form)) {\n      _dom.fireEvent.submit(form);\n    }\n  }\n}, {\n  matches: (keyDef, element) => keyDef.key === 'Enter' && ((0, _utils.isClickableInput)(element) || // Links with href defined should handle Enter the same as a click\n  (0, _utils.isElementType)(element, 'a') && Boolean(element.href)),\n  handle: (keyDef, element, options, state) => {\n    _dom.fireEvent.click(element, (0, _getEventProps.getMouseEventProps)(state));\n  }\n}, {\n  matches: (keyDef, element) => keyDef.key === 'Enter' && (0, _utils.isElementType)(element, 'input'),\n  handle: (keyDef, element) => {\n    const form = element.form;\n\n    if (form && (form.querySelectorAll('input').length === 1 || (0, _utils.hasFormSubmit)(form))) {\n      _dom.fireEvent.submit(form);\n    }\n  }\n}];\nexports.keypressBehavior = keypressBehavior;\nconst preKeyupBehavior = [// modifierKeys switch off the modifier BEFORE the keyup event\n...Object.entries(modifierKeys).map(([key, modKey]) => ({\n  matches: keyDef => keyDef.key === key,\n  handle: (keyDef, element, options, state) => {\n    state.modifiers[modKey] = false;\n  }\n}))];\nexports.preKeyupBehavior = preKeyupBehavior;\nconst keyupBehavior = [{\n  matches: (keyDef, element) => keyDef.key === ' ' && (0, _utils.isClickableInput)(element),\n  handle: (keyDef, element, options, state) => {\n    _dom.fireEvent.click(element, (0, _getEventProps.getMouseEventProps)(state));\n  }\n}];\nexports.keyupBehavior = keyupBehavior;\nconst postKeyupBehavior = [// AltGraph produces an extra keyup for Control\n// The modifier does not change\n{\n  matches: keyDef => keyDef.key === 'AltGraph',\n  handle: (keyDef, element, options, state) => {\n    var _options$keyboardMap$2;\n\n    const ctrlKeyDef = (_options$keyboardMap$2 = options.keyboardMap.find(k => k.key === 'Control')) != null ? _options$keyboardMap$2 :\n    /* istanbul ignore next */\n    {\n      key: 'Control',\n      code: 'Control'\n    };\n\n    _dom.fireEvent.keyUp(element, (0, _getEventProps.getKeyEventProps)(ctrlKeyDef, state));\n  }\n}];\nexports.postKeyupBehavior = postKeyupBehavior;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,gBAAgB,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,iBAAiB,GAAGJ,OAAO,CAACK,aAAa,GAAGL,OAAO,CAACM,gBAAgB,GAAGN,OAAO,CAACO,eAAe,GAAG,KAAK,CAAC;AAEvK,IAAIC,IAAI,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAE1C,IAAIC,MAAM,GAAGD,OAAO,CAAC,aAAa,CAAC;AAEnC,IAAIE,cAAc,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AAEhD,IAAIG,OAAO,GAAGH,OAAO,CAAC,WAAW,CAAC;;AAElC;AACA;AACA;AACA;AACA,MAAMI,YAAY,GAAG;EACnBC,GAAG,EAAE,KAAK;EACVC,OAAO,EAAE,MAAM;EACfC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE;AACR,CAAC;AACD,MAAMd,kBAAkB,GAAG;AAAC;AAC5B,GAAGL,MAAM,CAACoB,OAAO,CAACL,YAAY,CAAC,CAACM,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,MAAM,CAAC,MAAM;EACtDC,OAAO,EAAEC,MAAM,IAAIA,MAAM,CAACH,GAAG,KAAKA,GAAG;EACrCI,MAAM,EAAEA,CAACD,MAAM,EAAEE,OAAO,EAAEC,OAAO,EAAEC,KAAK,KAAK;IAC3CA,KAAK,CAACC,SAAS,CAACP,MAAM,CAAC,GAAG,IAAI;EAChC;AACF,CAAC,CAAC,CAAC;AAAE;AACL;AACA;EACEC,OAAO,EAAEC,MAAM,IAAIA,MAAM,CAACH,GAAG,KAAK,UAAU;EAC5CI,MAAM,EAAEA,CAACD,MAAM,EAAEE,OAAO,EAAEC,OAAO,EAAEC,KAAK,KAAK;IAC3C,IAAIE,qBAAqB;IAEzB,MAAMC,UAAU,GAAG,CAACD,qBAAqB,GAAGH,OAAO,CAACK,WAAW,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACb,GAAG,KAAK,SAAS,CAAC,KAAK,IAAI,GAAGS,qBAAqB,GAC/H;IACA;MACET,GAAG,EAAE,SAAS;MACdc,IAAI,EAAE;IACR,CAAC;IAED1B,IAAI,CAAC2B,SAAS,CAACC,OAAO,CAACX,OAAO,EAAE,CAAC,CAAC,EAAEd,cAAc,CAAC0B,gBAAgB,EAAEP,UAAU,EAAEH,KAAK,CAAC,CAAC;EAC1F;AACF,CAAC,CAAC;AACF3B,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,MAAMI,eAAe,GAAG,CAAC;EACvBe,OAAO,EAAEC,MAAM,IAAIA,MAAM,CAACH,GAAG,KAAK,UAAU;EAC5CI,MAAM,EAAEA,CAACD,MAAM,EAAEE,OAAO,EAAEC,OAAO,EAAEC,KAAK,KAAK;IAC3CA,KAAK,CAACC,SAAS,CAACU,IAAI,GAAG,CAACX,KAAK,CAACC,SAAS,CAACU,IAAI;EAC9C;AACF,CAAC,EAAE;EACDhB,OAAO,EAAEA,CAACC,MAAM,EAAEE,OAAO,KAAKF,MAAM,CAACH,GAAG,KAAK,WAAW,IAAI,CAAC,CAAC,EAAEV,MAAM,CAAC6B,UAAU,EAAEd,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEf,MAAM,CAAC8B,eAAe,EAAEf,OAAO,CAAC;EACpID,MAAM,EAAEA,CAACD,MAAM,EAAEE,OAAO,EAAEC,OAAO,EAAEC,KAAK,KAAK;IAC3C,MAAM;MACJc,QAAQ;MACRC;IACF,CAAC,GAAG,CAAC,CAAC,EAAEhC,MAAM,CAACiC,iBAAiB,EAAE,EAAE,EAAElB,OAAO,EAAEE,KAAK,CAACiB,UAAU,EAAEC,SAAS,EAAE,UAAU,CAAC;IACvF,CAAC,CAAC,EAAEjC,OAAO,CAACkC,cAAc,EAAErB,OAAO,EAAE;MACnCgB,QAAQ;MACRC,iBAAiB;MACjBK,cAAc,EAAE;QACdC,SAAS,EAAE;MACb;IACF,CAAC,CAAC;IACF,CAAC,CAAC,EAAEpC,OAAO,CAACgC,UAAU,EAAEnB,OAAO,EAAEE,KAAK,EAAEc,QAAQ,CAAC;EACnD;AACF,CAAC,CAAC;AACFzC,OAAO,CAACO,eAAe,GAAGA,eAAe;AACzC,MAAMD,gBAAgB,GAAG,CAAC;EACxBgB,OAAO,EAAEA,CAACC,MAAM,EAAEE,OAAO,KAAKF,MAAM,CAACH,GAAG,KAAK,OAAO,IAAI,CAAC,CAAC,EAAEV,MAAM,CAACuC,aAAa,EAAExB,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAACyB,QAAQ,CAACzB,OAAO,CAAC0B,IAAI,CAAC;EACnJ3B,MAAM,EAAEA,CAACD,MAAM,EAAEE,OAAO,KAAK;IAC3B,MAAM2B,IAAI,GAAG3B,OAAO,CAAC2B,IAAI;IAEzB,IAAI,CAAC,CAAC,EAAE1C,MAAM,CAAC2C,aAAa,EAAED,IAAI,CAAC,EAAE;MACnC5C,IAAI,CAAC2B,SAAS,CAACmB,MAAM,CAACF,IAAI,CAAC;IAC7B;EACF;AACF,CAAC,EAAE;EACD9B,OAAO,EAAEA,CAACC,MAAM,EAAEE,OAAO,KAAKF,MAAM,CAACH,GAAG,KAAK,OAAO,KAAK,CAAC,CAAC,EAAEV,MAAM,CAAC6C,gBAAgB,EAAE9B,OAAO,CAAC;EAAI;EAClG,CAAC,CAAC,EAAEf,MAAM,CAACuC,aAAa,EAAExB,OAAO,EAAE,GAAG,CAAC,IAAI+B,OAAO,CAAC/B,OAAO,CAACgC,IAAI,CAAC,CAAC;EACjEjC,MAAM,EAAEA,CAACD,MAAM,EAAEE,OAAO,EAAEC,OAAO,EAAEC,KAAK,KAAK;IAC3CnB,IAAI,CAAC2B,SAAS,CAACuB,KAAK,CAACjC,OAAO,EAAE,CAAC,CAAC,EAAEd,cAAc,CAACgD,kBAAkB,EAAEhC,KAAK,CAAC,CAAC;EAC9E;AACF,CAAC,EAAE;EACDL,OAAO,EAAEA,CAACC,MAAM,EAAEE,OAAO,KAAKF,MAAM,CAACH,GAAG,KAAK,OAAO,IAAI,CAAC,CAAC,EAAEV,MAAM,CAACuC,aAAa,EAAExB,OAAO,EAAE,OAAO,CAAC;EACnGD,MAAM,EAAEA,CAACD,MAAM,EAAEE,OAAO,KAAK;IAC3B,MAAM2B,IAAI,GAAG3B,OAAO,CAAC2B,IAAI;IAEzB,IAAIA,IAAI,KAAKA,IAAI,CAACQ,gBAAgB,CAAC,OAAO,CAAC,CAACC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,EAAEnD,MAAM,CAAC2C,aAAa,EAAED,IAAI,CAAC,CAAC,EAAE;MAC5F5C,IAAI,CAAC2B,SAAS,CAACmB,MAAM,CAACF,IAAI,CAAC;IAC7B;EACF;AACF,CAAC,CAAC;AACFpD,OAAO,CAACM,gBAAgB,GAAGA,gBAAgB;AAC3C,MAAMJ,gBAAgB,GAAG;AAAC;AAC1B,GAAGJ,MAAM,CAACoB,OAAO,CAACL,YAAY,CAAC,CAACM,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,MAAM,CAAC,MAAM;EACtDC,OAAO,EAAEC,MAAM,IAAIA,MAAM,CAACH,GAAG,KAAKA,GAAG;EACrCI,MAAM,EAAEA,CAACD,MAAM,EAAEE,OAAO,EAAEC,OAAO,EAAEC,KAAK,KAAK;IAC3CA,KAAK,CAACC,SAAS,CAACP,MAAM,CAAC,GAAG,KAAK;EACjC;AACF,CAAC,CAAC,CAAC,CAAC;AACJrB,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB;AAC3C,MAAMG,aAAa,GAAG,CAAC;EACrBiB,OAAO,EAAEA,CAACC,MAAM,EAAEE,OAAO,KAAKF,MAAM,CAACH,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,EAAEV,MAAM,CAAC6C,gBAAgB,EAAE9B,OAAO,CAAC;EACzFD,MAAM,EAAEA,CAACD,MAAM,EAAEE,OAAO,EAAEC,OAAO,EAAEC,KAAK,KAAK;IAC3CnB,IAAI,CAAC2B,SAAS,CAACuB,KAAK,CAACjC,OAAO,EAAE,CAAC,CAAC,EAAEd,cAAc,CAACgD,kBAAkB,EAAEhC,KAAK,CAAC,CAAC;EAC9E;AACF,CAAC,CAAC;AACF3B,OAAO,CAACK,aAAa,GAAGA,aAAa;AACrC,MAAMD,iBAAiB,GAAG;AAAC;AAC3B;AACA;EACEkB,OAAO,EAAEC,MAAM,IAAIA,MAAM,CAACH,GAAG,KAAK,UAAU;EAC5CI,MAAM,EAAEA,CAACD,MAAM,EAAEE,OAAO,EAAEC,OAAO,EAAEC,KAAK,KAAK;IAC3C,IAAImC,sBAAsB;IAE1B,MAAMhC,UAAU,GAAG,CAACgC,sBAAsB,GAAGpC,OAAO,CAACK,WAAW,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACb,GAAG,KAAK,SAAS,CAAC,KAAK,IAAI,GAAG0C,sBAAsB,GACjI;IACA;MACE1C,GAAG,EAAE,SAAS;MACdc,IAAI,EAAE;IACR,CAAC;IAED1B,IAAI,CAAC2B,SAAS,CAAC4B,KAAK,CAACtC,OAAO,EAAE,CAAC,CAAC,EAAEd,cAAc,CAAC0B,gBAAgB,EAAEP,UAAU,EAAEH,KAAK,CAAC,CAAC;EACxF;AACF,CAAC,CAAC;AACF3B,OAAO,CAACI,iBAAiB,GAAGA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}