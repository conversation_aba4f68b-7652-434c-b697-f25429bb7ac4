{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar listitemRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-level': null,\n    'aria-posinset': null,\n    'aria-setsize': null\n  },\n  relatedConcepts: [{\n    concept: {\n      constraints: ['direct descendant of ol', 'direct descendant of ul', 'direct descendant of menu'],\n      name: 'li'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'item'\n    },\n    module: 'XForms'\n  }],\n  requireContextRole: ['directory', 'list'],\n  requiredContextRole: ['directory', 'list'],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = listitemRole;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "listitemRole", "abstract", "accessibleNameRequired", "baseConcepts", "childrenPresentational", "nameFrom", "prohibitedProps", "props", "relatedConcepts", "concept", "constraints", "name", "module", "requireContextRole", "requiredContextRole", "requiredOwnedElements", "requiredProps", "superClass", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/etc/roles/literal/listitemRole.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar listitemRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-level': null,\n    'aria-posinset': null,\n    'aria-setsize': null\n  },\n  relatedConcepts: [{\n    concept: {\n      constraints: ['direct descendant of ol', 'direct descendant of ul', 'direct descendant of menu'],\n      name: 'li'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'item'\n    },\n    module: 'XForms'\n  }],\n  requireContextRole: ['directory', 'list'],\n  requiredContextRole: ['directory', 'list'],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = listitemRole;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,YAAY,GAAG;EACjBC,QAAQ,EAAE,KAAK;EACfC,sBAAsB,EAAE,KAAK;EAC7BC,YAAY,EAAE,EAAE;EAChBC,sBAAsB,EAAE,KAAK;EAC7BC,QAAQ,EAAE,CAAC,QAAQ,CAAC;EACpBC,eAAe,EAAE,EAAE;EACnBC,KAAK,EAAE;IACL,YAAY,EAAE,IAAI;IAClB,eAAe,EAAE,IAAI;IACrB,cAAc,EAAE;EAClB,CAAC;EACDC,eAAe,EAAE,CAAC;IAChBC,OAAO,EAAE;MACPC,WAAW,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,2BAA2B,CAAC;MAChGC,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,EAAE;IACDH,OAAO,EAAE;MACPE,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,CAAC;EACFC,kBAAkB,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC;EACzCC,mBAAmB,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC;EAC1CC,qBAAqB,EAAE,EAAE;EACzBC,aAAa,EAAE,CAAC,CAAC;EACjBC,UAAU,EAAE,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC;AACnD,CAAC;AACD,IAAIC,QAAQ,GAAGlB,YAAY;AAC3BH,OAAO,CAACE,OAAO,GAAGmB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}