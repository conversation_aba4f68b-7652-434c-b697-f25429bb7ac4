{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport { refType } from '@mui/utils';\nimport { useMobilePicker } from \"../internals/hooks/useMobilePicker/index.js\";\nimport { useDatePickerDefaultizedProps } from \"../DatePicker/shared.js\";\nimport { usePickersTranslations } from \"../hooks/usePickersTranslations.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { extractValidationProps, validateDate } from \"../validation/index.js\";\nimport { DateField } from \"../DateField/index.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { renderDateViewCalendar } from \"../dateViewRenderers/index.js\";\nimport { resolveDateFormat } from \"../internals/utils/date-utils.js\";\nimport { buildGetOpenDialogAriaText } from \"../locales/utils/getPickersLocalization.js\";\n/**\n * Demos:\n *\n * - [DatePicker](https://mui.com/x/react-date-pickers/date-picker/)\n * - [Validation](https://mui.com/x/react-date-pickers/validation/)\n *\n * API:\n *\n * - [MobileDatePicker API](https://mui.com/x/api/date-pickers/mobile-date-picker/)\n */\nconst MobileDatePicker = /*#__PURE__*/React.forwardRef(function MobileDatePicker(inProps, ref) {\n  const translations = usePickersTranslations();\n  const utils = useUtils();\n\n  // Props with the default values common to all date pickers\n  const defaultizedProps = useDatePickerDefaultizedProps(inProps, 'MuiMobileDatePicker');\n  const viewRenderers = _extends({\n    day: renderDateViewCalendar,\n    month: renderDateViewCalendar,\n    year: renderDateViewCalendar\n  }, defaultizedProps.viewRenderers);\n\n  // Props with the default values specific to the mobile variant\n  const props = _extends({}, defaultizedProps, {\n    viewRenderers,\n    format: resolveDateFormat(utils, defaultizedProps, false),\n    slots: _extends({\n      field: DateField\n    }, defaultizedProps.slots),\n    slotProps: _extends({}, defaultizedProps.slotProps, {\n      field: ownerState => _extends({}, resolveComponentProps(defaultizedProps.slotProps?.field, ownerState), extractValidationProps(defaultizedProps), {\n        ref\n      }),\n      toolbar: _extends({\n        hidden: false\n      }, defaultizedProps.slotProps?.toolbar)\n    })\n  });\n  const {\n    renderPicker\n  } = useMobilePicker({\n    props,\n    valueManager: singleItemValueManager,\n    valueType: 'date',\n    getOpenDialogAriaText: buildGetOpenDialogAriaText({\n      utils,\n      formatKey: 'fullDate',\n      contextTranslation: translations.openDatePickerDialogue,\n      propsTranslation: props.localeText?.openDatePickerDialogue\n    }),\n    validator: validateDate\n  });\n  return renderPicker();\n});\nMobileDatePicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  className: PropTypes.string,\n  /**\n   * If `true`, the popover or modal will close after submitting the full date.\n   * @default `true` for desktop, `false` for mobile (based on the chosen wrapper and `desktopModeMediaQuery` prop).\n   */\n  closeOnSelect: PropTypes.bool,\n  /**\n   * Formats the day of week displayed in the calendar header.\n   * @param {TDate} date The date of the day of week provided by the adapter.\n   * @returns {string} The name to display.\n   * @default (date: TDate) => adapter.format(date, 'weekdayShort').charAt(0).toUpperCase()\n   */\n  dayOfWeekFormatter: PropTypes.func,\n  /**\n   * The default value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the picker and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: PropTypes.bool,\n  /**\n   * If `true`, the open picker button will not be rendered (renders only the field).\n   * @default false\n   */\n  disableOpenPicker: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * If `true`, the week number will be display in the calendar.\n   */\n  displayWeekNumber: PropTypes.bool,\n  /**\n   * @default false\n   */\n  enableAccessibleFieldDOMStructure: PropTypes.any,\n  /**\n   * The day view will show as many weeks as needed after the end of the current month to match this value.\n   * Put it to 6 to have a fixed number of weeks in Gregorian calendars\n   */\n  fixedWeekNumber: PropTypes.number,\n  /**\n   * Format of the date when rendered in the input(s).\n   * Defaults to localized format based on the used `views`.\n   */\n  format: PropTypes.string,\n  /**\n   * Density of the format when rendered in the input.\n   * Setting `formatDensity` to `\"spacious\"` will add a space before and after each `/`, `-` and `.` character.\n   * @default \"dense\"\n   */\n  formatDensity: PropTypes.oneOf(['dense', 'spacious']),\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * The label content.\n   */\n  label: PropTypes.node,\n  /**\n   * If `true`, calls `renderLoading` instead of rendering the day calendar.\n   * Can be used to preload information and show it in calendar.\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Locale for components texts.\n   * Allows overriding texts coming from `LocalizationProvider` and `theme`.\n   */\n  localeText: PropTypes.object,\n  /**\n   * Maximal selectable date.\n   * @default 2099-12-31\n   */\n  maxDate: PropTypes.object,\n  /**\n   * Minimal selectable date.\n   * @default 1900-01-01\n   */\n  minDate: PropTypes.object,\n  /**\n   * Months rendered per row.\n   * @default 3\n   */\n  monthsPerRow: PropTypes.oneOf([3, 4]),\n  /**\n   * Name attribute used by the `input` element in the Field.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value is accepted.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The value that was just accepted.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onAccept: PropTypes.func,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The new value.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see `open`).\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the error associated with the current value changes.\n   * When a validation error is detected, the `error` parameter contains a non-null value.\n   * This can be used to render an appropriate form error.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TError} error The reason why the current value is not valid.\n   * @param {TValue} value The value associated with the error.\n   */\n  onError: PropTypes.func,\n  /**\n   * Callback fired on month change.\n   * @template TDate\n   * @param {TDate} month The new month.\n   */\n  onMonthChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see `open`).\n   */\n  onOpen: PropTypes.func,\n  /**\n   * Callback fired when the selected sections change.\n   * @param {FieldSelectedSections} newValue The new selected sections.\n   */\n  onSelectedSectionsChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * Callback fired on year change.\n   * @template TDate\n   * @param {TDate} year The new year.\n   */\n  onYearChange: PropTypes.func,\n  /**\n   * Control the popup or dialog open state.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['day', 'month', 'year']),\n  /**\n   * Force rendering in particular orientation.\n   */\n  orientation: PropTypes.oneOf(['landscape', 'portrait']),\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, disable heavy animations.\n   * @default `@media(prefers-reduced-motion: reduce)` || `navigator.userAgent` matches Android <10 or iOS <13\n   */\n  reduceAnimations: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid date-time using the validation props, except callbacks like `shouldDisable<...>`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Component displaying when passed `loading` true.\n   * @returns {React.ReactNode} The node to render when loading.\n   * @default () => <span data-mui-test=\"loading-progress\">...</span>\n   */\n  renderLoading: PropTypes.func,\n  /**\n   * The currently selected sections.\n   * This prop accepts four formats:\n   * 1. If a number is provided, the section at this index will be selected.\n   * 2. If a string of type `FieldSectionType` is provided, the first section with that name will be selected.\n   * 3. If `\"all\"` is provided, all the sections will be selected.\n   * 4. If `null` is provided, no section will be selected.\n   * If not provided, the selected sections will be handled internally.\n   */\n  selectedSections: PropTypes.oneOfType([PropTypes.oneOf(['all', 'day', 'empty', 'hours', 'meridiem', 'minutes', 'month', 'seconds', 'weekDay', 'year']), PropTypes.number]),\n  /**\n   * Disable specific date.\n   *\n   * Warning: This function can be called multiple times (for example when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.\n   *\n   * @template TDate\n   * @param {TDate} day The date to test.\n   * @returns {boolean} If `true` the date will be disabled.\n   */\n  shouldDisableDate: PropTypes.func,\n  /**\n   * Disable specific month.\n   * @template TDate\n   * @param {TDate} month The month to test.\n   * @returns {boolean} If `true`, the month will be disabled.\n   */\n  shouldDisableMonth: PropTypes.func,\n  /**\n   * Disable specific year.\n   * @template TDate\n   * @param {TDate} year The year to test.\n   * @returns {boolean} If `true`, the year will be disabled.\n   */\n  shouldDisableYear: PropTypes.func,\n  /**\n   * If `true`, days outside the current month are rendered:\n   *\n   * - if `fixedWeekNumber` is defined, renders days to have the weeks requested.\n   *\n   * - if `fixedWeekNumber` is not defined, renders day to fill the first and last week of the current month.\n   *\n   * - ignored if `calendars` equals more than `1` on range pickers.\n   * @default false\n   */\n  showDaysOutsideCurrentMonth: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['day', 'month', 'year']),\n  /**\n   * Define custom view renderers for each section.\n   * If `null`, the section will only have field editing.\n   * If `undefined`, internally defined view will be used.\n   */\n  viewRenderers: PropTypes.shape({\n    day: PropTypes.func,\n    month: PropTypes.func,\n    year: PropTypes.func\n  }),\n  /**\n   * Available views.\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'month', 'year']).isRequired),\n  /**\n   * Years are displayed in ascending (chronological) order by default.\n   * If `desc`, years are displayed in descending order.\n   * @default 'asc'\n   */\n  yearsOrder: PropTypes.oneOf(['asc', 'desc']),\n  /**\n   * Years rendered per row.\n   * @default 3\n   */\n  yearsPerRow: PropTypes.oneOf([3, 4])\n};\nexport { MobileDatePicker };", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "resolveComponentProps", "refType", "useMobilePicker", "useDatePickerDefaultizedProps", "usePickersTranslations", "useUtils", "extractValidationProps", "validateDate", "DateField", "singleItemValueManager", "renderDateViewCalendar", "resolveDateFormat", "buildGetOpenDialogAriaText", "MobileDatePicker", "forwardRef", "inProps", "ref", "translations", "utils", "defaultizedProps", "viewRenderers", "day", "month", "year", "props", "format", "slots", "field", "slotProps", "ownerState", "toolbar", "hidden", "renderPicker", "valueManager", "valueType", "getOpenDialogAriaText", "formatKey", "contextTranslation", "openDatePickerDialogue", "propsTranslation", "localeText", "validator", "propTypes", "autoFocus", "bool", "className", "string", "closeOnSelect", "dayOfWeekFormatter", "func", "defaultValue", "object", "disabled", "disableFuture", "disableHighlightToday", "disableOpenPicker", "disablePast", "displayWeekNumber", "enableAccessibleFieldDOMStructure", "any", "fixedWeekNumber", "number", "formatDensity", "oneOf", "inputRef", "label", "node", "loading", "maxDate", "minDate", "monthsPerRow", "name", "onAccept", "onChange", "onClose", "onError", "onMonthChange", "onOpen", "onSelectedSectionsChange", "onViewChange", "onYearChange", "open", "openTo", "orientation", "readOnly", "reduceAnimations", "referenceDate", "renderLoading", "selectedSections", "oneOfType", "shouldDisableDate", "shouldDisableMonth", "shouldDisableYear", "showDaysOutsideCurrentMonth", "sx", "arrayOf", "timezone", "value", "view", "shape", "views", "isRequired", "yearsOrder", "yearsPerRow"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/MobileDatePicker/MobileDatePicker.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport { refType } from '@mui/utils';\nimport { useMobilePicker } from \"../internals/hooks/useMobilePicker/index.js\";\nimport { useDatePickerDefaultizedProps } from \"../DatePicker/shared.js\";\nimport { usePickersTranslations } from \"../hooks/usePickersTranslations.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { extractValidationProps, validateDate } from \"../validation/index.js\";\nimport { DateField } from \"../DateField/index.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { renderDateViewCalendar } from \"../dateViewRenderers/index.js\";\nimport { resolveDateFormat } from \"../internals/utils/date-utils.js\";\nimport { buildGetOpenDialogAriaText } from \"../locales/utils/getPickersLocalization.js\";\n/**\n * Demos:\n *\n * - [DatePicker](https://mui.com/x/react-date-pickers/date-picker/)\n * - [Validation](https://mui.com/x/react-date-pickers/validation/)\n *\n * API:\n *\n * - [MobileDatePicker API](https://mui.com/x/api/date-pickers/mobile-date-picker/)\n */\nconst MobileDatePicker = /*#__PURE__*/React.forwardRef(function MobileDatePicker(inProps, ref) {\n  const translations = usePickersTranslations();\n  const utils = useUtils();\n\n  // Props with the default values common to all date pickers\n  const defaultizedProps = useDatePickerDefaultizedProps(inProps, 'MuiMobileDatePicker');\n  const viewRenderers = _extends({\n    day: renderDateViewCalendar,\n    month: renderDateViewCalendar,\n    year: renderDateViewCalendar\n  }, defaultizedProps.viewRenderers);\n\n  // Props with the default values specific to the mobile variant\n  const props = _extends({}, defaultizedProps, {\n    viewRenderers,\n    format: resolveDateFormat(utils, defaultizedProps, false),\n    slots: _extends({\n      field: DateField\n    }, defaultizedProps.slots),\n    slotProps: _extends({}, defaultizedProps.slotProps, {\n      field: ownerState => _extends({}, resolveComponentProps(defaultizedProps.slotProps?.field, ownerState), extractValidationProps(defaultizedProps), {\n        ref\n      }),\n      toolbar: _extends({\n        hidden: false\n      }, defaultizedProps.slotProps?.toolbar)\n    })\n  });\n  const {\n    renderPicker\n  } = useMobilePicker({\n    props,\n    valueManager: singleItemValueManager,\n    valueType: 'date',\n    getOpenDialogAriaText: buildGetOpenDialogAriaText({\n      utils,\n      formatKey: 'fullDate',\n      contextTranslation: translations.openDatePickerDialogue,\n      propsTranslation: props.localeText?.openDatePickerDialogue\n    }),\n    validator: validateDate\n  });\n  return renderPicker();\n});\nMobileDatePicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  className: PropTypes.string,\n  /**\n   * If `true`, the popover or modal will close after submitting the full date.\n   * @default `true` for desktop, `false` for mobile (based on the chosen wrapper and `desktopModeMediaQuery` prop).\n   */\n  closeOnSelect: PropTypes.bool,\n  /**\n   * Formats the day of week displayed in the calendar header.\n   * @param {TDate} date The date of the day of week provided by the adapter.\n   * @returns {string} The name to display.\n   * @default (date: TDate) => adapter.format(date, 'weekdayShort').charAt(0).toUpperCase()\n   */\n  dayOfWeekFormatter: PropTypes.func,\n  /**\n   * The default value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the picker and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: PropTypes.bool,\n  /**\n   * If `true`, the open picker button will not be rendered (renders only the field).\n   * @default false\n   */\n  disableOpenPicker: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * If `true`, the week number will be display in the calendar.\n   */\n  displayWeekNumber: PropTypes.bool,\n  /**\n   * @default false\n   */\n  enableAccessibleFieldDOMStructure: PropTypes.any,\n  /**\n   * The day view will show as many weeks as needed after the end of the current month to match this value.\n   * Put it to 6 to have a fixed number of weeks in Gregorian calendars\n   */\n  fixedWeekNumber: PropTypes.number,\n  /**\n   * Format of the date when rendered in the input(s).\n   * Defaults to localized format based on the used `views`.\n   */\n  format: PropTypes.string,\n  /**\n   * Density of the format when rendered in the input.\n   * Setting `formatDensity` to `\"spacious\"` will add a space before and after each `/`, `-` and `.` character.\n   * @default \"dense\"\n   */\n  formatDensity: PropTypes.oneOf(['dense', 'spacious']),\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * The label content.\n   */\n  label: PropTypes.node,\n  /**\n   * If `true`, calls `renderLoading` instead of rendering the day calendar.\n   * Can be used to preload information and show it in calendar.\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Locale for components texts.\n   * Allows overriding texts coming from `LocalizationProvider` and `theme`.\n   */\n  localeText: PropTypes.object,\n  /**\n   * Maximal selectable date.\n   * @default 2099-12-31\n   */\n  maxDate: PropTypes.object,\n  /**\n   * Minimal selectable date.\n   * @default 1900-01-01\n   */\n  minDate: PropTypes.object,\n  /**\n   * Months rendered per row.\n   * @default 3\n   */\n  monthsPerRow: PropTypes.oneOf([3, 4]),\n  /**\n   * Name attribute used by the `input` element in the Field.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value is accepted.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The value that was just accepted.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onAccept: PropTypes.func,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The new value.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see `open`).\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the error associated with the current value changes.\n   * When a validation error is detected, the `error` parameter contains a non-null value.\n   * This can be used to render an appropriate form error.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TError} error The reason why the current value is not valid.\n   * @param {TValue} value The value associated with the error.\n   */\n  onError: PropTypes.func,\n  /**\n   * Callback fired on month change.\n   * @template TDate\n   * @param {TDate} month The new month.\n   */\n  onMonthChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see `open`).\n   */\n  onOpen: PropTypes.func,\n  /**\n   * Callback fired when the selected sections change.\n   * @param {FieldSelectedSections} newValue The new selected sections.\n   */\n  onSelectedSectionsChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * Callback fired on year change.\n   * @template TDate\n   * @param {TDate} year The new year.\n   */\n  onYearChange: PropTypes.func,\n  /**\n   * Control the popup or dialog open state.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['day', 'month', 'year']),\n  /**\n   * Force rendering in particular orientation.\n   */\n  orientation: PropTypes.oneOf(['landscape', 'portrait']),\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, disable heavy animations.\n   * @default `@media(prefers-reduced-motion: reduce)` || `navigator.userAgent` matches Android <10 or iOS <13\n   */\n  reduceAnimations: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid date-time using the validation props, except callbacks like `shouldDisable<...>`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Component displaying when passed `loading` true.\n   * @returns {React.ReactNode} The node to render when loading.\n   * @default () => <span data-mui-test=\"loading-progress\">...</span>\n   */\n  renderLoading: PropTypes.func,\n  /**\n   * The currently selected sections.\n   * This prop accepts four formats:\n   * 1. If a number is provided, the section at this index will be selected.\n   * 2. If a string of type `FieldSectionType` is provided, the first section with that name will be selected.\n   * 3. If `\"all\"` is provided, all the sections will be selected.\n   * 4. If `null` is provided, no section will be selected.\n   * If not provided, the selected sections will be handled internally.\n   */\n  selectedSections: PropTypes.oneOfType([PropTypes.oneOf(['all', 'day', 'empty', 'hours', 'meridiem', 'minutes', 'month', 'seconds', 'weekDay', 'year']), PropTypes.number]),\n  /**\n   * Disable specific date.\n   *\n   * Warning: This function can be called multiple times (for example when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.\n   *\n   * @template TDate\n   * @param {TDate} day The date to test.\n   * @returns {boolean} If `true` the date will be disabled.\n   */\n  shouldDisableDate: PropTypes.func,\n  /**\n   * Disable specific month.\n   * @template TDate\n   * @param {TDate} month The month to test.\n   * @returns {boolean} If `true`, the month will be disabled.\n   */\n  shouldDisableMonth: PropTypes.func,\n  /**\n   * Disable specific year.\n   * @template TDate\n   * @param {TDate} year The year to test.\n   * @returns {boolean} If `true`, the year will be disabled.\n   */\n  shouldDisableYear: PropTypes.func,\n  /**\n   * If `true`, days outside the current month are rendered:\n   *\n   * - if `fixedWeekNumber` is defined, renders days to have the weeks requested.\n   *\n   * - if `fixedWeekNumber` is not defined, renders day to fill the first and last week of the current month.\n   *\n   * - ignored if `calendars` equals more than `1` on range pickers.\n   * @default false\n   */\n  showDaysOutsideCurrentMonth: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['day', 'month', 'year']),\n  /**\n   * Define custom view renderers for each section.\n   * If `null`, the section will only have field editing.\n   * If `undefined`, internally defined view will be used.\n   */\n  viewRenderers: PropTypes.shape({\n    day: PropTypes.func,\n    month: PropTypes.func,\n    year: PropTypes.func\n  }),\n  /**\n   * Available views.\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'month', 'year']).isRequired),\n  /**\n   * Years are displayed in ascending (chronological) order by default.\n   * If `desc`, years are displayed in descending order.\n   * @default 'asc'\n   */\n  yearsOrder: PropTypes.oneOf(['asc', 'desc']),\n  /**\n   * Years rendered per row.\n   * @default 3\n   */\n  yearsPerRow: PropTypes.oneOf([3, 4])\n};\nexport { MobileDatePicker };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,qBAAqB,MAAM,kCAAkC;AACpE,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,6BAA6B,QAAQ,yBAAyB;AACvE,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,sBAAsB,EAAEC,YAAY,QAAQ,wBAAwB;AAC7E,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,sBAAsB,QAAQ,+BAA+B;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,0BAA0B,QAAQ,4CAA4C;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,aAAaf,KAAK,CAACgB,UAAU,CAAC,SAASD,gBAAgBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC7F,MAAMC,YAAY,GAAGb,sBAAsB,CAAC,CAAC;EAC7C,MAAMc,KAAK,GAAGb,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAMc,gBAAgB,GAAGhB,6BAA6B,CAACY,OAAO,EAAE,qBAAqB,CAAC;EACtF,MAAMK,aAAa,GAAGvB,QAAQ,CAAC;IAC7BwB,GAAG,EAAEX,sBAAsB;IAC3BY,KAAK,EAAEZ,sBAAsB;IAC7Ba,IAAI,EAAEb;EACR,CAAC,EAAES,gBAAgB,CAACC,aAAa,CAAC;;EAElC;EACA,MAAMI,KAAK,GAAG3B,QAAQ,CAAC,CAAC,CAAC,EAAEsB,gBAAgB,EAAE;IAC3CC,aAAa;IACbK,MAAM,EAAEd,iBAAiB,CAACO,KAAK,EAAEC,gBAAgB,EAAE,KAAK,CAAC;IACzDO,KAAK,EAAE7B,QAAQ,CAAC;MACd8B,KAAK,EAAEnB;IACT,CAAC,EAAEW,gBAAgB,CAACO,KAAK,CAAC;IAC1BE,SAAS,EAAE/B,QAAQ,CAAC,CAAC,CAAC,EAAEsB,gBAAgB,CAACS,SAAS,EAAE;MAClDD,KAAK,EAAEE,UAAU,IAAIhC,QAAQ,CAAC,CAAC,CAAC,EAAEG,qBAAqB,CAACmB,gBAAgB,CAACS,SAAS,EAAED,KAAK,EAAEE,UAAU,CAAC,EAAEvB,sBAAsB,CAACa,gBAAgB,CAAC,EAAE;QAChJH;MACF,CAAC,CAAC;MACFc,OAAO,EAAEjC,QAAQ,CAAC;QAChBkC,MAAM,EAAE;MACV,CAAC,EAAEZ,gBAAgB,CAACS,SAAS,EAAEE,OAAO;IACxC,CAAC;EACH,CAAC,CAAC;EACF,MAAM;IACJE;EACF,CAAC,GAAG9B,eAAe,CAAC;IAClBsB,KAAK;IACLS,YAAY,EAAExB,sBAAsB;IACpCyB,SAAS,EAAE,MAAM;IACjBC,qBAAqB,EAAEvB,0BAA0B,CAAC;MAChDM,KAAK;MACLkB,SAAS,EAAE,UAAU;MACrBC,kBAAkB,EAAEpB,YAAY,CAACqB,sBAAsB;MACvDC,gBAAgB,EAAEf,KAAK,CAACgB,UAAU,EAAEF;IACtC,CAAC,CAAC;IACFG,SAAS,EAAElC;EACb,CAAC,CAAC;EACF,OAAOyB,YAAY,CAAC,CAAC;AACvB,CAAC,CAAC;AACFnB,gBAAgB,CAAC6B,SAAS,GAAG;EAC3B;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;EACEC,SAAS,EAAE5C,SAAS,CAAC6C,IAAI;EACzBC,SAAS,EAAE9C,SAAS,CAAC+C,MAAM;EAC3B;AACF;AACA;AACA;EACEC,aAAa,EAAEhD,SAAS,CAAC6C,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;EACEI,kBAAkB,EAAEjD,SAAS,CAACkD,IAAI;EAClC;AACF;AACA;AACA;EACEC,YAAY,EAAEnD,SAAS,CAACoD,MAAM;EAC9B;AACF;AACA;AACA;EACEC,QAAQ,EAAErD,SAAS,CAAC6C,IAAI;EACxB;AACF;AACA;AACA;EACES,aAAa,EAAEtD,SAAS,CAAC6C,IAAI;EAC7B;AACF;AACA;AACA;EACEU,qBAAqB,EAAEvD,SAAS,CAAC6C,IAAI;EACrC;AACF;AACA;AACA;EACEW,iBAAiB,EAAExD,SAAS,CAAC6C,IAAI;EACjC;AACF;AACA;AACA;EACEY,WAAW,EAAEzD,SAAS,CAAC6C,IAAI;EAC3B;AACF;AACA;EACEa,iBAAiB,EAAE1D,SAAS,CAAC6C,IAAI;EACjC;AACF;AACA;EACEc,iCAAiC,EAAE3D,SAAS,CAAC4D,GAAG;EAChD;AACF;AACA;AACA;EACEC,eAAe,EAAE7D,SAAS,CAAC8D,MAAM;EACjC;AACF;AACA;AACA;EACEpC,MAAM,EAAE1B,SAAS,CAAC+C,MAAM;EACxB;AACF;AACA;AACA;AACA;EACEgB,aAAa,EAAE/D,SAAS,CAACgE,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;EACrD;AACF;AACA;EACEC,QAAQ,EAAE/D,OAAO;EACjB;AACF;AACA;EACEgE,KAAK,EAAElE,SAAS,CAACmE,IAAI;EACrB;AACF;AACA;AACA;AACA;EACEC,OAAO,EAAEpE,SAAS,CAAC6C,IAAI;EACvB;AACF;AACA;AACA;EACEJ,UAAU,EAAEzC,SAAS,CAACoD,MAAM;EAC5B;AACF;AACA;AACA;EACEiB,OAAO,EAAErE,SAAS,CAACoD,MAAM;EACzB;AACF;AACA;AACA;EACEkB,OAAO,EAAEtE,SAAS,CAACoD,MAAM;EACzB;AACF;AACA;AACA;EACEmB,YAAY,EAAEvE,SAAS,CAACgE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACrC;AACF;AACA;EACEQ,IAAI,EAAExE,SAAS,CAAC+C,MAAM;EACtB;AACF;AACA;AACA;AACA;AACA;AACA;EACE0B,QAAQ,EAAEzE,SAAS,CAACkD,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACEwB,QAAQ,EAAE1E,SAAS,CAACkD,IAAI;EACxB;AACF;AACA;AACA;EACEyB,OAAO,EAAE3E,SAAS,CAACkD,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE0B,OAAO,EAAE5E,SAAS,CAACkD,IAAI;EACvB;AACF;AACA;AACA;AACA;EACE2B,aAAa,EAAE7E,SAAS,CAACkD,IAAI;EAC7B;AACF;AACA;AACA;EACE4B,MAAM,EAAE9E,SAAS,CAACkD,IAAI;EACtB;AACF;AACA;AACA;EACE6B,wBAAwB,EAAE/E,SAAS,CAACkD,IAAI;EACxC;AACF;AACA;AACA;AACA;EACE8B,YAAY,EAAEhF,SAAS,CAACkD,IAAI;EAC5B;AACF;AACA;AACA;AACA;EACE+B,YAAY,EAAEjF,SAAS,CAACkD,IAAI;EAC5B;AACF;AACA;AACA;EACEgC,IAAI,EAAElF,SAAS,CAAC6C,IAAI;EACpB;AACF;AACA;AACA;AACA;EACEsC,MAAM,EAAEnF,SAAS,CAACgE,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;EACjD;AACF;AACA;EACEoB,WAAW,EAAEpF,SAAS,CAACgE,KAAK,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;EACvDqB,QAAQ,EAAErF,SAAS,CAAC6C,IAAI;EACxB;AACF;AACA;AACA;EACEyC,gBAAgB,EAAEtF,SAAS,CAAC6C,IAAI;EAChC;AACF;AACA;AACA;EACE0C,aAAa,EAAEvF,SAAS,CAACoD,MAAM;EAC/B;AACF;AACA;AACA;AACA;EACEoC,aAAa,EAAExF,SAAS,CAACkD,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEuC,gBAAgB,EAAEzF,SAAS,CAAC0F,SAAS,CAAC,CAAC1F,SAAS,CAACgE,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,EAAEhE,SAAS,CAAC8D,MAAM,CAAC,CAAC;EAC1K;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE6B,iBAAiB,EAAE3F,SAAS,CAACkD,IAAI;EACjC;AACF;AACA;AACA;AACA;AACA;EACE0C,kBAAkB,EAAE5F,SAAS,CAACkD,IAAI;EAClC;AACF;AACA;AACA;AACA;AACA;EACE2C,iBAAiB,EAAE7F,SAAS,CAACkD,IAAI;EACjC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE4C,2BAA2B,EAAE9F,SAAS,CAAC6C,IAAI;EAC3C;AACF;AACA;AACA;EACEhB,SAAS,EAAE7B,SAAS,CAACoD,MAAM;EAC3B;AACF;AACA;AACA;EACEzB,KAAK,EAAE3B,SAAS,CAACoD,MAAM;EACvB;AACF;AACA;EACE2C,EAAE,EAAE/F,SAAS,CAAC0F,SAAS,CAAC,CAAC1F,SAAS,CAACgG,OAAO,CAAChG,SAAS,CAAC0F,SAAS,CAAC,CAAC1F,SAAS,CAACkD,IAAI,EAAElD,SAAS,CAACoD,MAAM,EAAEpD,SAAS,CAAC6C,IAAI,CAAC,CAAC,CAAC,EAAE7C,SAAS,CAACkD,IAAI,EAAElD,SAAS,CAACoD,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;EACE6C,QAAQ,EAAEjG,SAAS,CAAC+C,MAAM;EAC1B;AACF;AACA;AACA;EACEmD,KAAK,EAAElG,SAAS,CAACoD,MAAM;EACvB;AACF;AACA;AACA;AACA;EACE+C,IAAI,EAAEnG,SAAS,CAACgE,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;EAC/C;AACF;AACA;AACA;AACA;EACE3C,aAAa,EAAErB,SAAS,CAACoG,KAAK,CAAC;IAC7B9E,GAAG,EAAEtB,SAAS,CAACkD,IAAI;IACnB3B,KAAK,EAAEvB,SAAS,CAACkD,IAAI;IACrB1B,IAAI,EAAExB,SAAS,CAACkD;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEmD,KAAK,EAAErG,SAAS,CAACgG,OAAO,CAAChG,SAAS,CAACgE,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAACsC,UAAU,CAAC;EAC9E;AACF;AACA;AACA;AACA;EACEC,UAAU,EAAEvG,SAAS,CAACgE,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;EAC5C;AACF;AACA;AACA;EACEwC,WAAW,EAAExG,SAAS,CAACgE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACrC,CAAC;AACD,SAASlD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}