{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { getActiveElement } from \"../../utils/utils.js\";\nimport { getSectionVisibleValue, isAndroid } from \"./useField.utils.js\";\nconst cleanString = dirtyString => dirtyString.replace(/[\\u2066\\u2067\\u2068\\u2069]/g, '');\nexport const addPositionPropertiesToSections = (sections, localizedDigits, isRtl) => {\n  let position = 0;\n  let positionInInput = isRtl ? 1 : 0;\n  const newSections = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const renderedValue = getSectionVisibleValue(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    const sectionStr = `${section.startSeparator}${renderedValue}${section.endSeparator}`;\n    const sectionLength = cleanString(sectionStr).length;\n    const sectionLengthInInput = sectionStr.length;\n\n    // The ...InInput values consider the unicode characters but do include them in their indexes\n    const cleanedValue = cleanString(renderedValue);\n    const startInInput = positionInInput + (cleanedValue === '' ? 0 : renderedValue.indexOf(cleanedValue[0])) + section.startSeparator.length;\n    const endInInput = startInInput + cleanedValue.length;\n    newSections.push(_extends({}, section, {\n      start: position,\n      end: position + sectionLength,\n      startInInput,\n      endInInput\n    }));\n    position += sectionLength;\n    // Move position to the end of string associated to the current section\n    positionInInput += sectionLengthInInput;\n  }\n  return newSections;\n};\nexport const useFieldV6TextField = params => {\n  const isRtl = useRtl();\n  const focusTimeoutRef = React.useRef();\n  const selectionSyncTimeoutRef = React.useRef();\n  const {\n    forwardedProps: {\n      onFocus,\n      onClick,\n      onPaste,\n      onBlur,\n      inputRef: inputRefProp,\n      placeholder: inPlaceholder\n    },\n    internalProps: {\n      readOnly = false,\n      disabled = false\n    },\n    parsedSelectedSections,\n    activeSectionIndex,\n    state,\n    fieldValueManager,\n    valueManager,\n    applyCharacterEditing,\n    resetCharacterQuery,\n    updateSectionValue,\n    updateValueFromValueStr,\n    clearActiveSection,\n    clearValue,\n    setTempAndroidValueStr,\n    setSelectedSections,\n    getSectionsFromValue,\n    areAllSectionsEmpty,\n    localizedDigits\n  } = params;\n  const inputRef = React.useRef(null);\n  const handleRef = useForkRef(inputRefProp, inputRef);\n  const sections = React.useMemo(() => addPositionPropertiesToSections(state.sections, localizedDigits, isRtl), [state.sections, localizedDigits, isRtl]);\n  const interactions = React.useMemo(() => ({\n    syncSelectionToDOM: () => {\n      if (!inputRef.current) {\n        return;\n      }\n      if (parsedSelectedSections == null) {\n        if (inputRef.current.scrollLeft) {\n          // Ensure that input content is not marked as selected.\n          // setting selection range to 0 causes issues in Safari.\n          // https://bugs.webkit.org/show_bug.cgi?id=224425\n          inputRef.current.scrollLeft = 0;\n        }\n        return;\n      }\n\n      // On multi input range pickers we want to update selection range only for the active input\n      // This helps to avoid the focus jumping on Safari https://github.com/mui/mui-x/issues/9003\n      // because WebKit implements the `setSelectionRange` based on the spec: https://bugs.webkit.org/show_bug.cgi?id=224425\n      if (inputRef.current !== getActiveElement(document)) {\n        return;\n      }\n\n      // Fix scroll jumping on iOS browser: https://github.com/mui/mui-x/issues/8321\n      const currentScrollTop = inputRef.current.scrollTop;\n      if (parsedSelectedSections === 'all') {\n        inputRef.current.select();\n      } else {\n        const selectedSection = sections[parsedSelectedSections];\n        const selectionStart = selectedSection.type === 'empty' ? selectedSection.startInInput - selectedSection.startSeparator.length : selectedSection.startInInput;\n        const selectionEnd = selectedSection.type === 'empty' ? selectedSection.endInInput + selectedSection.endSeparator.length : selectedSection.endInInput;\n        if (selectionStart !== inputRef.current.selectionStart || selectionEnd !== inputRef.current.selectionEnd) {\n          if (inputRef.current === getActiveElement(document)) {\n            inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n          }\n        }\n        clearTimeout(selectionSyncTimeoutRef.current);\n        selectionSyncTimeoutRef.current = setTimeout(() => {\n          // handle case when the selection is not updated correctly\n          // could happen on Android\n          if (inputRef.current && inputRef.current === getActiveElement(document) &&\n          // The section might loose all selection, where `selectionStart === selectionEnd`\n          // https://github.com/mui/mui-x/pull/13652\n          inputRef.current.selectionStart === inputRef.current.selectionEnd && (inputRef.current.selectionStart !== selectionStart || inputRef.current.selectionEnd !== selectionEnd)) {\n            interactions.syncSelectionToDOM();\n          }\n        });\n      }\n\n      // Even reading this variable seems to do the trick, but also setting it just to make use of it\n      inputRef.current.scrollTop = currentScrollTop;\n    },\n    getActiveSectionIndexFromDOM: () => {\n      const browserStartIndex = inputRef.current.selectionStart ?? 0;\n      const browserEndIndex = inputRef.current.selectionEnd ?? 0;\n      if (browserStartIndex === 0 && browserEndIndex === 0) {\n        return null;\n      }\n      const nextSectionIndex = browserStartIndex <= sections[0].startInInput ? 1 // Special case if browser index is in invisible characters at the beginning.\n      : sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n      return nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    },\n    focusField: (newSelectedSection = 0) => {\n      inputRef.current?.focus();\n      setSelectedSections(newSelectedSection);\n    },\n    setSelectedSections: newSelectedSections => setSelectedSections(newSelectedSections),\n    isFieldFocused: () => inputRef.current === getActiveElement(document)\n  }), [inputRef, parsedSelectedSections, sections, setSelectedSections]);\n  const syncSelectionFromDOM = () => {\n    const browserStartIndex = inputRef.current.selectionStart ?? 0;\n    let nextSectionIndex;\n    if (browserStartIndex <= sections[0].startInInput) {\n      // Special case if browser index is in invisible characters at the beginning\n      nextSectionIndex = 1;\n    } else if (browserStartIndex >= sections[sections.length - 1].endInInput) {\n      // If the click is after the last character of the input, then we want to select the 1st section.\n      nextSectionIndex = 1;\n    } else {\n      nextSectionIndex = sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n    }\n    const sectionIndex = nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    setSelectedSections(sectionIndex);\n  };\n  const handleInputFocus = useEventCallback((...args) => {\n    onFocus?.(...args);\n    // The ref is guaranteed to be resolved at this point.\n    const input = inputRef.current;\n    clearTimeout(focusTimeoutRef.current);\n    focusTimeoutRef.current = setTimeout(() => {\n      // The ref changed, the component got remounted, the focus event is no longer relevant.\n      if (!input || input !== inputRef.current) {\n        return;\n      }\n      if (activeSectionIndex != null) {\n        return;\n      }\n      if (\n      // avoid selecting all sections when focusing empty field without value\n      input.value.length && Number(input.selectionEnd) - Number(input.selectionStart) === input.value.length) {\n        setSelectedSections('all');\n      } else {\n        syncSelectionFromDOM();\n      }\n    });\n  });\n  const handleInputClick = useEventCallback((event, ...args) => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call of `handleInputClick` is actually intended, or a side effect.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    onClick?.(event, ...args);\n    syncSelectionFromDOM();\n  });\n  const handleInputPaste = useEventCallback(event => {\n    onPaste?.(event);\n\n    // prevent default to avoid the input `onChange` handler being called\n    event.preventDefault();\n    if (readOnly || disabled) {\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    if (typeof parsedSelectedSections === 'number') {\n      const activeSection = state.sections[parsedSelectedSections];\n      const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n      const digitsOnly = /^[0-9]+$/.test(pastedValue);\n      const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n      const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n      if (isValidPastedValue) {\n        resetCharacterQuery();\n        updateSectionValue({\n          activeSection,\n          newSectionValue: pastedValue,\n          shouldGoToNextSection: true\n        });\n        return;\n      }\n      if (lettersOnly || digitsOnly) {\n        // The pasted value corresponds to a single section, but not the expected type,\n        // skip the modification\n        return;\n      }\n    }\n    resetCharacterQuery();\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleContainerBlur = useEventCallback((...args) => {\n    onBlur?.(...args);\n    setSelectedSections(null);\n  });\n  const handleInputChange = useEventCallback(event => {\n    if (readOnly) {\n      return;\n    }\n    const targetValue = event.target.value;\n    if (targetValue === '') {\n      resetCharacterQuery();\n      clearValue();\n      return;\n    }\n    const eventData = event.nativeEvent.data;\n    // Calling `.fill(04/11/2022)` in playwright will trigger a change event with the requested content to insert in `event.nativeEvent.data`\n    // usual changes have only the currently typed character in the `event.nativeEvent.data`\n    const shouldUseEventData = eventData && eventData.length > 1;\n    const valueStr = shouldUseEventData ? eventData : targetValue;\n    const cleanValueStr = cleanString(valueStr);\n\n    // If no section is selected or eventData should be used, we just try to parse the new value\n    // This line is mostly triggered by imperative code / application tests.\n    if (activeSectionIndex == null || shouldUseEventData) {\n      updateValueFromValueStr(shouldUseEventData ? eventData : cleanValueStr);\n      return;\n    }\n    let keyPressed;\n    if (parsedSelectedSections === 'all' && cleanValueStr.length === 1) {\n      keyPressed = cleanValueStr;\n    } else {\n      const prevValueStr = cleanString(fieldValueManager.getV6InputValueFromSections(sections, localizedDigits, isRtl));\n      let startOfDiffIndex = -1;\n      let endOfDiffIndex = -1;\n      for (let i = 0; i < prevValueStr.length; i += 1) {\n        if (startOfDiffIndex === -1 && prevValueStr[i] !== cleanValueStr[i]) {\n          startOfDiffIndex = i;\n        }\n        if (endOfDiffIndex === -1 && prevValueStr[prevValueStr.length - i - 1] !== cleanValueStr[cleanValueStr.length - i - 1]) {\n          endOfDiffIndex = i;\n        }\n      }\n      const activeSection = sections[activeSectionIndex];\n      const hasDiffOutsideOfActiveSection = startOfDiffIndex < activeSection.start || prevValueStr.length - endOfDiffIndex - 1 > activeSection.end;\n      if (hasDiffOutsideOfActiveSection) {\n        // TODO: Support if the new date is valid\n        return;\n      }\n\n      // The active section being selected, the browser has replaced its value with the key pressed by the user.\n      const activeSectionEndRelativeToNewValue = cleanValueStr.length - prevValueStr.length + activeSection.end - cleanString(activeSection.endSeparator || '').length;\n      keyPressed = cleanValueStr.slice(activeSection.start + cleanString(activeSection.startSeparator || '').length, activeSectionEndRelativeToNewValue);\n    }\n    if (keyPressed.length === 0) {\n      if (isAndroid()) {\n        setTempAndroidValueStr(valueStr);\n      }\n      resetCharacterQuery();\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex: activeSectionIndex\n    });\n  });\n  const placeholder = React.useMemo(() => {\n    if (inPlaceholder !== undefined) {\n      return inPlaceholder;\n    }\n    return fieldValueManager.getV6InputValueFromSections(getSectionsFromValue(valueManager.emptyValue), localizedDigits, isRtl);\n  }, [inPlaceholder, fieldValueManager, getSectionsFromValue, valueManager.emptyValue, localizedDigits, isRtl]);\n  const valueStr = React.useMemo(() => state.tempValueStrAndroid ?? fieldValueManager.getV6InputValueFromSections(state.sections, localizedDigits, isRtl), [state.sections, fieldValueManager, state.tempValueStrAndroid, localizedDigits, isRtl]);\n  React.useEffect(() => {\n    // Select all the sections when focused on mount (`autoFocus = true` on the input)\n    if (inputRef.current && inputRef.current === getActiveElement(document)) {\n      setSelectedSections('all');\n    }\n    return () => {\n      clearTimeout(focusTimeoutRef.current);\n      clearTimeout(selectionSyncTimeoutRef.current);\n    };\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const inputMode = React.useMemo(() => {\n    if (activeSectionIndex == null) {\n      return 'text';\n    }\n    if (state.sections[activeSectionIndex].contentType === 'letter') {\n      return 'text';\n    }\n    return 'numeric';\n  }, [activeSectionIndex, state.sections]);\n  const inputHasFocus = inputRef.current && inputRef.current === getActiveElement(document);\n  const shouldShowPlaceholder = !inputHasFocus && areAllSectionsEmpty;\n  return {\n    interactions,\n    returnedValue: {\n      // Forwarded\n      readOnly,\n      onBlur: handleContainerBlur,\n      onClick: handleInputClick,\n      onFocus: handleInputFocus,\n      onPaste: handleInputPaste,\n      inputRef: handleRef,\n      // Additional\n      enableAccessibleFieldDOMStructure: false,\n      placeholder,\n      inputMode,\n      autoComplete: 'off',\n      value: shouldShowPlaceholder ? '' : valueStr,\n      onChange: handleInputChange\n    }\n  };\n};", "map": {"version": 3, "names": ["_extends", "React", "useRtl", "useEventCallback", "useForkRef", "getActiveElement", "getSectionVisibleValue", "isAndroid", "cleanString", "dirtyString", "replace", "addPositionPropertiesToSections", "sections", "localizedDigits", "isRtl", "position", "positionInInput", "newSections", "i", "length", "section", "renderedValue", "sectionStr", "startSeparator", "endSeparator", "sectionLength", "sectionLengthInInput", "cleanedValue", "startInInput", "indexOf", "endInInput", "push", "start", "end", "useFieldV6TextField", "params", "focusTimeoutRef", "useRef", "selectionSyncTimeoutRef", "forwardedProps", "onFocus", "onClick", "onPaste", "onBlur", "inputRef", "inputRefProp", "placeholder", "inPlaceholder", "internalProps", "readOnly", "disabled", "parsedSelectedSections", "activeSectionIndex", "state", "field<PERSON><PERSON>ueManager", "valueManager", "applyCharacterEditing", "resetCharacterQuery", "updateSectionValue", "updateValueFromValueStr", "clearActiveSection", "clearValue", "setTempAndroidValueStr", "setSelectedSections", "getSectionsFromValue", "areAllSectionsEmpty", "handleRef", "useMemo", "interactions", "syncSelectionToDOM", "current", "scrollLeft", "document", "currentScrollTop", "scrollTop", "select", "selectedSection", "selectionStart", "type", "selectionEnd", "setSelectionRange", "clearTimeout", "setTimeout", "getActiveSectionIndexFromDOM", "browserStartIndex", "browserEndIndex", "nextSectionIndex", "findIndex", "focusField", "newSelectedSection", "focus", "newSelectedSections", "isFieldFocused", "syncSelectionFromDOM", "sectionIndex", "handleInputFocus", "args", "input", "value", "Number", "handleInputClick", "event", "isDefaultPrevented", "handleInputPaste", "preventDefault", "pastedValue", "clipboardData", "getData", "activeSection", "lettersOnly", "test", "digitsOnly", "digitsAndLetterOnly", "isValidPastedValue", "contentType", "newSectionValue", "shouldGoToNextSection", "handleContainerBlur", "handleInputChange", "targetValue", "target", "eventData", "nativeEvent", "data", "shouldUseEventData", "valueStr", "cleanValueStr", "keyPressed", "prevValueStr", "getV6InputValueFromSections", "startOfDiffIndex", "endOfDiffIndex", "hasDiffOutsideOfActiveSection", "activeSectionEndRelativeToNewValue", "slice", "undefined", "emptyValue", "tempValueStrAndroid", "useEffect", "inputMode", "inputHasFocus", "shouldShowPlaceholder", "returnedValue", "enableAccessibleFieldDOMStructure", "autoComplete", "onChange"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldV6TextField.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { getActiveElement } from \"../../utils/utils.js\";\nimport { getSectionVisibleValue, isAndroid } from \"./useField.utils.js\";\nconst cleanString = dirtyString => dirtyString.replace(/[\\u2066\\u2067\\u2068\\u2069]/g, '');\nexport const addPositionPropertiesToSections = (sections, localizedDigits, isRtl) => {\n  let position = 0;\n  let positionInInput = isRtl ? 1 : 0;\n  const newSections = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const renderedValue = getSectionVisibleValue(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    const sectionStr = `${section.startSeparator}${renderedValue}${section.endSeparator}`;\n    const sectionLength = cleanString(sectionStr).length;\n    const sectionLengthInInput = sectionStr.length;\n\n    // The ...InInput values consider the unicode characters but do include them in their indexes\n    const cleanedValue = cleanString(renderedValue);\n    const startInInput = positionInInput + (cleanedValue === '' ? 0 : renderedValue.indexOf(cleanedValue[0])) + section.startSeparator.length;\n    const endInInput = startInInput + cleanedValue.length;\n    newSections.push(_extends({}, section, {\n      start: position,\n      end: position + sectionLength,\n      startInInput,\n      endInInput\n    }));\n    position += sectionLength;\n    // Move position to the end of string associated to the current section\n    positionInInput += sectionLengthInInput;\n  }\n  return newSections;\n};\nexport const useFieldV6TextField = params => {\n  const isRtl = useRtl();\n  const focusTimeoutRef = React.useRef();\n  const selectionSyncTimeoutRef = React.useRef();\n  const {\n    forwardedProps: {\n      onFocus,\n      onClick,\n      onPaste,\n      onBlur,\n      inputRef: inputRefProp,\n      placeholder: inPlaceholder\n    },\n    internalProps: {\n      readOnly = false,\n      disabled = false\n    },\n    parsedSelectedSections,\n    activeSectionIndex,\n    state,\n    fieldValueManager,\n    valueManager,\n    applyCharacterEditing,\n    resetCharacterQuery,\n    updateSectionValue,\n    updateValueFromValueStr,\n    clearActiveSection,\n    clearValue,\n    setTempAndroidValueStr,\n    setSelectedSections,\n    getSectionsFromValue,\n    areAllSectionsEmpty,\n    localizedDigits\n  } = params;\n  const inputRef = React.useRef(null);\n  const handleRef = useForkRef(inputRefProp, inputRef);\n  const sections = React.useMemo(() => addPositionPropertiesToSections(state.sections, localizedDigits, isRtl), [state.sections, localizedDigits, isRtl]);\n  const interactions = React.useMemo(() => ({\n    syncSelectionToDOM: () => {\n      if (!inputRef.current) {\n        return;\n      }\n      if (parsedSelectedSections == null) {\n        if (inputRef.current.scrollLeft) {\n          // Ensure that input content is not marked as selected.\n          // setting selection range to 0 causes issues in Safari.\n          // https://bugs.webkit.org/show_bug.cgi?id=224425\n          inputRef.current.scrollLeft = 0;\n        }\n        return;\n      }\n\n      // On multi input range pickers we want to update selection range only for the active input\n      // This helps to avoid the focus jumping on Safari https://github.com/mui/mui-x/issues/9003\n      // because WebKit implements the `setSelectionRange` based on the spec: https://bugs.webkit.org/show_bug.cgi?id=224425\n      if (inputRef.current !== getActiveElement(document)) {\n        return;\n      }\n\n      // Fix scroll jumping on iOS browser: https://github.com/mui/mui-x/issues/8321\n      const currentScrollTop = inputRef.current.scrollTop;\n      if (parsedSelectedSections === 'all') {\n        inputRef.current.select();\n      } else {\n        const selectedSection = sections[parsedSelectedSections];\n        const selectionStart = selectedSection.type === 'empty' ? selectedSection.startInInput - selectedSection.startSeparator.length : selectedSection.startInInput;\n        const selectionEnd = selectedSection.type === 'empty' ? selectedSection.endInInput + selectedSection.endSeparator.length : selectedSection.endInInput;\n        if (selectionStart !== inputRef.current.selectionStart || selectionEnd !== inputRef.current.selectionEnd) {\n          if (inputRef.current === getActiveElement(document)) {\n            inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n          }\n        }\n        clearTimeout(selectionSyncTimeoutRef.current);\n        selectionSyncTimeoutRef.current = setTimeout(() => {\n          // handle case when the selection is not updated correctly\n          // could happen on Android\n          if (inputRef.current && inputRef.current === getActiveElement(document) &&\n          // The section might loose all selection, where `selectionStart === selectionEnd`\n          // https://github.com/mui/mui-x/pull/13652\n          inputRef.current.selectionStart === inputRef.current.selectionEnd && (inputRef.current.selectionStart !== selectionStart || inputRef.current.selectionEnd !== selectionEnd)) {\n            interactions.syncSelectionToDOM();\n          }\n        });\n      }\n\n      // Even reading this variable seems to do the trick, but also setting it just to make use of it\n      inputRef.current.scrollTop = currentScrollTop;\n    },\n    getActiveSectionIndexFromDOM: () => {\n      const browserStartIndex = inputRef.current.selectionStart ?? 0;\n      const browserEndIndex = inputRef.current.selectionEnd ?? 0;\n      if (browserStartIndex === 0 && browserEndIndex === 0) {\n        return null;\n      }\n      const nextSectionIndex = browserStartIndex <= sections[0].startInInput ? 1 // Special case if browser index is in invisible characters at the beginning.\n      : sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n      return nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    },\n    focusField: (newSelectedSection = 0) => {\n      inputRef.current?.focus();\n      setSelectedSections(newSelectedSection);\n    },\n    setSelectedSections: newSelectedSections => setSelectedSections(newSelectedSections),\n    isFieldFocused: () => inputRef.current === getActiveElement(document)\n  }), [inputRef, parsedSelectedSections, sections, setSelectedSections]);\n  const syncSelectionFromDOM = () => {\n    const browserStartIndex = inputRef.current.selectionStart ?? 0;\n    let nextSectionIndex;\n    if (browserStartIndex <= sections[0].startInInput) {\n      // Special case if browser index is in invisible characters at the beginning\n      nextSectionIndex = 1;\n    } else if (browserStartIndex >= sections[sections.length - 1].endInInput) {\n      // If the click is after the last character of the input, then we want to select the 1st section.\n      nextSectionIndex = 1;\n    } else {\n      nextSectionIndex = sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n    }\n    const sectionIndex = nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    setSelectedSections(sectionIndex);\n  };\n  const handleInputFocus = useEventCallback((...args) => {\n    onFocus?.(...args);\n    // The ref is guaranteed to be resolved at this point.\n    const input = inputRef.current;\n    clearTimeout(focusTimeoutRef.current);\n    focusTimeoutRef.current = setTimeout(() => {\n      // The ref changed, the component got remounted, the focus event is no longer relevant.\n      if (!input || input !== inputRef.current) {\n        return;\n      }\n      if (activeSectionIndex != null) {\n        return;\n      }\n      if (\n      // avoid selecting all sections when focusing empty field without value\n      input.value.length && Number(input.selectionEnd) - Number(input.selectionStart) === input.value.length) {\n        setSelectedSections('all');\n      } else {\n        syncSelectionFromDOM();\n      }\n    });\n  });\n  const handleInputClick = useEventCallback((event, ...args) => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call of `handleInputClick` is actually intended, or a side effect.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    onClick?.(event, ...args);\n    syncSelectionFromDOM();\n  });\n  const handleInputPaste = useEventCallback(event => {\n    onPaste?.(event);\n\n    // prevent default to avoid the input `onChange` handler being called\n    event.preventDefault();\n    if (readOnly || disabled) {\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    if (typeof parsedSelectedSections === 'number') {\n      const activeSection = state.sections[parsedSelectedSections];\n      const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n      const digitsOnly = /^[0-9]+$/.test(pastedValue);\n      const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n      const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n      if (isValidPastedValue) {\n        resetCharacterQuery();\n        updateSectionValue({\n          activeSection,\n          newSectionValue: pastedValue,\n          shouldGoToNextSection: true\n        });\n        return;\n      }\n      if (lettersOnly || digitsOnly) {\n        // The pasted value corresponds to a single section, but not the expected type,\n        // skip the modification\n        return;\n      }\n    }\n    resetCharacterQuery();\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleContainerBlur = useEventCallback((...args) => {\n    onBlur?.(...args);\n    setSelectedSections(null);\n  });\n  const handleInputChange = useEventCallback(event => {\n    if (readOnly) {\n      return;\n    }\n    const targetValue = event.target.value;\n    if (targetValue === '') {\n      resetCharacterQuery();\n      clearValue();\n      return;\n    }\n    const eventData = event.nativeEvent.data;\n    // Calling `.fill(04/11/2022)` in playwright will trigger a change event with the requested content to insert in `event.nativeEvent.data`\n    // usual changes have only the currently typed character in the `event.nativeEvent.data`\n    const shouldUseEventData = eventData && eventData.length > 1;\n    const valueStr = shouldUseEventData ? eventData : targetValue;\n    const cleanValueStr = cleanString(valueStr);\n\n    // If no section is selected or eventData should be used, we just try to parse the new value\n    // This line is mostly triggered by imperative code / application tests.\n    if (activeSectionIndex == null || shouldUseEventData) {\n      updateValueFromValueStr(shouldUseEventData ? eventData : cleanValueStr);\n      return;\n    }\n    let keyPressed;\n    if (parsedSelectedSections === 'all' && cleanValueStr.length === 1) {\n      keyPressed = cleanValueStr;\n    } else {\n      const prevValueStr = cleanString(fieldValueManager.getV6InputValueFromSections(sections, localizedDigits, isRtl));\n      let startOfDiffIndex = -1;\n      let endOfDiffIndex = -1;\n      for (let i = 0; i < prevValueStr.length; i += 1) {\n        if (startOfDiffIndex === -1 && prevValueStr[i] !== cleanValueStr[i]) {\n          startOfDiffIndex = i;\n        }\n        if (endOfDiffIndex === -1 && prevValueStr[prevValueStr.length - i - 1] !== cleanValueStr[cleanValueStr.length - i - 1]) {\n          endOfDiffIndex = i;\n        }\n      }\n      const activeSection = sections[activeSectionIndex];\n      const hasDiffOutsideOfActiveSection = startOfDiffIndex < activeSection.start || prevValueStr.length - endOfDiffIndex - 1 > activeSection.end;\n      if (hasDiffOutsideOfActiveSection) {\n        // TODO: Support if the new date is valid\n        return;\n      }\n\n      // The active section being selected, the browser has replaced its value with the key pressed by the user.\n      const activeSectionEndRelativeToNewValue = cleanValueStr.length - prevValueStr.length + activeSection.end - cleanString(activeSection.endSeparator || '').length;\n      keyPressed = cleanValueStr.slice(activeSection.start + cleanString(activeSection.startSeparator || '').length, activeSectionEndRelativeToNewValue);\n    }\n    if (keyPressed.length === 0) {\n      if (isAndroid()) {\n        setTempAndroidValueStr(valueStr);\n      }\n      resetCharacterQuery();\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex: activeSectionIndex\n    });\n  });\n  const placeholder = React.useMemo(() => {\n    if (inPlaceholder !== undefined) {\n      return inPlaceholder;\n    }\n    return fieldValueManager.getV6InputValueFromSections(getSectionsFromValue(valueManager.emptyValue), localizedDigits, isRtl);\n  }, [inPlaceholder, fieldValueManager, getSectionsFromValue, valueManager.emptyValue, localizedDigits, isRtl]);\n  const valueStr = React.useMemo(() => state.tempValueStrAndroid ?? fieldValueManager.getV6InputValueFromSections(state.sections, localizedDigits, isRtl), [state.sections, fieldValueManager, state.tempValueStrAndroid, localizedDigits, isRtl]);\n  React.useEffect(() => {\n    // Select all the sections when focused on mount (`autoFocus = true` on the input)\n    if (inputRef.current && inputRef.current === getActiveElement(document)) {\n      setSelectedSections('all');\n    }\n    return () => {\n      clearTimeout(focusTimeoutRef.current);\n      clearTimeout(selectionSyncTimeoutRef.current);\n    };\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const inputMode = React.useMemo(() => {\n    if (activeSectionIndex == null) {\n      return 'text';\n    }\n    if (state.sections[activeSectionIndex].contentType === 'letter') {\n      return 'text';\n    }\n    return 'numeric';\n  }, [activeSectionIndex, state.sections]);\n  const inputHasFocus = inputRef.current && inputRef.current === getActiveElement(document);\n  const shouldShowPlaceholder = !inputHasFocus && areAllSectionsEmpty;\n  return {\n    interactions,\n    returnedValue: {\n      // Forwarded\n      readOnly,\n      onBlur: handleContainerBlur,\n      onClick: handleInputClick,\n      onFocus: handleInputFocus,\n      onPaste: handleInputPaste,\n      inputRef: handleRef,\n      // Additional\n      enableAccessibleFieldDOMStructure: false,\n      placeholder,\n      inputMode,\n      autoComplete: 'off',\n      value: shouldShowPlaceholder ? '' : valueStr,\n      onChange: handleInputChange\n    }\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,sBAAsB,EAAEC,SAAS,QAAQ,qBAAqB;AACvE,MAAMC,WAAW,GAAGC,WAAW,IAAIA,WAAW,CAACC,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC;AACzF,OAAO,MAAMC,+BAA+B,GAAGA,CAACC,QAAQ,EAAEC,eAAe,EAAEC,KAAK,KAAK;EACnF,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,eAAe,GAAGF,KAAK,GAAG,CAAC,GAAG,CAAC;EACnC,MAAMG,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,QAAQ,CAACO,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC3C,MAAME,OAAO,GAAGR,QAAQ,CAACM,CAAC,CAAC;IAC3B,MAAMG,aAAa,GAAGf,sBAAsB,CAACc,OAAO,EAAEN,KAAK,GAAG,WAAW,GAAG,WAAW,EAAED,eAAe,CAAC;IACzG,MAAMS,UAAU,GAAG,GAAGF,OAAO,CAACG,cAAc,GAAGF,aAAa,GAAGD,OAAO,CAACI,YAAY,EAAE;IACrF,MAAMC,aAAa,GAAGjB,WAAW,CAACc,UAAU,CAAC,CAACH,MAAM;IACpD,MAAMO,oBAAoB,GAAGJ,UAAU,CAACH,MAAM;;IAE9C;IACA,MAAMQ,YAAY,GAAGnB,WAAW,CAACa,aAAa,CAAC;IAC/C,MAAMO,YAAY,GAAGZ,eAAe,IAAIW,YAAY,KAAK,EAAE,GAAG,CAAC,GAAGN,aAAa,CAACQ,OAAO,CAACF,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGP,OAAO,CAACG,cAAc,CAACJ,MAAM;IACzI,MAAMW,UAAU,GAAGF,YAAY,GAAGD,YAAY,CAACR,MAAM;IACrDF,WAAW,CAACc,IAAI,CAAC/B,QAAQ,CAAC,CAAC,CAAC,EAAEoB,OAAO,EAAE;MACrCY,KAAK,EAAEjB,QAAQ;MACfkB,GAAG,EAAElB,QAAQ,GAAGU,aAAa;MAC7BG,YAAY;MACZE;IACF,CAAC,CAAC,CAAC;IACHf,QAAQ,IAAIU,aAAa;IACzB;IACAT,eAAe,IAAIU,oBAAoB;EACzC;EACA,OAAOT,WAAW;AACpB,CAAC;AACD,OAAO,MAAMiB,mBAAmB,GAAGC,MAAM,IAAI;EAC3C,MAAMrB,KAAK,GAAGZ,MAAM,CAAC,CAAC;EACtB,MAAMkC,eAAe,GAAGnC,KAAK,CAACoC,MAAM,CAAC,CAAC;EACtC,MAAMC,uBAAuB,GAAGrC,KAAK,CAACoC,MAAM,CAAC,CAAC;EAC9C,MAAM;IACJE,cAAc,EAAE;MACdC,OAAO;MACPC,OAAO;MACPC,OAAO;MACPC,MAAM;MACNC,QAAQ,EAAEC,YAAY;MACtBC,WAAW,EAAEC;IACf,CAAC;IACDC,aAAa,EAAE;MACbC,QAAQ,GAAG,KAAK;MAChBC,QAAQ,GAAG;IACb,CAAC;IACDC,sBAAsB;IACtBC,kBAAkB;IAClBC,KAAK;IACLC,iBAAiB;IACjBC,YAAY;IACZC,qBAAqB;IACrBC,mBAAmB;IACnBC,kBAAkB;IAClBC,uBAAuB;IACvBC,kBAAkB;IAClBC,UAAU;IACVC,sBAAsB;IACtBC,mBAAmB;IACnBC,oBAAoB;IACpBC,mBAAmB;IACnBpD;EACF,CAAC,GAAGsB,MAAM;EACV,MAAMS,QAAQ,GAAG3C,KAAK,CAACoC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM6B,SAAS,GAAG9D,UAAU,CAACyC,YAAY,EAAED,QAAQ,CAAC;EACpD,MAAMhC,QAAQ,GAAGX,KAAK,CAACkE,OAAO,CAAC,MAAMxD,+BAA+B,CAAC0C,KAAK,CAACzC,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,EAAE,CAACuC,KAAK,CAACzC,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,CAAC;EACvJ,MAAMsD,YAAY,GAAGnE,KAAK,CAACkE,OAAO,CAAC,OAAO;IACxCE,kBAAkB,EAAEA,CAAA,KAAM;MACxB,IAAI,CAACzB,QAAQ,CAAC0B,OAAO,EAAE;QACrB;MACF;MACA,IAAInB,sBAAsB,IAAI,IAAI,EAAE;QAClC,IAAIP,QAAQ,CAAC0B,OAAO,CAACC,UAAU,EAAE;UAC/B;UACA;UACA;UACA3B,QAAQ,CAAC0B,OAAO,CAACC,UAAU,GAAG,CAAC;QACjC;QACA;MACF;;MAEA;MACA;MACA;MACA,IAAI3B,QAAQ,CAAC0B,OAAO,KAAKjE,gBAAgB,CAACmE,QAAQ,CAAC,EAAE;QACnD;MACF;;MAEA;MACA,MAAMC,gBAAgB,GAAG7B,QAAQ,CAAC0B,OAAO,CAACI,SAAS;MACnD,IAAIvB,sBAAsB,KAAK,KAAK,EAAE;QACpCP,QAAQ,CAAC0B,OAAO,CAACK,MAAM,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,MAAMC,eAAe,GAAGhE,QAAQ,CAACuC,sBAAsB,CAAC;QACxD,MAAM0B,cAAc,GAAGD,eAAe,CAACE,IAAI,KAAK,OAAO,GAAGF,eAAe,CAAChD,YAAY,GAAGgD,eAAe,CAACrD,cAAc,CAACJ,MAAM,GAAGyD,eAAe,CAAChD,YAAY;QAC7J,MAAMmD,YAAY,GAAGH,eAAe,CAACE,IAAI,KAAK,OAAO,GAAGF,eAAe,CAAC9C,UAAU,GAAG8C,eAAe,CAACpD,YAAY,CAACL,MAAM,GAAGyD,eAAe,CAAC9C,UAAU;QACrJ,IAAI+C,cAAc,KAAKjC,QAAQ,CAAC0B,OAAO,CAACO,cAAc,IAAIE,YAAY,KAAKnC,QAAQ,CAAC0B,OAAO,CAACS,YAAY,EAAE;UACxG,IAAInC,QAAQ,CAAC0B,OAAO,KAAKjE,gBAAgB,CAACmE,QAAQ,CAAC,EAAE;YACnD5B,QAAQ,CAAC0B,OAAO,CAACU,iBAAiB,CAACH,cAAc,EAAEE,YAAY,CAAC;UAClE;QACF;QACAE,YAAY,CAAC3C,uBAAuB,CAACgC,OAAO,CAAC;QAC7ChC,uBAAuB,CAACgC,OAAO,GAAGY,UAAU,CAAC,MAAM;UACjD;UACA;UACA,IAAItC,QAAQ,CAAC0B,OAAO,IAAI1B,QAAQ,CAAC0B,OAAO,KAAKjE,gBAAgB,CAACmE,QAAQ,CAAC;UACvE;UACA;UACA5B,QAAQ,CAAC0B,OAAO,CAACO,cAAc,KAAKjC,QAAQ,CAAC0B,OAAO,CAACS,YAAY,KAAKnC,QAAQ,CAAC0B,OAAO,CAACO,cAAc,KAAKA,cAAc,IAAIjC,QAAQ,CAAC0B,OAAO,CAACS,YAAY,KAAKA,YAAY,CAAC,EAAE;YAC3KX,YAAY,CAACC,kBAAkB,CAAC,CAAC;UACnC;QACF,CAAC,CAAC;MACJ;;MAEA;MACAzB,QAAQ,CAAC0B,OAAO,CAACI,SAAS,GAAGD,gBAAgB;IAC/C,CAAC;IACDU,4BAA4B,EAAEA,CAAA,KAAM;MAClC,MAAMC,iBAAiB,GAAGxC,QAAQ,CAAC0B,OAAO,CAACO,cAAc,IAAI,CAAC;MAC9D,MAAMQ,eAAe,GAAGzC,QAAQ,CAAC0B,OAAO,CAACS,YAAY,IAAI,CAAC;MAC1D,IAAIK,iBAAiB,KAAK,CAAC,IAAIC,eAAe,KAAK,CAAC,EAAE;QACpD,OAAO,IAAI;MACb;MACA,MAAMC,gBAAgB,GAAGF,iBAAiB,IAAIxE,QAAQ,CAAC,CAAC,CAAC,CAACgB,YAAY,GAAG,CAAC,CAAC;MAAA,EACzEhB,QAAQ,CAAC2E,SAAS,CAACnE,OAAO,IAAIA,OAAO,CAACQ,YAAY,GAAGR,OAAO,CAACG,cAAc,CAACJ,MAAM,GAAGiE,iBAAiB,CAAC;MACzG,OAAOE,gBAAgB,KAAK,CAAC,CAAC,GAAG1E,QAAQ,CAACO,MAAM,GAAG,CAAC,GAAGmE,gBAAgB,GAAG,CAAC;IAC7E,CAAC;IACDE,UAAU,EAAEA,CAACC,kBAAkB,GAAG,CAAC,KAAK;MACtC7C,QAAQ,CAAC0B,OAAO,EAAEoB,KAAK,CAAC,CAAC;MACzB3B,mBAAmB,CAAC0B,kBAAkB,CAAC;IACzC,CAAC;IACD1B,mBAAmB,EAAE4B,mBAAmB,IAAI5B,mBAAmB,CAAC4B,mBAAmB,CAAC;IACpFC,cAAc,EAAEA,CAAA,KAAMhD,QAAQ,CAAC0B,OAAO,KAAKjE,gBAAgB,CAACmE,QAAQ;EACtE,CAAC,CAAC,EAAE,CAAC5B,QAAQ,EAAEO,sBAAsB,EAAEvC,QAAQ,EAAEmD,mBAAmB,CAAC,CAAC;EACtE,MAAM8B,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMT,iBAAiB,GAAGxC,QAAQ,CAAC0B,OAAO,CAACO,cAAc,IAAI,CAAC;IAC9D,IAAIS,gBAAgB;IACpB,IAAIF,iBAAiB,IAAIxE,QAAQ,CAAC,CAAC,CAAC,CAACgB,YAAY,EAAE;MACjD;MACA0D,gBAAgB,GAAG,CAAC;IACtB,CAAC,MAAM,IAAIF,iBAAiB,IAAIxE,QAAQ,CAACA,QAAQ,CAACO,MAAM,GAAG,CAAC,CAAC,CAACW,UAAU,EAAE;MACxE;MACAwD,gBAAgB,GAAG,CAAC;IACtB,CAAC,MAAM;MACLA,gBAAgB,GAAG1E,QAAQ,CAAC2E,SAAS,CAACnE,OAAO,IAAIA,OAAO,CAACQ,YAAY,GAAGR,OAAO,CAACG,cAAc,CAACJ,MAAM,GAAGiE,iBAAiB,CAAC;IAC5H;IACA,MAAMU,YAAY,GAAGR,gBAAgB,KAAK,CAAC,CAAC,GAAG1E,QAAQ,CAACO,MAAM,GAAG,CAAC,GAAGmE,gBAAgB,GAAG,CAAC;IACzFvB,mBAAmB,CAAC+B,YAAY,CAAC;EACnC,CAAC;EACD,MAAMC,gBAAgB,GAAG5F,gBAAgB,CAAC,CAAC,GAAG6F,IAAI,KAAK;IACrDxD,OAAO,GAAG,GAAGwD,IAAI,CAAC;IAClB;IACA,MAAMC,KAAK,GAAGrD,QAAQ,CAAC0B,OAAO;IAC9BW,YAAY,CAAC7C,eAAe,CAACkC,OAAO,CAAC;IACrClC,eAAe,CAACkC,OAAO,GAAGY,UAAU,CAAC,MAAM;MACzC;MACA,IAAI,CAACe,KAAK,IAAIA,KAAK,KAAKrD,QAAQ,CAAC0B,OAAO,EAAE;QACxC;MACF;MACA,IAAIlB,kBAAkB,IAAI,IAAI,EAAE;QAC9B;MACF;MACA;MACA;MACA6C,KAAK,CAACC,KAAK,CAAC/E,MAAM,IAAIgF,MAAM,CAACF,KAAK,CAAClB,YAAY,CAAC,GAAGoB,MAAM,CAACF,KAAK,CAACpB,cAAc,CAAC,KAAKoB,KAAK,CAACC,KAAK,CAAC/E,MAAM,EAAE;QACtG4C,mBAAmB,CAAC,KAAK,CAAC;MAC5B,CAAC,MAAM;QACL8B,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMO,gBAAgB,GAAGjG,gBAAgB,CAAC,CAACkG,KAAK,EAAE,GAAGL,IAAI,KAAK;IAC5D;IACA;IACA,IAAIK,KAAK,CAACC,kBAAkB,CAAC,CAAC,EAAE;MAC9B;IACF;IACA7D,OAAO,GAAG4D,KAAK,EAAE,GAAGL,IAAI,CAAC;IACzBH,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,MAAMU,gBAAgB,GAAGpG,gBAAgB,CAACkG,KAAK,IAAI;IACjD3D,OAAO,GAAG2D,KAAK,CAAC;;IAEhB;IACAA,KAAK,CAACG,cAAc,CAAC,CAAC;IACtB,IAAIvD,QAAQ,IAAIC,QAAQ,EAAE;MACxB;IACF;IACA,MAAMuD,WAAW,GAAGJ,KAAK,CAACK,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACvD,IAAI,OAAOxD,sBAAsB,KAAK,QAAQ,EAAE;MAC9C,MAAMyD,aAAa,GAAGvD,KAAK,CAACzC,QAAQ,CAACuC,sBAAsB,CAAC;MAC5D,MAAM0D,WAAW,GAAG,aAAa,CAACC,IAAI,CAACL,WAAW,CAAC;MACnD,MAAMM,UAAU,GAAG,UAAU,CAACD,IAAI,CAACL,WAAW,CAAC;MAC/C,MAAMO,mBAAmB,GAAG,wCAAwC,CAACF,IAAI,CAACL,WAAW,CAAC;MACtF,MAAMQ,kBAAkB,GAAGL,aAAa,CAACM,WAAW,KAAK,QAAQ,IAAIL,WAAW,IAAID,aAAa,CAACM,WAAW,KAAK,OAAO,IAAIH,UAAU,IAAIH,aAAa,CAACM,WAAW,KAAK,mBAAmB,IAAIF,mBAAmB;MACnN,IAAIC,kBAAkB,EAAE;QACtBxD,mBAAmB,CAAC,CAAC;QACrBC,kBAAkB,CAAC;UACjBkD,aAAa;UACbO,eAAe,EAAEV,WAAW;UAC5BW,qBAAqB,EAAE;QACzB,CAAC,CAAC;QACF;MACF;MACA,IAAIP,WAAW,IAAIE,UAAU,EAAE;QAC7B;QACA;QACA;MACF;IACF;IACAtD,mBAAmB,CAAC,CAAC;IACrBE,uBAAuB,CAAC8C,WAAW,CAAC;EACtC,CAAC,CAAC;EACF,MAAMY,mBAAmB,GAAGlH,gBAAgB,CAAC,CAAC,GAAG6F,IAAI,KAAK;IACxDrD,MAAM,GAAG,GAAGqD,IAAI,CAAC;IACjBjC,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC,CAAC;EACF,MAAMuD,iBAAiB,GAAGnH,gBAAgB,CAACkG,KAAK,IAAI;IAClD,IAAIpD,QAAQ,EAAE;MACZ;IACF;IACA,MAAMsE,WAAW,GAAGlB,KAAK,CAACmB,MAAM,CAACtB,KAAK;IACtC,IAAIqB,WAAW,KAAK,EAAE,EAAE;MACtB9D,mBAAmB,CAAC,CAAC;MACrBI,UAAU,CAAC,CAAC;MACZ;IACF;IACA,MAAM4D,SAAS,GAAGpB,KAAK,CAACqB,WAAW,CAACC,IAAI;IACxC;IACA;IACA,MAAMC,kBAAkB,GAAGH,SAAS,IAAIA,SAAS,CAACtG,MAAM,GAAG,CAAC;IAC5D,MAAM0G,QAAQ,GAAGD,kBAAkB,GAAGH,SAAS,GAAGF,WAAW;IAC7D,MAAMO,aAAa,GAAGtH,WAAW,CAACqH,QAAQ,CAAC;;IAE3C;IACA;IACA,IAAIzE,kBAAkB,IAAI,IAAI,IAAIwE,kBAAkB,EAAE;MACpDjE,uBAAuB,CAACiE,kBAAkB,GAAGH,SAAS,GAAGK,aAAa,CAAC;MACvE;IACF;IACA,IAAIC,UAAU;IACd,IAAI5E,sBAAsB,KAAK,KAAK,IAAI2E,aAAa,CAAC3G,MAAM,KAAK,CAAC,EAAE;MAClE4G,UAAU,GAAGD,aAAa;IAC5B,CAAC,MAAM;MACL,MAAME,YAAY,GAAGxH,WAAW,CAAC8C,iBAAiB,CAAC2E,2BAA2B,CAACrH,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,CAAC;MACjH,IAAIoH,gBAAgB,GAAG,CAAC,CAAC;MACzB,IAAIC,cAAc,GAAG,CAAC,CAAC;MACvB,KAAK,IAAIjH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8G,YAAY,CAAC7G,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC/C,IAAIgH,gBAAgB,KAAK,CAAC,CAAC,IAAIF,YAAY,CAAC9G,CAAC,CAAC,KAAK4G,aAAa,CAAC5G,CAAC,CAAC,EAAE;UACnEgH,gBAAgB,GAAGhH,CAAC;QACtB;QACA,IAAIiH,cAAc,KAAK,CAAC,CAAC,IAAIH,YAAY,CAACA,YAAY,CAAC7G,MAAM,GAAGD,CAAC,GAAG,CAAC,CAAC,KAAK4G,aAAa,CAACA,aAAa,CAAC3G,MAAM,GAAGD,CAAC,GAAG,CAAC,CAAC,EAAE;UACtHiH,cAAc,GAAGjH,CAAC;QACpB;MACF;MACA,MAAM0F,aAAa,GAAGhG,QAAQ,CAACwC,kBAAkB,CAAC;MAClD,MAAMgF,6BAA6B,GAAGF,gBAAgB,GAAGtB,aAAa,CAAC5E,KAAK,IAAIgG,YAAY,CAAC7G,MAAM,GAAGgH,cAAc,GAAG,CAAC,GAAGvB,aAAa,CAAC3E,GAAG;MAC5I,IAAImG,6BAA6B,EAAE;QACjC;QACA;MACF;;MAEA;MACA,MAAMC,kCAAkC,GAAGP,aAAa,CAAC3G,MAAM,GAAG6G,YAAY,CAAC7G,MAAM,GAAGyF,aAAa,CAAC3E,GAAG,GAAGzB,WAAW,CAACoG,aAAa,CAACpF,YAAY,IAAI,EAAE,CAAC,CAACL,MAAM;MAChK4G,UAAU,GAAGD,aAAa,CAACQ,KAAK,CAAC1B,aAAa,CAAC5E,KAAK,GAAGxB,WAAW,CAACoG,aAAa,CAACrF,cAAc,IAAI,EAAE,CAAC,CAACJ,MAAM,EAAEkH,kCAAkC,CAAC;IACpJ;IACA,IAAIN,UAAU,CAAC5G,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAIZ,SAAS,CAAC,CAAC,EAAE;QACfuD,sBAAsB,CAAC+D,QAAQ,CAAC;MAClC;MACApE,mBAAmB,CAAC,CAAC;MACrBG,kBAAkB,CAAC,CAAC;MACpB;IACF;IACAJ,qBAAqB,CAAC;MACpBuE,UAAU;MACVjC,YAAY,EAAE1C;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMN,WAAW,GAAG7C,KAAK,CAACkE,OAAO,CAAC,MAAM;IACtC,IAAIpB,aAAa,KAAKwF,SAAS,EAAE;MAC/B,OAAOxF,aAAa;IACtB;IACA,OAAOO,iBAAiB,CAAC2E,2BAA2B,CAACjE,oBAAoB,CAACT,YAAY,CAACiF,UAAU,CAAC,EAAE3H,eAAe,EAAEC,KAAK,CAAC;EAC7H,CAAC,EAAE,CAACiC,aAAa,EAAEO,iBAAiB,EAAEU,oBAAoB,EAAET,YAAY,CAACiF,UAAU,EAAE3H,eAAe,EAAEC,KAAK,CAAC,CAAC;EAC7G,MAAM+G,QAAQ,GAAG5H,KAAK,CAACkE,OAAO,CAAC,MAAMd,KAAK,CAACoF,mBAAmB,IAAInF,iBAAiB,CAAC2E,2BAA2B,CAAC5E,KAAK,CAACzC,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,EAAE,CAACuC,KAAK,CAACzC,QAAQ,EAAE0C,iBAAiB,EAAED,KAAK,CAACoF,mBAAmB,EAAE5H,eAAe,EAAEC,KAAK,CAAC,CAAC;EAChPb,KAAK,CAACyI,SAAS,CAAC,MAAM;IACpB;IACA,IAAI9F,QAAQ,CAAC0B,OAAO,IAAI1B,QAAQ,CAAC0B,OAAO,KAAKjE,gBAAgB,CAACmE,QAAQ,CAAC,EAAE;MACvET,mBAAmB,CAAC,KAAK,CAAC;IAC5B;IACA,OAAO,MAAM;MACXkB,YAAY,CAAC7C,eAAe,CAACkC,OAAO,CAAC;MACrCW,YAAY,CAAC3C,uBAAuB,CAACgC,OAAO,CAAC;IAC/C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,MAAMqE,SAAS,GAAG1I,KAAK,CAACkE,OAAO,CAAC,MAAM;IACpC,IAAIf,kBAAkB,IAAI,IAAI,EAAE;MAC9B,OAAO,MAAM;IACf;IACA,IAAIC,KAAK,CAACzC,QAAQ,CAACwC,kBAAkB,CAAC,CAAC8D,WAAW,KAAK,QAAQ,EAAE;MAC/D,OAAO,MAAM;IACf;IACA,OAAO,SAAS;EAClB,CAAC,EAAE,CAAC9D,kBAAkB,EAAEC,KAAK,CAACzC,QAAQ,CAAC,CAAC;EACxC,MAAMgI,aAAa,GAAGhG,QAAQ,CAAC0B,OAAO,IAAI1B,QAAQ,CAAC0B,OAAO,KAAKjE,gBAAgB,CAACmE,QAAQ,CAAC;EACzF,MAAMqE,qBAAqB,GAAG,CAACD,aAAa,IAAI3E,mBAAmB;EACnE,OAAO;IACLG,YAAY;IACZ0E,aAAa,EAAE;MACb;MACA7F,QAAQ;MACRN,MAAM,EAAE0E,mBAAmB;MAC3B5E,OAAO,EAAE2D,gBAAgB;MACzB5D,OAAO,EAAEuD,gBAAgB;MACzBrD,OAAO,EAAE6D,gBAAgB;MACzB3D,QAAQ,EAAEsB,SAAS;MACnB;MACA6E,iCAAiC,EAAE,KAAK;MACxCjG,WAAW;MACX6F,SAAS;MACTK,YAAY,EAAE,KAAK;MACnB9C,KAAK,EAAE2C,qBAAqB,GAAG,EAAE,GAAGhB,QAAQ;MAC5CoB,QAAQ,EAAE3B;IACZ;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}