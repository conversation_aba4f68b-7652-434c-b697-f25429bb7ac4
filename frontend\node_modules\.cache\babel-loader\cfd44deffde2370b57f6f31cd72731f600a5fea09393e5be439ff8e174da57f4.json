{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.fireChangeForInputTimeIfValid = fireChangeForInputTimeIfValid;\nvar _dom = require(\"@testing-library/dom\");\nvar _utils = require(\"../../utils\");\nfunction fireChangeForInputTimeIfValid(el, prevValue, timeNewEntry) {\n  if ((0, _utils.isValidInputTimeValue)(el, timeNewEntry) && prevValue !== timeNewEntry) {\n    _dom.fireEvent.change(el, {\n      target: {\n        value: timeNewEntry\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "fireChangeForInputTimeIfValid", "_dom", "require", "_utils", "el", "prevValue", "timeNewEntry", "isValidInputTimeValue", "fireEvent", "change", "target"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/user-event/dist/keyboard/shared/fireChangeForInputTimeIfValid.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.fireChangeForInputTimeIfValid = fireChangeForInputTimeIfValid;\n\nvar _dom = require(\"@testing-library/dom\");\n\nvar _utils = require(\"../../utils\");\n\nfunction fireChangeForInputTimeIfValid(el, prevValue, timeNewEntry) {\n  if ((0, _utils.isValidInputTimeValue)(el, timeNewEntry) && prevValue !== timeNewEntry) {\n    _dom.fireEvent.change(el, {\n      target: {\n        value: timeNewEntry\n      }\n    });\n  }\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,6BAA6B,GAAGA,6BAA6B;AAErE,IAAIC,IAAI,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAE1C,IAAIC,MAAM,GAAGD,OAAO,CAAC,aAAa,CAAC;AAEnC,SAASF,6BAA6BA,CAACI,EAAE,EAAEC,SAAS,EAAEC,YAAY,EAAE;EAClE,IAAI,CAAC,CAAC,EAAEH,MAAM,CAACI,qBAAqB,EAAEH,EAAE,EAAEE,YAAY,CAAC,IAAID,SAAS,KAAKC,YAAY,EAAE;IACrFL,IAAI,CAACO,SAAS,CAACC,MAAM,CAACL,EAAE,EAAE;MACxBM,MAAM,EAAE;QACNX,KAAK,EAAEO;MACT;IACF,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}