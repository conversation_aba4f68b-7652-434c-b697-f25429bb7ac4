{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useIsDateDisabled } from \"./useIsDateDisabled.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { SECTION_TYPE_GRANULARITY } from \"../internals/utils/getDefaultReferenceDate.js\";\nexport const createCalendarStateReducer = (reduceAnimations, disableSwitchToMonthOnDayFocus, utils) => (state, action) => {\n  switch (action.type) {\n    case 'changeMonth':\n      return _extends({}, state, {\n        slideDirection: action.direction,\n        currentMonth: action.newMonth,\n        isMonthSwitchingAnimating: !reduceAnimations\n      });\n    case 'finishMonthSwitchingAnimation':\n      return _extends({}, state, {\n        isMonthSwitchingAnimating: false\n      });\n    case 'changeFocusedDay':\n      {\n        if (state.focusedDay != null && action.focusedDay != null && utils.isSameDay(action.focusedDay, state.focusedDay)) {\n          return state;\n        }\n        const needMonthSwitch = action.focusedDay != null && !disableSwitchToMonthOnDayFocus && !utils.isSameMonth(state.currentMonth, action.focusedDay);\n        return _extends({}, state, {\n          focusedDay: action.focusedDay,\n          isMonthSwitchingAnimating: needMonthSwitch && !reduceAnimations && !action.withoutMonthSwitchingAnimation,\n          currentMonth: needMonthSwitch ? utils.startOfMonth(action.focusedDay) : state.currentMonth,\n          slideDirection: action.focusedDay != null && utils.isAfterDay(action.focusedDay, state.currentMonth) ? 'left' : 'right'\n        });\n      }\n    default:\n      throw new Error('missing support');\n  }\n};\nexport const useCalendarState = params => {\n  const {\n    value,\n    referenceDate: referenceDateProp,\n    disableFuture,\n    disablePast,\n    disableSwitchToMonthOnDayFocus = false,\n    maxDate,\n    minDate,\n    onMonthChange,\n    reduceAnimations,\n    shouldDisableDate,\n    timezone\n  } = params;\n  const utils = useUtils();\n  const reducerFn = React.useRef(createCalendarStateReducer(Boolean(reduceAnimations), disableSwitchToMonthOnDayFocus, utils)).current;\n  const referenceDate = React.useMemo(() => {\n    return singleItemValueManager.getInitialReferenceValue({\n      value,\n      utils,\n      timezone,\n      props: params,\n      referenceDate: referenceDateProp,\n      granularity: SECTION_TYPE_GRANULARITY.day\n    });\n  }, [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  const [calendarState, dispatch] = React.useReducer(reducerFn, {\n    isMonthSwitchingAnimating: false,\n    focusedDay: referenceDate,\n    currentMonth: utils.startOfMonth(referenceDate),\n    slideDirection: 'left'\n  });\n  const handleChangeMonth = React.useCallback(payload => {\n    dispatch(_extends({\n      type: 'changeMonth'\n    }, payload));\n    if (onMonthChange) {\n      onMonthChange(payload.newMonth);\n    }\n  }, [onMonthChange]);\n  const changeMonth = React.useCallback(newDate => {\n    const newDateRequested = newDate;\n    if (utils.isSameMonth(newDateRequested, calendarState.currentMonth)) {\n      return;\n    }\n    handleChangeMonth({\n      newMonth: utils.startOfMonth(newDateRequested),\n      direction: utils.isAfterDay(newDateRequested, calendarState.currentMonth) ? 'left' : 'right'\n    });\n  }, [calendarState.currentMonth, handleChangeMonth, utils]);\n  const isDateDisabled = useIsDateDisabled({\n    shouldDisableDate,\n    minDate,\n    maxDate,\n    disableFuture,\n    disablePast,\n    timezone\n  });\n  const onMonthSwitchingAnimationEnd = React.useCallback(() => {\n    dispatch({\n      type: 'finishMonthSwitchingAnimation'\n    });\n  }, []);\n  const changeFocusedDay = useEventCallback((newFocusedDate, withoutMonthSwitchingAnimation) => {\n    if (!isDateDisabled(newFocusedDate)) {\n      dispatch({\n        type: 'changeFocusedDay',\n        focusedDay: newFocusedDate,\n        withoutMonthSwitchingAnimation\n      });\n    }\n  });\n  return {\n    referenceDate,\n    calendarState,\n    changeMonth,\n    changeFocusedDay,\n    isDateDisabled,\n    onMonthSwitchingAnimationEnd,\n    handleChangeMonth\n  };\n};", "map": {"version": 3, "names": ["_extends", "React", "useEventCallback", "useIsDateDisabled", "useUtils", "singleItemValueManager", "SECTION_TYPE_GRANULARITY", "createCalendarStateReducer", "reduceAnimations", "disableSwitchToMonthOnDayFocus", "utils", "state", "action", "type", "slideDirection", "direction", "currentMonth", "newMonth", "isMonthSwitchingAnimating", "focusedDay", "isSameDay", "needMonthSwitch", "isSameMonth", "withoutMonthSwitchingAnimation", "startOfMonth", "isAfterDay", "Error", "useCalendarState", "params", "value", "referenceDate", "referenceDateProp", "disableFuture", "disablePast", "maxDate", "minDate", "onMonthChange", "shouldDisableDate", "timezone", "reducerFn", "useRef", "Boolean", "current", "useMemo", "getInitialReferenceValue", "props", "granularity", "day", "calendarState", "dispatch", "useReducer", "handleChangeMonth", "useCallback", "payload", "changeMonth", "newDate", "newDateRequested", "isDateDisabled", "onMonthSwitchingAnimationEnd", "changeFocusedDay", "newFocusedDate"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/DateCalendar/useCalendarState.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useIsDateDisabled } from \"./useIsDateDisabled.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { SECTION_TYPE_GRANULARITY } from \"../internals/utils/getDefaultReferenceDate.js\";\nexport const createCalendarStateReducer = (reduceAnimations, disableSwitchToMonthOnDayFocus, utils) => (state, action) => {\n  switch (action.type) {\n    case 'changeMonth':\n      return _extends({}, state, {\n        slideDirection: action.direction,\n        currentMonth: action.newMonth,\n        isMonthSwitchingAnimating: !reduceAnimations\n      });\n    case 'finishMonthSwitchingAnimation':\n      return _extends({}, state, {\n        isMonthSwitchingAnimating: false\n      });\n    case 'changeFocusedDay':\n      {\n        if (state.focusedDay != null && action.focusedDay != null && utils.isSameDay(action.focusedDay, state.focusedDay)) {\n          return state;\n        }\n        const needMonthSwitch = action.focusedDay != null && !disableSwitchToMonthOnDayFocus && !utils.isSameMonth(state.currentMonth, action.focusedDay);\n        return _extends({}, state, {\n          focusedDay: action.focusedDay,\n          isMonthSwitchingAnimating: needMonthSwitch && !reduceAnimations && !action.withoutMonthSwitchingAnimation,\n          currentMonth: needMonthSwitch ? utils.startOfMonth(action.focusedDay) : state.currentMonth,\n          slideDirection: action.focusedDay != null && utils.isAfterDay(action.focusedDay, state.currentMonth) ? 'left' : 'right'\n        });\n      }\n    default:\n      throw new Error('missing support');\n  }\n};\nexport const useCalendarState = params => {\n  const {\n    value,\n    referenceDate: referenceDateProp,\n    disableFuture,\n    disablePast,\n    disableSwitchToMonthOnDayFocus = false,\n    maxDate,\n    minDate,\n    onMonthChange,\n    reduceAnimations,\n    shouldDisableDate,\n    timezone\n  } = params;\n  const utils = useUtils();\n  const reducerFn = React.useRef(createCalendarStateReducer(Boolean(reduceAnimations), disableSwitchToMonthOnDayFocus, utils)).current;\n  const referenceDate = React.useMemo(() => {\n    return singleItemValueManager.getInitialReferenceValue({\n      value,\n      utils,\n      timezone,\n      props: params,\n      referenceDate: referenceDateProp,\n      granularity: SECTION_TYPE_GRANULARITY.day\n    });\n  }, [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  const [calendarState, dispatch] = React.useReducer(reducerFn, {\n    isMonthSwitchingAnimating: false,\n    focusedDay: referenceDate,\n    currentMonth: utils.startOfMonth(referenceDate),\n    slideDirection: 'left'\n  });\n  const handleChangeMonth = React.useCallback(payload => {\n    dispatch(_extends({\n      type: 'changeMonth'\n    }, payload));\n    if (onMonthChange) {\n      onMonthChange(payload.newMonth);\n    }\n  }, [onMonthChange]);\n  const changeMonth = React.useCallback(newDate => {\n    const newDateRequested = newDate;\n    if (utils.isSameMonth(newDateRequested, calendarState.currentMonth)) {\n      return;\n    }\n    handleChangeMonth({\n      newMonth: utils.startOfMonth(newDateRequested),\n      direction: utils.isAfterDay(newDateRequested, calendarState.currentMonth) ? 'left' : 'right'\n    });\n  }, [calendarState.currentMonth, handleChangeMonth, utils]);\n  const isDateDisabled = useIsDateDisabled({\n    shouldDisableDate,\n    minDate,\n    maxDate,\n    disableFuture,\n    disablePast,\n    timezone\n  });\n  const onMonthSwitchingAnimationEnd = React.useCallback(() => {\n    dispatch({\n      type: 'finishMonthSwitchingAnimation'\n    });\n  }, []);\n  const changeFocusedDay = useEventCallback((newFocusedDate, withoutMonthSwitchingAnimation) => {\n    if (!isDateDisabled(newFocusedDate)) {\n      dispatch({\n        type: 'changeFocusedDay',\n        focusedDay: newFocusedDate,\n        withoutMonthSwitchingAnimation\n      });\n    }\n  });\n  return {\n    referenceDate,\n    calendarState,\n    changeMonth,\n    changeFocusedDay,\n    isDateDisabled,\n    onMonthSwitchingAnimationEnd,\n    handleChangeMonth\n  };\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,OAAO,MAAMC,0BAA0B,GAAGA,CAACC,gBAAgB,EAAEC,8BAA8B,EAAEC,KAAK,KAAK,CAACC,KAAK,EAAEC,MAAM,KAAK;EACxH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,aAAa;MAChB,OAAOb,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;QACzBG,cAAc,EAAEF,MAAM,CAACG,SAAS;QAChCC,YAAY,EAAEJ,MAAM,CAACK,QAAQ;QAC7BC,yBAAyB,EAAE,CAACV;MAC9B,CAAC,CAAC;IACJ,KAAK,+BAA+B;MAClC,OAAOR,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;QACzBO,yBAAyB,EAAE;MAC7B,CAAC,CAAC;IACJ,KAAK,kBAAkB;MACrB;QACE,IAAIP,KAAK,CAACQ,UAAU,IAAI,IAAI,IAAIP,MAAM,CAACO,UAAU,IAAI,IAAI,IAAIT,KAAK,CAACU,SAAS,CAACR,MAAM,CAACO,UAAU,EAAER,KAAK,CAACQ,UAAU,CAAC,EAAE;UACjH,OAAOR,KAAK;QACd;QACA,MAAMU,eAAe,GAAGT,MAAM,CAACO,UAAU,IAAI,IAAI,IAAI,CAACV,8BAA8B,IAAI,CAACC,KAAK,CAACY,WAAW,CAACX,KAAK,CAACK,YAAY,EAAEJ,MAAM,CAACO,UAAU,CAAC;QACjJ,OAAOnB,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;UACzBQ,UAAU,EAAEP,MAAM,CAACO,UAAU;UAC7BD,yBAAyB,EAAEG,eAAe,IAAI,CAACb,gBAAgB,IAAI,CAACI,MAAM,CAACW,8BAA8B;UACzGP,YAAY,EAAEK,eAAe,GAAGX,KAAK,CAACc,YAAY,CAACZ,MAAM,CAACO,UAAU,CAAC,GAAGR,KAAK,CAACK,YAAY;UAC1FF,cAAc,EAAEF,MAAM,CAACO,UAAU,IAAI,IAAI,IAAIT,KAAK,CAACe,UAAU,CAACb,MAAM,CAACO,UAAU,EAAER,KAAK,CAACK,YAAY,CAAC,GAAG,MAAM,GAAG;QAClH,CAAC,CAAC;MACJ;IACF;MACE,MAAM,IAAIU,KAAK,CAAC,iBAAiB,CAAC;EACtC;AACF,CAAC;AACD,OAAO,MAAMC,gBAAgB,GAAGC,MAAM,IAAI;EACxC,MAAM;IACJC,KAAK;IACLC,aAAa,EAAEC,iBAAiB;IAChCC,aAAa;IACbC,WAAW;IACXxB,8BAA8B,GAAG,KAAK;IACtCyB,OAAO;IACPC,OAAO;IACPC,aAAa;IACb5B,gBAAgB;IAChB6B,iBAAiB;IACjBC;EACF,CAAC,GAAGV,MAAM;EACV,MAAMlB,KAAK,GAAGN,QAAQ,CAAC,CAAC;EACxB,MAAMmC,SAAS,GAAGtC,KAAK,CAACuC,MAAM,CAACjC,0BAA0B,CAACkC,OAAO,CAACjC,gBAAgB,CAAC,EAAEC,8BAA8B,EAAEC,KAAK,CAAC,CAAC,CAACgC,OAAO;EACpI,MAAMZ,aAAa,GAAG7B,KAAK,CAAC0C,OAAO,CAAC,MAAM;IACxC,OAAOtC,sBAAsB,CAACuC,wBAAwB,CAAC;MACrDf,KAAK;MACLnB,KAAK;MACL4B,QAAQ;MACRO,KAAK,EAAEjB,MAAM;MACbE,aAAa,EAAEC,iBAAiB;MAChCe,WAAW,EAAExC,wBAAwB,CAACyC;IACxC,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,CAAC;EACD,MAAM,CAACC,aAAa,EAAEC,QAAQ,CAAC,GAAGhD,KAAK,CAACiD,UAAU,CAACX,SAAS,EAAE;IAC5DrB,yBAAyB,EAAE,KAAK;IAChCC,UAAU,EAAEW,aAAa;IACzBd,YAAY,EAAEN,KAAK,CAACc,YAAY,CAACM,aAAa,CAAC;IAC/ChB,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAMqC,iBAAiB,GAAGlD,KAAK,CAACmD,WAAW,CAACC,OAAO,IAAI;IACrDJ,QAAQ,CAACjD,QAAQ,CAAC;MAChBa,IAAI,EAAE;IACR,CAAC,EAAEwC,OAAO,CAAC,CAAC;IACZ,IAAIjB,aAAa,EAAE;MACjBA,aAAa,CAACiB,OAAO,CAACpC,QAAQ,CAAC;IACjC;EACF,CAAC,EAAE,CAACmB,aAAa,CAAC,CAAC;EACnB,MAAMkB,WAAW,GAAGrD,KAAK,CAACmD,WAAW,CAACG,OAAO,IAAI;IAC/C,MAAMC,gBAAgB,GAAGD,OAAO;IAChC,IAAI7C,KAAK,CAACY,WAAW,CAACkC,gBAAgB,EAAER,aAAa,CAAChC,YAAY,CAAC,EAAE;MACnE;IACF;IACAmC,iBAAiB,CAAC;MAChBlC,QAAQ,EAAEP,KAAK,CAACc,YAAY,CAACgC,gBAAgB,CAAC;MAC9CzC,SAAS,EAAEL,KAAK,CAACe,UAAU,CAAC+B,gBAAgB,EAAER,aAAa,CAAChC,YAAY,CAAC,GAAG,MAAM,GAAG;IACvF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACgC,aAAa,CAAChC,YAAY,EAAEmC,iBAAiB,EAAEzC,KAAK,CAAC,CAAC;EAC1D,MAAM+C,cAAc,GAAGtD,iBAAiB,CAAC;IACvCkC,iBAAiB;IACjBF,OAAO;IACPD,OAAO;IACPF,aAAa;IACbC,WAAW;IACXK;EACF,CAAC,CAAC;EACF,MAAMoB,4BAA4B,GAAGzD,KAAK,CAACmD,WAAW,CAAC,MAAM;IAC3DH,QAAQ,CAAC;MACPpC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,MAAM8C,gBAAgB,GAAGzD,gBAAgB,CAAC,CAAC0D,cAAc,EAAErC,8BAA8B,KAAK;IAC5F,IAAI,CAACkC,cAAc,CAACG,cAAc,CAAC,EAAE;MACnCX,QAAQ,CAAC;QACPpC,IAAI,EAAE,kBAAkB;QACxBM,UAAU,EAAEyC,cAAc;QAC1BrC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAO;IACLO,aAAa;IACbkB,aAAa;IACbM,WAAW;IACXK,gBAAgB;IAChBF,cAAc;IACdC,4BAA4B;IAC5BP;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}