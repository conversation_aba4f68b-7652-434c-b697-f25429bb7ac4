{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.TEXT_NODE = void 0;\nexports.checkContainerType = checkContainerType;\nexports.getDocument = getDocument;\nexports.getWindowFromNode = getWindowFromNode;\nexports.jestFakeTimersAreEnabled = jestFakeTimersAreEnabled;\n// Constant node.nodeType for text nodes, see:\n// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType#Node_type_constants\nconst TEXT_NODE = exports.TEXT_NODE = 3;\nfunction jestFakeTimersAreEnabled() {\n  /* istanbul ignore else */\n  // eslint-disable-next-line\n  if (typeof jest !== 'undefined' && jest !== null) {\n    return (\n      // legacy timers\n      setTimeout._isMockFunction === true ||\n      // modern timers\n      // eslint-disable-next-line prefer-object-has-own -- not supported by our support matrix\n      Object.prototype.hasOwnProperty.call(setTimeout, 'clock')\n    );\n  }\n  // istanbul ignore next\n  return false;\n}\nfunction getDocument() {\n  /* istanbul ignore if */\n  if (typeof window === 'undefined') {\n    throw new Error('Could not find default container');\n  }\n  return window.document;\n}\nfunction getWindowFromNode(node) {\n  if (node.defaultView) {\n    // node is document\n    return node.defaultView;\n  } else if (node.ownerDocument && node.ownerDocument.defaultView) {\n    // node is a DOM node\n    return node.ownerDocument.defaultView;\n  } else if (node.window) {\n    // node is window\n    return node.window;\n  } else if (node.ownerDocument && node.ownerDocument.defaultView === null) {\n    throw new Error(`It looks like the window object is not available for the provided node.`);\n  } else if (node.then instanceof Function) {\n    throw new Error(`It looks like you passed a Promise object instead of a DOM node. Did you do something like \\`fireEvent.click(screen.findBy...\\` when you meant to use a \\`getBy\\` query \\`fireEvent.click(screen.getBy...\\`, or await the findBy query \\`fireEvent.click(await screen.findBy...\\`?`);\n  } else if (Array.isArray(node)) {\n    throw new Error(`It looks like you passed an Array instead of a DOM node. Did you do something like \\`fireEvent.click(screen.getAllBy...\\` when you meant to use a \\`getBy\\` query \\`fireEvent.click(screen.getBy...\\`?`);\n  } else if (typeof node.debug === 'function' && typeof node.logTestingPlaygroundURL === 'function') {\n    throw new Error(`It looks like you passed a \\`screen\\` object. Did you do something like \\`fireEvent.click(screen, ...\\` when you meant to use a query, e.g. \\`fireEvent.click(screen.getBy..., \\`?`);\n  } else {\n    // The user passed something unusual to a calling function\n    throw new Error(`The given node is not an Element, the node type is: ${typeof node}.`);\n  }\n}\nfunction checkContainerType(container) {\n  if (!container || !(typeof container.querySelector === 'function') || !(typeof container.querySelectorAll === 'function')) {\n    throw new TypeError(`Expected container to be an Element, a Document or a DocumentFragment but got ${getTypeName(container)}.`);\n  }\n  function getTypeName(object) {\n    if (typeof object === 'object') {\n      return object === null ? 'null' : object.constructor.name;\n    }\n    return typeof object;\n  }\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "TEXT_NODE", "checkContainerType", "getDocument", "getWindowFromNode", "jestFakeTimersAreEnabled", "jest", "setTimeout", "_isMockFunction", "prototype", "hasOwnProperty", "call", "window", "Error", "document", "node", "defaultView", "ownerDocument", "then", "Function", "Array", "isArray", "debug", "logTestingPlaygroundURL", "container", "querySelector", "querySelectorAll", "TypeError", "getTypeName", "object", "constructor", "name"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/dist/helpers.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.TEXT_NODE = void 0;\nexports.checkContainerType = checkContainerType;\nexports.getDocument = getDocument;\nexports.getWindowFromNode = getWindowFromNode;\nexports.jestFakeTimersAreEnabled = jestFakeTimersAreEnabled;\n// Constant node.nodeType for text nodes, see:\n// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType#Node_type_constants\nconst TEXT_NODE = exports.TEXT_NODE = 3;\nfunction jestFakeTimersAreEnabled() {\n  /* istanbul ignore else */\n  // eslint-disable-next-line\n  if (typeof jest !== 'undefined' && jest !== null) {\n    return (\n      // legacy timers\n      setTimeout._isMockFunction === true ||\n      // modern timers\n      // eslint-disable-next-line prefer-object-has-own -- not supported by our support matrix\n      Object.prototype.hasOwnProperty.call(setTimeout, 'clock')\n    );\n  }\n  // istanbul ignore next\n  return false;\n}\nfunction getDocument() {\n  /* istanbul ignore if */\n  if (typeof window === 'undefined') {\n    throw new Error('Could not find default container');\n  }\n  return window.document;\n}\nfunction getWindowFromNode(node) {\n  if (node.defaultView) {\n    // node is document\n    return node.defaultView;\n  } else if (node.ownerDocument && node.ownerDocument.defaultView) {\n    // node is a DOM node\n    return node.ownerDocument.defaultView;\n  } else if (node.window) {\n    // node is window\n    return node.window;\n  } else if (node.ownerDocument && node.ownerDocument.defaultView === null) {\n    throw new Error(`It looks like the window object is not available for the provided node.`);\n  } else if (node.then instanceof Function) {\n    throw new Error(`It looks like you passed a Promise object instead of a DOM node. Did you do something like \\`fireEvent.click(screen.findBy...\\` when you meant to use a \\`getBy\\` query \\`fireEvent.click(screen.getBy...\\`, or await the findBy query \\`fireEvent.click(await screen.findBy...\\`?`);\n  } else if (Array.isArray(node)) {\n    throw new Error(`It looks like you passed an Array instead of a DOM node. Did you do something like \\`fireEvent.click(screen.getAllBy...\\` when you meant to use a \\`getBy\\` query \\`fireEvent.click(screen.getBy...\\`?`);\n  } else if (typeof node.debug === 'function' && typeof node.logTestingPlaygroundURL === 'function') {\n    throw new Error(`It looks like you passed a \\`screen\\` object. Did you do something like \\`fireEvent.click(screen, ...\\` when you meant to use a query, e.g. \\`fireEvent.click(screen.getBy..., \\`?`);\n  } else {\n    // The user passed something unusual to a calling function\n    throw new Error(`The given node is not an Element, the node type is: ${typeof node}.`);\n  }\n}\nfunction checkContainerType(container) {\n  if (!container || !(typeof container.querySelector === 'function') || !(typeof container.querySelectorAll === 'function')) {\n    throw new TypeError(`Expected container to be an Element, a Document or a DocumentFragment but got ${getTypeName(container)}.`);\n  }\n  function getTypeName(object) {\n    if (typeof object === 'object') {\n      return object === null ? 'null' : object.constructor.name;\n    }\n    return typeof object;\n  }\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1BF,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/CH,OAAO,CAACI,WAAW,GAAGA,WAAW;AACjCJ,OAAO,CAACK,iBAAiB,GAAGA,iBAAiB;AAC7CL,OAAO,CAACM,wBAAwB,GAAGA,wBAAwB;AAC3D;AACA;AACA,MAAMJ,SAAS,GAAGF,OAAO,CAACE,SAAS,GAAG,CAAC;AACvC,SAASI,wBAAwBA,CAAA,EAAG;EAClC;EACA;EACA,IAAI,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,IAAI,EAAE;IAChD;MACE;MACAC,UAAU,CAACC,eAAe,KAAK,IAAI;MACnC;MACA;MACAX,MAAM,CAACY,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,UAAU,EAAE,OAAO;IAAC;EAE7D;EACA;EACA,OAAO,KAAK;AACd;AACA,SAASJ,WAAWA,CAAA,EAAG;EACrB;EACA,IAAI,OAAOS,MAAM,KAAK,WAAW,EAAE;IACjC,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;EACrD;EACA,OAAOD,MAAM,CAACE,QAAQ;AACxB;AACA,SAASV,iBAAiBA,CAACW,IAAI,EAAE;EAC/B,IAAIA,IAAI,CAACC,WAAW,EAAE;IACpB;IACA,OAAOD,IAAI,CAACC,WAAW;EACzB,CAAC,MAAM,IAAID,IAAI,CAACE,aAAa,IAAIF,IAAI,CAACE,aAAa,CAACD,WAAW,EAAE;IAC/D;IACA,OAAOD,IAAI,CAACE,aAAa,CAACD,WAAW;EACvC,CAAC,MAAM,IAAID,IAAI,CAACH,MAAM,EAAE;IACtB;IACA,OAAOG,IAAI,CAACH,MAAM;EACpB,CAAC,MAAM,IAAIG,IAAI,CAACE,aAAa,IAAIF,IAAI,CAACE,aAAa,CAACD,WAAW,KAAK,IAAI,EAAE;IACxE,MAAM,IAAIH,KAAK,CAAC,yEAAyE,CAAC;EAC5F,CAAC,MAAM,IAAIE,IAAI,CAACG,IAAI,YAAYC,QAAQ,EAAE;IACxC,MAAM,IAAIN,KAAK,CAAC,oRAAoR,CAAC;EACvS,CAAC,MAAM,IAAIO,KAAK,CAACC,OAAO,CAACN,IAAI,CAAC,EAAE;IAC9B,MAAM,IAAIF,KAAK,CAAC,wMAAwM,CAAC;EAC3N,CAAC,MAAM,IAAI,OAAOE,IAAI,CAACO,KAAK,KAAK,UAAU,IAAI,OAAOP,IAAI,CAACQ,uBAAuB,KAAK,UAAU,EAAE;IACjG,MAAM,IAAIV,KAAK,CAAC,oLAAoL,CAAC;EACvM,CAAC,MAAM;IACL;IACA,MAAM,IAAIA,KAAK,CAAC,uDAAuD,OAAOE,IAAI,GAAG,CAAC;EACxF;AACF;AACA,SAASb,kBAAkBA,CAACsB,SAAS,EAAE;EACrC,IAAI,CAACA,SAAS,IAAI,EAAE,OAAOA,SAAS,CAACC,aAAa,KAAK,UAAU,CAAC,IAAI,EAAE,OAAOD,SAAS,CAACE,gBAAgB,KAAK,UAAU,CAAC,EAAE;IACzH,MAAM,IAAIC,SAAS,CAAC,iFAAiFC,WAAW,CAACJ,SAAS,CAAC,GAAG,CAAC;EACjI;EACA,SAASI,WAAWA,CAACC,MAAM,EAAE;IAC3B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAOA,MAAM,KAAK,IAAI,GAAG,MAAM,GAAGA,MAAM,CAACC,WAAW,CAACC,IAAI;IAC3D;IACA,OAAO,OAAOF,MAAM;EACtB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}