{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M4 20h16V4H4zm5-2c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m0-3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m3 3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m0-3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m3 3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m0-3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m-8-4c0-2.76 2.24-5 5-5s5 2.24 5 5v1H7z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"15\",\n  cy: \"14\",\n  r: \"1\"\n}, \"1\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"14\",\n  r: \"1\"\n}, \"2\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"15\",\n  cy: \"17\",\n  r: \"1\"\n}, \"3\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M20 2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m0 18H4V4h16z\"\n}, \"4\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M17 11c0-2.76-2.24-5-5-5s-5 2.24-5 5v1h10zm-8.46-.5c.24-1.69 1.7-3 3.46-3s3.22 1.31 3.47 3z\"\n}, \"5\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"9\",\n  cy: \"17\",\n  r: \"1\"\n}, \"6\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"9\",\n  cy: \"14\",\n  r: \"1\"\n}, \"7\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"17\",\n  r: \"1\"\n}, \"8\")], 'BathroomTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity", "cx", "cy", "r"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/icons-material/esm/BathroomTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M4 20h16V4H4zm5-2c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m0-3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m3 3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m0-3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m3 3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m0-3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m-8-4c0-2.76 2.24-5 5-5s5 2.24 5 5v1H7z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"15\",\n  cy: \"14\",\n  r: \"1\"\n}, \"1\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"14\",\n  r: \"1\"\n}, \"2\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"15\",\n  cy: \"17\",\n  r: \"1\"\n}, \"3\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M20 2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m0 18H4V4h16z\"\n}, \"4\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M17 11c0-2.76-2.24-5-5-5s-5 2.24-5 5v1h10zm-8.46-.5c.24-1.69 1.7-3 3.46-3s3.22 1.31 3.47 3z\"\n}, \"5\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"9\",\n  cy: \"17\",\n  r: \"1\"\n}, \"6\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"9\",\n  cy: \"14\",\n  r: \"1\"\n}, \"7\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"17\",\n  r: \"1\"\n}, \"8\")], 'BathroomTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,wWAAwW;EAC3WC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,QAAQ,EAAE;EACnCG,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaL,IAAI,CAAC,QAAQ,EAAE;EACnCG,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaL,IAAI,CAAC,QAAQ,EAAE;EACnCG,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaL,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,QAAQ,EAAE;EACnCG,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaL,IAAI,CAAC,QAAQ,EAAE;EACnCG,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaL,IAAI,CAAC,QAAQ,EAAE;EACnCG,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}