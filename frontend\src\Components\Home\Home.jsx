import React, { useEffect, useState } from "react"
import "./Home.css"
import MultiItemCarousel from "./MultiItemCarousel"
import RestaurantCard from "../Restaurant/RestaurantCard"
import Auth from "../Auth/Auth"
import { useDispatch, useSelector } from "react-redux"
import { getAllRestaurants } from "../State/Restaurant/action"
import { useNavigate } from "react-router-dom"
import { findCard } from "../State/Card/action"
import { restaurantSeedData } from "../../Data/RestaurantSeedData"
import { CircularProgress } from "@mui/material"
export const Home = () => {
    const dispatch = useDispatch()
    const jwt = localStorage.getItem("token")
    const navigate = useNavigate()
    const {restaurant} = useSelector(store=>store)
    const [isLoading, setIsLoading] = useState(true)
    const [displayRestaurants, setDisplayRestaurants] = useState([])

    useEffect(()=> {
        dispatch(getAllRestaurants(jwt))

        // Simulate loading and use seed data if no restaurants from API
        setTimeout(() => {
            setIsLoading(false)
            if (restaurant.restaurants.length === 0) {
                setDisplayRestaurants(restaurantSeedData)
            } else {
                setDisplayRestaurants(restaurant.restaurants)
            }
        }, 1500)

    },[])

    useEffect(() => {
        if (restaurant.restaurants.length > 0) {
            setDisplayRestaurants(restaurant.restaurants)
            setIsLoading(false)
        }
    }, [restaurant.restaurants])


    return(
        <div className="pb-10">
            <section className="banner -z-50 relative flex flex-col justify-center items-center min-h-[70vh]">

                <div className="w-[50vw] z-10 text-center animate-fade-in-up">

                    <p className="text-2xl lg:text-6xl font-bold z-10 py-5 bg-gradient-to-r from-orange-400 to-red-600 bg-clip-text text-transparent animate-pulse">
                        Zashopp Food
                    </p>
                    <p className="z-10 text-gray-300 text-xl lg:text-4xl animate-fade-in-up delay-300">
                        Delicious Food, Delivered Fresh to Your Door!
                    </p>
                    <p className="z-10 text-gray-400 text-lg mt-4 animate-fade-in-up delay-500">
                        🍕 20+ Restaurants • 🚚 Fast Delivery • ⭐ Premium Quality
                    </p>

                </div>

                <div className="cover absolute top-0 left-0 right-0">

                </div>

                <div className="footer">

                </div>
            </section>

            <section className="p-10 lg:py-10 lg:px-20">
                <p className="text-2xl font-semibold text-gray-400 py-3 pb-10 animate-fade-in-left">Top Meals</p>
                <div className="animate-fade-in-up">
                    <MultiItemCarousel/>
                </div>
            </section>


            <section className="px-5 lg:px-20 pt-10">
                <h1 className="text-2xl font-semibold text-gray-400 pb-8 animate-fade-in-left">
                    Order From Our Best Restaurants
                    <span className="text-orange-500 ml-2">({displayRestaurants.length} Available)</span>
                </h1>
            </section>

            {isLoading ? (
                <div className="flex justify-center items-center py-20">
                    <div className="text-center">
                        <CircularProgress size={60} className="text-orange-500 mb-4" />
                        <p className="text-gray-500 text-lg">Loading delicious restaurants...</p>
                    </div>
                </div>
            ) : (
                <div className="flex flex-wrap items-center justify-center gap-8 px-5 lg:px-20 animate-fade-in-up">
                    {displayRestaurants.map((item, index) => (
                        <div
                            key={item.id || index}
                            className="animate-fade-in-up"
                            style={{ animationDelay: `${index * 100}ms` }}
                        >
                            <RestaurantCard item={item}/>
                        </div>
                    ))}
                </div>
            )}

            {!isLoading && displayRestaurants.length === 0 && (
                <div className="text-center py-20">
                    <p className="text-gray-500 text-xl">No restaurants available at the moment</p>
                    <p className="text-gray-400 text-sm mt-2">Please check back later</p>
                </div>
            )}

        </div>
    )
}

export default Home