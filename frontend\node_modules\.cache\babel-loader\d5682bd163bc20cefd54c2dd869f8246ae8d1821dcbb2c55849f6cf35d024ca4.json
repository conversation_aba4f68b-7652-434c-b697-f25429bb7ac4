{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _graphicsDocumentRole = _interopRequireDefault(require(\"./graphics/graphicsDocumentRole\"));\nvar _graphicsObjectRole = _interopRequireDefault(require(\"./graphics/graphicsObjectRole\"));\nvar _graphicsSymbolRole = _interopRequireDefault(require(\"./graphics/graphicsSymbolRole\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar ariaGraphicsRoles = [['graphics-document', _graphicsDocumentRole.default], ['graphics-object', _graphicsObjectRole.default], ['graphics-symbol', _graphicsSymbolRole.default]];\nvar _default = ariaGraphicsRoles;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_graphicsDocumentRole", "_interopRequireDefault", "require", "_graphicsObjectRole", "_graphicsSymbolRole", "obj", "__esModule", "ariaGraphicsRoles", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/etc/roles/ariaGraphicsRoles.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _graphicsDocumentRole = _interopRequireDefault(require(\"./graphics/graphicsDocumentRole\"));\nvar _graphicsObjectRole = _interopRequireDefault(require(\"./graphics/graphicsObjectRole\"));\nvar _graphicsSymbolRole = _interopRequireDefault(require(\"./graphics/graphicsSymbolRole\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar ariaGraphicsRoles = [['graphics-document', _graphicsDocumentRole.default], ['graphics-object', _graphicsObjectRole.default], ['graphics-symbol', _graphicsSymbolRole.default]];\nvar _default = ariaGraphicsRoles;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,qBAAqB,GAAGC,sBAAsB,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC9F,IAAIC,mBAAmB,GAAGF,sBAAsB,CAACC,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAC1F,IAAIE,mBAAmB,GAAGH,sBAAsB,CAACC,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAC1F,SAASD,sBAAsBA,CAACI,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,IAAIE,iBAAiB,GAAG,CAAC,CAAC,mBAAmB,EAAEP,qBAAqB,CAACD,OAAO,CAAC,EAAE,CAAC,iBAAiB,EAAEI,mBAAmB,CAACJ,OAAO,CAAC,EAAE,CAAC,iBAAiB,EAAEK,mBAAmB,CAACL,OAAO,CAAC,CAAC;AAClL,IAAIS,QAAQ,GAAGD,iBAAiB;AAChCV,OAAO,CAACE,OAAO,GAAGS,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}