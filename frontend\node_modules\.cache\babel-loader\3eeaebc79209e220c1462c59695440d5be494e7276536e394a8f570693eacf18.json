{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _docAbstractRole = _interopRequireDefault(require(\"./dpub/docAbstractRole\"));\nvar _docAcknowledgmentsRole = _interopRequireDefault(require(\"./dpub/docAcknowledgmentsRole\"));\nvar _docAfterwordRole = _interopRequireDefault(require(\"./dpub/docAfterwordRole\"));\nvar _docAppendixRole = _interopRequireDefault(require(\"./dpub/docAppendixRole\"));\nvar _docBacklinkRole = _interopRequireDefault(require(\"./dpub/docBacklinkRole\"));\nvar _docBiblioentryRole = _interopRequireDefault(require(\"./dpub/docBiblioentryRole\"));\nvar _docBibliographyRole = _interopRequireDefault(require(\"./dpub/docBibliographyRole\"));\nvar _docBibliorefRole = _interopRequireDefault(require(\"./dpub/docBibliorefRole\"));\nvar _docChapterRole = _interopRequireDefault(require(\"./dpub/docChapterRole\"));\nvar _docColophonRole = _interopRequireDefault(require(\"./dpub/docColophonRole\"));\nvar _docConclusionRole = _interopRequireDefault(require(\"./dpub/docConclusionRole\"));\nvar _docCoverRole = _interopRequireDefault(require(\"./dpub/docCoverRole\"));\nvar _docCreditRole = _interopRequireDefault(require(\"./dpub/docCreditRole\"));\nvar _docCreditsRole = _interopRequireDefault(require(\"./dpub/docCreditsRole\"));\nvar _docDedicationRole = _interopRequireDefault(require(\"./dpub/docDedicationRole\"));\nvar _docEndnoteRole = _interopRequireDefault(require(\"./dpub/docEndnoteRole\"));\nvar _docEndnotesRole = _interopRequireDefault(require(\"./dpub/docEndnotesRole\"));\nvar _docEpigraphRole = _interopRequireDefault(require(\"./dpub/docEpigraphRole\"));\nvar _docEpilogueRole = _interopRequireDefault(require(\"./dpub/docEpilogueRole\"));\nvar _docErrataRole = _interopRequireDefault(require(\"./dpub/docErrataRole\"));\nvar _docExampleRole = _interopRequireDefault(require(\"./dpub/docExampleRole\"));\nvar _docFootnoteRole = _interopRequireDefault(require(\"./dpub/docFootnoteRole\"));\nvar _docForewordRole = _interopRequireDefault(require(\"./dpub/docForewordRole\"));\nvar _docGlossaryRole = _interopRequireDefault(require(\"./dpub/docGlossaryRole\"));\nvar _docGlossrefRole = _interopRequireDefault(require(\"./dpub/docGlossrefRole\"));\nvar _docIndexRole = _interopRequireDefault(require(\"./dpub/docIndexRole\"));\nvar _docIntroductionRole = _interopRequireDefault(require(\"./dpub/docIntroductionRole\"));\nvar _docNoterefRole = _interopRequireDefault(require(\"./dpub/docNoterefRole\"));\nvar _docNoticeRole = _interopRequireDefault(require(\"./dpub/docNoticeRole\"));\nvar _docPagebreakRole = _interopRequireDefault(require(\"./dpub/docPagebreakRole\"));\nvar _docPagelistRole = _interopRequireDefault(require(\"./dpub/docPagelistRole\"));\nvar _docPartRole = _interopRequireDefault(require(\"./dpub/docPartRole\"));\nvar _docPrefaceRole = _interopRequireDefault(require(\"./dpub/docPrefaceRole\"));\nvar _docPrologueRole = _interopRequireDefault(require(\"./dpub/docPrologueRole\"));\nvar _docPullquoteRole = _interopRequireDefault(require(\"./dpub/docPullquoteRole\"));\nvar _docQnaRole = _interopRequireDefault(require(\"./dpub/docQnaRole\"));\nvar _docSubtitleRole = _interopRequireDefault(require(\"./dpub/docSubtitleRole\"));\nvar _docTipRole = _interopRequireDefault(require(\"./dpub/docTipRole\"));\nvar _docTocRole = _interopRequireDefault(require(\"./dpub/docTocRole\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar ariaDpubRoles = [['doc-abstract', _docAbstractRole.default], ['doc-acknowledgments', _docAcknowledgmentsRole.default], ['doc-afterword', _docAfterwordRole.default], ['doc-appendix', _docAppendixRole.default], ['doc-backlink', _docBacklinkRole.default], ['doc-biblioentry', _docBiblioentryRole.default], ['doc-bibliography', _docBibliographyRole.default], ['doc-biblioref', _docBibliorefRole.default], ['doc-chapter', _docChapterRole.default], ['doc-colophon', _docColophonRole.default], ['doc-conclusion', _docConclusionRole.default], ['doc-cover', _docCoverRole.default], ['doc-credit', _docCreditRole.default], ['doc-credits', _docCreditsRole.default], ['doc-dedication', _docDedicationRole.default], ['doc-endnote', _docEndnoteRole.default], ['doc-endnotes', _docEndnotesRole.default], ['doc-epigraph', _docEpigraphRole.default], ['doc-epilogue', _docEpilogueRole.default], ['doc-errata', _docErrataRole.default], ['doc-example', _docExampleRole.default], ['doc-footnote', _docFootnoteRole.default], ['doc-foreword', _docForewordRole.default], ['doc-glossary', _docGlossaryRole.default], ['doc-glossref', _docGlossrefRole.default], ['doc-index', _docIndexRole.default], ['doc-introduction', _docIntroductionRole.default], ['doc-noteref', _docNoterefRole.default], ['doc-notice', _docNoticeRole.default], ['doc-pagebreak', _docPagebreakRole.default], ['doc-pagelist', _docPagelistRole.default], ['doc-part', _docPartRole.default], ['doc-preface', _docPrefaceRole.default], ['doc-prologue', _docPrologueRole.default], ['doc-pullquote', _docPullquoteRole.default], ['doc-qna', _docQnaRole.default], ['doc-subtitle', _docSubtitleRole.default], ['doc-tip', _docTipRole.default], ['doc-toc', _docTocRole.default]];\nvar _default = ariaDpubRoles;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_docAbstractRole", "_interopRequireDefault", "require", "_docAcknowledgmentsRole", "_docAfterwordRole", "_docAppendixRole", "_docBacklinkRole", "_docBiblioentryRole", "_docBibliographyRole", "_docBibliorefRole", "_docChapterRole", "_docColophonRole", "_docConclusionRole", "_docCoverRole", "_docCreditRole", "_docCreditsRole", "_docDedicationRole", "_docEndnoteRole", "_docEndnotesRole", "_docEpigraphRole", "_docEpilogueRole", "_docErrataRole", "_docExampleRole", "_docFootnoteRole", "_docForewordRole", "_docGlossaryRole", "_docGlossrefRole", "_docIndexRole", "_docIntroductionRole", "_docNoterefRole", "_docNoticeRole", "_docPagebreakRole", "_docPagelistRole", "_docPartRole", "_docPrefaceRole", "_docPrologueRole", "_docPullquoteRole", "_docQnaRole", "_docSubtitleRole", "_docTipRole", "_docTocRole", "obj", "__esModule", "ariaDpubRoles", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/etc/roles/ariaDpubRoles.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _docAbstractRole = _interopRequireDefault(require(\"./dpub/docAbstractRole\"));\nvar _docAcknowledgmentsRole = _interopRequireDefault(require(\"./dpub/docAcknowledgmentsRole\"));\nvar _docAfterwordRole = _interopRequireDefault(require(\"./dpub/docAfterwordRole\"));\nvar _docAppendixRole = _interopRequireDefault(require(\"./dpub/docAppendixRole\"));\nvar _docBacklinkRole = _interopRequireDefault(require(\"./dpub/docBacklinkRole\"));\nvar _docBiblioentryRole = _interopRequireDefault(require(\"./dpub/docBiblioentryRole\"));\nvar _docBibliographyRole = _interopRequireDefault(require(\"./dpub/docBibliographyRole\"));\nvar _docBibliorefRole = _interopRequireDefault(require(\"./dpub/docBibliorefRole\"));\nvar _docChapterRole = _interopRequireDefault(require(\"./dpub/docChapterRole\"));\nvar _docColophonRole = _interopRequireDefault(require(\"./dpub/docColophonRole\"));\nvar _docConclusionRole = _interopRequireDefault(require(\"./dpub/docConclusionRole\"));\nvar _docCoverRole = _interopRequireDefault(require(\"./dpub/docCoverRole\"));\nvar _docCreditRole = _interopRequireDefault(require(\"./dpub/docCreditRole\"));\nvar _docCreditsRole = _interopRequireDefault(require(\"./dpub/docCreditsRole\"));\nvar _docDedicationRole = _interopRequireDefault(require(\"./dpub/docDedicationRole\"));\nvar _docEndnoteRole = _interopRequireDefault(require(\"./dpub/docEndnoteRole\"));\nvar _docEndnotesRole = _interopRequireDefault(require(\"./dpub/docEndnotesRole\"));\nvar _docEpigraphRole = _interopRequireDefault(require(\"./dpub/docEpigraphRole\"));\nvar _docEpilogueRole = _interopRequireDefault(require(\"./dpub/docEpilogueRole\"));\nvar _docErrataRole = _interopRequireDefault(require(\"./dpub/docErrataRole\"));\nvar _docExampleRole = _interopRequireDefault(require(\"./dpub/docExampleRole\"));\nvar _docFootnoteRole = _interopRequireDefault(require(\"./dpub/docFootnoteRole\"));\nvar _docForewordRole = _interopRequireDefault(require(\"./dpub/docForewordRole\"));\nvar _docGlossaryRole = _interopRequireDefault(require(\"./dpub/docGlossaryRole\"));\nvar _docGlossrefRole = _interopRequireDefault(require(\"./dpub/docGlossrefRole\"));\nvar _docIndexRole = _interopRequireDefault(require(\"./dpub/docIndexRole\"));\nvar _docIntroductionRole = _interopRequireDefault(require(\"./dpub/docIntroductionRole\"));\nvar _docNoterefRole = _interopRequireDefault(require(\"./dpub/docNoterefRole\"));\nvar _docNoticeRole = _interopRequireDefault(require(\"./dpub/docNoticeRole\"));\nvar _docPagebreakRole = _interopRequireDefault(require(\"./dpub/docPagebreakRole\"));\nvar _docPagelistRole = _interopRequireDefault(require(\"./dpub/docPagelistRole\"));\nvar _docPartRole = _interopRequireDefault(require(\"./dpub/docPartRole\"));\nvar _docPrefaceRole = _interopRequireDefault(require(\"./dpub/docPrefaceRole\"));\nvar _docPrologueRole = _interopRequireDefault(require(\"./dpub/docPrologueRole\"));\nvar _docPullquoteRole = _interopRequireDefault(require(\"./dpub/docPullquoteRole\"));\nvar _docQnaRole = _interopRequireDefault(require(\"./dpub/docQnaRole\"));\nvar _docSubtitleRole = _interopRequireDefault(require(\"./dpub/docSubtitleRole\"));\nvar _docTipRole = _interopRequireDefault(require(\"./dpub/docTipRole\"));\nvar _docTocRole = _interopRequireDefault(require(\"./dpub/docTocRole\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar ariaDpubRoles = [['doc-abstract', _docAbstractRole.default], ['doc-acknowledgments', _docAcknowledgmentsRole.default], ['doc-afterword', _docAfterwordRole.default], ['doc-appendix', _docAppendixRole.default], ['doc-backlink', _docBacklinkRole.default], ['doc-biblioentry', _docBiblioentryRole.default], ['doc-bibliography', _docBibliographyRole.default], ['doc-biblioref', _docBibliorefRole.default], ['doc-chapter', _docChapterRole.default], ['doc-colophon', _docColophonRole.default], ['doc-conclusion', _docConclusionRole.default], ['doc-cover', _docCoverRole.default], ['doc-credit', _docCreditRole.default], ['doc-credits', _docCreditsRole.default], ['doc-dedication', _docDedicationRole.default], ['doc-endnote', _docEndnoteRole.default], ['doc-endnotes', _docEndnotesRole.default], ['doc-epigraph', _docEpigraphRole.default], ['doc-epilogue', _docEpilogueRole.default], ['doc-errata', _docErrataRole.default], ['doc-example', _docExampleRole.default], ['doc-footnote', _docFootnoteRole.default], ['doc-foreword', _docForewordRole.default], ['doc-glossary', _docGlossaryRole.default], ['doc-glossref', _docGlossrefRole.default], ['doc-index', _docIndexRole.default], ['doc-introduction', _docIntroductionRole.default], ['doc-noteref', _docNoterefRole.default], ['doc-notice', _docNoticeRole.default], ['doc-pagebreak', _docPagebreakRole.default], ['doc-pagelist', _docPagelistRole.default], ['doc-part', _docPartRole.default], ['doc-preface', _docPrefaceRole.default], ['doc-prologue', _docPrologueRole.default], ['doc-pullquote', _docPullquoteRole.default], ['doc-qna', _docQnaRole.default], ['doc-subtitle', _docSubtitleRole.default], ['doc-tip', _docTipRole.default], ['doc-toc', _docTocRole.default]];\nvar _default = ariaDpubRoles;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,gBAAgB,GAAGC,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAIC,uBAAuB,GAAGF,sBAAsB,CAACC,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAC9F,IAAIE,iBAAiB,GAAGH,sBAAsB,CAACC,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAClF,IAAIG,gBAAgB,GAAGJ,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAII,gBAAgB,GAAGL,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAIK,mBAAmB,GAAGN,sBAAsB,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAAC;AACtF,IAAIM,oBAAoB,GAAGP,sBAAsB,CAACC,OAAO,CAAC,4BAA4B,CAAC,CAAC;AACxF,IAAIO,iBAAiB,GAAGR,sBAAsB,CAACC,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAClF,IAAIQ,eAAe,GAAGT,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC9E,IAAIS,gBAAgB,GAAGV,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAIU,kBAAkB,GAAGX,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AACpF,IAAIW,aAAa,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAC1E,IAAIY,cAAc,GAAGb,sBAAsB,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAC5E,IAAIa,eAAe,GAAGd,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC9E,IAAIc,kBAAkB,GAAGf,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AACpF,IAAIe,eAAe,GAAGhB,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC9E,IAAIgB,gBAAgB,GAAGjB,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAIiB,gBAAgB,GAAGlB,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAIkB,gBAAgB,GAAGnB,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAImB,cAAc,GAAGpB,sBAAsB,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAC5E,IAAIoB,eAAe,GAAGrB,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC9E,IAAIqB,gBAAgB,GAAGtB,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAIsB,gBAAgB,GAAGvB,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAIuB,gBAAgB,GAAGxB,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAIwB,gBAAgB,GAAGzB,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAIyB,aAAa,GAAG1B,sBAAsB,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAC1E,IAAI0B,oBAAoB,GAAG3B,sBAAsB,CAACC,OAAO,CAAC,4BAA4B,CAAC,CAAC;AACxF,IAAI2B,eAAe,GAAG5B,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC9E,IAAI4B,cAAc,GAAG7B,sBAAsB,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAC5E,IAAI6B,iBAAiB,GAAG9B,sBAAsB,CAACC,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAClF,IAAI8B,gBAAgB,GAAG/B,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAI+B,YAAY,GAAGhC,sBAAsB,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACxE,IAAIgC,eAAe,GAAGjC,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC9E,IAAIiC,gBAAgB,GAAGlC,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAIkC,iBAAiB,GAAGnC,sBAAsB,CAACC,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAClF,IAAImC,WAAW,GAAGpC,sBAAsB,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACtE,IAAIoC,gBAAgB,GAAGrC,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAIqC,WAAW,GAAGtC,sBAAsB,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACtE,IAAIsC,WAAW,GAAGvC,sBAAsB,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACtE,SAASD,sBAAsBA,CAACwC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE1C,OAAO,EAAE0C;EAAI,CAAC;AAAE;AAE9F,IAAIE,aAAa,GAAG,CAAC,CAAC,cAAc,EAAE3C,gBAAgB,CAACD,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAEI,uBAAuB,CAACJ,OAAO,CAAC,EAAE,CAAC,eAAe,EAAEK,iBAAiB,CAACL,OAAO,CAAC,EAAE,CAAC,cAAc,EAAEM,gBAAgB,CAACN,OAAO,CAAC,EAAE,CAAC,cAAc,EAAEO,gBAAgB,CAACP,OAAO,CAAC,EAAE,CAAC,iBAAiB,EAAEQ,mBAAmB,CAACR,OAAO,CAAC,EAAE,CAAC,kBAAkB,EAAES,oBAAoB,CAACT,OAAO,CAAC,EAAE,CAAC,eAAe,EAAEU,iBAAiB,CAACV,OAAO,CAAC,EAAE,CAAC,aAAa,EAAEW,eAAe,CAACX,OAAO,CAAC,EAAE,CAAC,cAAc,EAAEY,gBAAgB,CAACZ,OAAO,CAAC,EAAE,CAAC,gBAAgB,EAAEa,kBAAkB,CAACb,OAAO,CAAC,EAAE,CAAC,WAAW,EAAEc,aAAa,CAACd,OAAO,CAAC,EAAE,CAAC,YAAY,EAAEe,cAAc,CAACf,OAAO,CAAC,EAAE,CAAC,aAAa,EAAEgB,eAAe,CAAChB,OAAO,CAAC,EAAE,CAAC,gBAAgB,EAAEiB,kBAAkB,CAACjB,OAAO,CAAC,EAAE,CAAC,aAAa,EAAEkB,eAAe,CAAClB,OAAO,CAAC,EAAE,CAAC,cAAc,EAAEmB,gBAAgB,CAACnB,OAAO,CAAC,EAAE,CAAC,cAAc,EAAEoB,gBAAgB,CAACpB,OAAO,CAAC,EAAE,CAAC,cAAc,EAAEqB,gBAAgB,CAACrB,OAAO,CAAC,EAAE,CAAC,YAAY,EAAEsB,cAAc,CAACtB,OAAO,CAAC,EAAE,CAAC,aAAa,EAAEuB,eAAe,CAACvB,OAAO,CAAC,EAAE,CAAC,cAAc,EAAEwB,gBAAgB,CAACxB,OAAO,CAAC,EAAE,CAAC,cAAc,EAAEyB,gBAAgB,CAACzB,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE0B,gBAAgB,CAAC1B,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE2B,gBAAgB,CAAC3B,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE4B,aAAa,CAAC5B,OAAO,CAAC,EAAE,CAAC,kBAAkB,EAAE6B,oBAAoB,CAAC7B,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE8B,eAAe,CAAC9B,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE+B,cAAc,CAAC/B,OAAO,CAAC,EAAE,CAAC,eAAe,EAAEgC,iBAAiB,CAAChC,OAAO,CAAC,EAAE,CAAC,cAAc,EAAEiC,gBAAgB,CAACjC,OAAO,CAAC,EAAE,CAAC,UAAU,EAAEkC,YAAY,CAAClC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAEmC,eAAe,CAACnC,OAAO,CAAC,EAAE,CAAC,cAAc,EAAEoC,gBAAgB,CAACpC,OAAO,CAAC,EAAE,CAAC,eAAe,EAAEqC,iBAAiB,CAACrC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAEsC,WAAW,CAACtC,OAAO,CAAC,EAAE,CAAC,cAAc,EAAEuC,gBAAgB,CAACvC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAEwC,WAAW,CAACxC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAEyC,WAAW,CAACzC,OAAO,CAAC,CAAC;AAClrD,IAAI6C,QAAQ,GAAGD,aAAa;AAC5B9C,OAAO,CAACE,OAAO,GAAG6C,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}