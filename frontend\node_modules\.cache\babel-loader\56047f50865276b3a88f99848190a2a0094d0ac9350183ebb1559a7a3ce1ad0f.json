{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DOM_KEY_LOCATION = void 0;\n\n/**\n * @internal Do not create/alter this by yourself as this type might be subject to changes.\n */\nlet DOM_KEY_LOCATION;\nexports.DOM_KEY_LOCATION = DOM_KEY_LOCATION;\n(function (DOM_KEY_LOCATION) {\n  DOM_KEY_LOCATION[DOM_KEY_LOCATION[\"STANDARD\"] = 0] = \"STANDARD\";\n  DOM_KEY_LOCATION[DOM_KEY_LOCATION[\"LEFT\"] = 1] = \"LEFT\";\n  DOM_KEY_LOCATION[DOM_KEY_LOCATION[\"RIGHT\"] = 2] = \"RIGHT\";\n  DOM_KEY_LOCATION[DOM_KEY_LOCATION[\"NUMPAD\"] = 3] = \"NUMPAD\";\n})(DOM_KEY_LOCATION || (exports.DOM_KEY_LOCATION = DOM_KEY_LOCATION = {}));", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "DOM_KEY_LOCATION"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/user-event/dist/keyboard/types.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DOM_KEY_LOCATION = void 0;\n\n/**\n * @internal Do not create/alter this by yourself as this type might be subject to changes.\n */\nlet DOM_KEY_LOCATION;\nexports.DOM_KEY_LOCATION = DOM_KEY_LOCATION;\n\n(function (DOM_KEY_LOCATION) {\n  DOM_KEY_LOCATION[DOM_KEY_LOCATION[\"STANDARD\"] = 0] = \"STANDARD\";\n  DOM_KEY_LOCATION[DOM_KEY_LOCATION[\"LEFT\"] = 1] = \"LEFT\";\n  DOM_KEY_LOCATION[DOM_KEY_LOCATION[\"RIGHT\"] = 2] = \"RIGHT\";\n  DOM_KEY_LOCATION[DOM_KEY_LOCATION[\"NUMPAD\"] = 3] = \"NUMPAD\";\n})(DOM_KEY_LOCATION || (exports.DOM_KEY_LOCATION = DOM_KEY_LOCATION = {}));"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,gBAAgB,GAAG,KAAK,CAAC;;AAEjC;AACA;AACA;AACA,IAAIA,gBAAgB;AACpBF,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB;AAE3C,CAAC,UAAUA,gBAAgB,EAAE;EAC3BA,gBAAgB,CAACA,gBAAgB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EAC/DA,gBAAgB,CAACA,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACvDA,gBAAgB,CAACA,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACzDA,gBAAgB,CAACA,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AAC7D,CAAC,EAAEA,gBAAgB,KAAKF,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}