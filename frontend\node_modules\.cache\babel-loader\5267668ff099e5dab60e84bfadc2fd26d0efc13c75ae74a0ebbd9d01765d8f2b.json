{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\IdeaProjects\\\\FoodOrdering-main\\\\FoodOrdering-main\\\\frontend\\\\src\\\\Components\\\\Navbar\\\\Navbar.jsx\",\n  _s = $RefreshSig$();\nimport { Avatar, Badge, IconButton } from \"@mui/material\";\nimport React from \"react\";\nimport SearchIcon from \"@mui/icons-material/Search\";\nimport { green } from \"@mui/material/colors\";\nimport ShoppingCartIcon from \"@mui/icons-material/ShoppingCart\";\nimport \"./Navbar.css\";\nimport { Person } from \"@mui/icons-material\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useSelector } from \"react-redux\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Navbar = () => {\n  _s();\n  var _auth$user2, _card$card;\n  const {\n    auth,\n    card\n  } = useSelector(store => store);\n  const navigate = useNavigate();\n  const handleAvatarClick = () => {\n    var _auth$user;\n    if (((_auth$user = auth.user) === null || _auth$user === void 0 ? void 0 : _auth$user.role) === \"RESTAURANT_OWNER\") {\n      navigate(\"/admin/restaurant\");\n    } else {\n      navigate(\"/my-account\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"px-5 z-50 py-[.8rem] bg-[#93c47d] lg:px-20 flex justify-between\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:mr-10 cursor-pointer flex items-center space-x-4\",\n      children: /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => navigate(\"/\"),\n        className: \"logo font-semibold text-gray-300 text-2xl\",\n        children: \"Zashopp Food\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 lg:space-x-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n            sx: {\n              fontSize: \"1.5rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"\",\n        children: auth.user ? /*#__PURE__*/_jsxDEV(Avatar, {\n          onClick: handleAvatarClick,\n          sx: {\n            bgcolor: \"white\",\n            color: green.A200\n          },\n          children: (_auth$user2 = auth.user) === null || _auth$user2 === void 0 ? void 0 : _auth$user2.fullName[0].toUpperCase()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => navigate(\"/account/login\"),\n          children: /*#__PURE__*/_jsxDEV(Person, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => navigate(\"/card\"),\n          children: /*#__PURE__*/_jsxDEV(Badge, {\n            color: \"secondary\",\n            badgeContent: (_card$card = card.card) === null || _card$card === void 0 ? void 0 : _card$card.items.length,\n            children: /*#__PURE__*/_jsxDEV(ShoppingCartIcon, {\n              sx: {\n                fontSize: \"1.5rem\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"qjoxUaQKKJgK99FroWZZ8DoMqiY=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["Avatar", "Badge", "IconButton", "React", "SearchIcon", "green", "ShoppingCartIcon", "Person", "useNavigate", "useSelector", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "_auth$user2", "_card$card", "auth", "card", "store", "navigate", "handleAvatarClick", "_auth$user", "user", "role", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "fontSize", "bgcolor", "color", "A200", "fullName", "toUpperCase", "badgeContent", "items", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/src/Components/Navbar/Navbar.jsx"], "sourcesContent": ["import { Ava<PERSON>, Badge, IconButton } from \"@mui/material\";\nimport React from \"react\";\nimport SearchIcon from \"@mui/icons-material/Search\";\nimport { green } from \"@mui/material/colors\";\nimport ShoppingCartIcon from \"@mui/icons-material/ShoppingCart\";\nimport \"./Navbar.css\";\nimport { Person } from \"@mui/icons-material\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useSelector } from \"react-redux\";\n\n\nexport const Navbar = () => {\n  const {auth,card} = useSelector(store => store);\n  const navigate = useNavigate();\n\n  const handleAvatarClick=()=> {\n    if(auth.user?.role === \"RESTAURANT_OWNER\"){\n      navigate(\"/admin/restaurant\")\n    }else{\n      navigate(\"/my-account\")\n    }\n  }\n  return (\n    <div className=\"px-5 z-50 py-[.8rem] bg-[#93c47d] lg:px-20 flex justify-between\">\n      <div className=\"lg:mr-10 cursor-pointer flex items-center space-x-4\">\n        <li onClick={()=>navigate(\"/\")} className=\"logo font-semibold text-gray-300 text-2xl\">\n          Zashopp Food\n        </li>\n      </div>\n\n      <div className=\"flex items-center space-x-2 lg:space-x-10\">\n        <div className=\"\">\n          <IconButton>\n            <SearchIcon sx={{ fontSize: \"1.5rem\" }} />\n          </IconButton>\n        </div>\n\n        <div className=\"\">\n          {auth.user ? (\n            <Avatar onClick={handleAvatarClick} sx={{ bgcolor: \"white\", color: green.A200 }}>{auth.user?.fullName[0].toUpperCase()}</Avatar>\n          ) : (\n            <IconButton onClick={()=> navigate(\"/account/login\")}>\n              <Person />\n            </IconButton>\n          )}\n        </div>\n\n        <div className=\"\">\n          <IconButton onClick={()=> navigate(\"/card\")}>\n            <Badge color=\"secondary\" badgeContent={card.card?.items.length}>\n              <ShoppingCartIcon sx={{ fontSize: \"1.5rem\" }} />\n            </Badge>\n          </IconButton>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,SAASA,MAAM,EAAEC,KAAK,EAAEC,UAAU,QAAQ,eAAe;AACzD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,4BAA4B;AACnD,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAO,cAAc;AACrB,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1C,OAAO,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,WAAA,EAAAC,UAAA;EAC1B,MAAM;IAACC,IAAI;IAACC;EAAI,CAAC,GAAGR,WAAW,CAACS,KAAK,IAAIA,KAAK,CAAC;EAC/C,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,iBAAiB,GAACA,CAAA,KAAK;IAAA,IAAAC,UAAA;IAC3B,IAAG,EAAAA,UAAA,GAAAL,IAAI,CAACM,IAAI,cAAAD,UAAA,uBAATA,UAAA,CAAWE,IAAI,MAAK,kBAAkB,EAAC;MACxCJ,QAAQ,CAAC,mBAAmB,CAAC;IAC/B,CAAC,MAAI;MACHA,QAAQ,CAAC,aAAa,CAAC;IACzB;EACF,CAAC;EACD,oBACER,OAAA;IAAKa,SAAS,EAAC,iEAAiE;IAAAC,QAAA,gBAC9Ed,OAAA;MAAKa,SAAS,EAAC,qDAAqD;MAAAC,QAAA,eAClEd,OAAA;QAAIe,OAAO,EAAEA,CAAA,KAAIP,QAAQ,CAAC,GAAG,CAAE;QAACK,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAEtF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAENnB,OAAA;MAAKa,SAAS,EAAC,2CAA2C;MAAAC,QAAA,gBACxDd,OAAA;QAAKa,SAAS,EAAC,EAAE;QAAAC,QAAA,eACfd,OAAA,CAACT,UAAU;UAAAuB,QAAA,eACTd,OAAA,CAACP,UAAU;YAAC2B,EAAE,EAAE;cAAEC,QAAQ,EAAE;YAAS;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENnB,OAAA;QAAKa,SAAS,EAAC,EAAE;QAAAC,QAAA,EACdT,IAAI,CAACM,IAAI,gBACRX,OAAA,CAACX,MAAM;UAAC0B,OAAO,EAAEN,iBAAkB;UAACW,EAAE,EAAE;YAAEE,OAAO,EAAE,OAAO;YAAEC,KAAK,EAAE7B,KAAK,CAAC8B;UAAK,CAAE;UAAAV,QAAA,GAAAX,WAAA,GAAEE,IAAI,CAACM,IAAI,cAAAR,WAAA,uBAATA,WAAA,CAAWsB,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;QAAC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,gBAEhInB,OAAA,CAACT,UAAU;UAACwB,OAAO,EAAEA,CAAA,KAAKP,QAAQ,CAAC,gBAAgB,CAAE;UAAAM,QAAA,eACnDd,OAAA,CAACJ,MAAM;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnB,OAAA;QAAKa,SAAS,EAAC,EAAE;QAAAC,QAAA,eACfd,OAAA,CAACT,UAAU;UAACwB,OAAO,EAAEA,CAAA,KAAKP,QAAQ,CAAC,OAAO,CAAE;UAAAM,QAAA,eAC1Cd,OAAA,CAACV,KAAK;YAACiC,KAAK,EAAC,WAAW;YAACI,YAAY,GAAAvB,UAAA,GAAEE,IAAI,CAACA,IAAI,cAAAF,UAAA,uBAATA,UAAA,CAAWwB,KAAK,CAACC,MAAO;YAAAf,QAAA,eAC7Dd,OAAA,CAACL,gBAAgB;cAACyB,EAAE,EAAE;gBAAEC,QAAQ,EAAE;cAAS;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CA9CWD,MAAM;EAAA,QACGH,WAAW,EACdD,WAAW;AAAA;AAAAiC,EAAA,GAFjB7B,MAAM;AAgDnB,eAAeA,MAAM;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}