{"ast": null, "code": "var QueryHandler = require('./QueryHandler');\nvar each = require('./Util').each;\n\n/**\n * Represents a single media query, manages it's state and registered handlers for this query\n *\n * @constructor\n * @param {string} query the media query string\n * @param {boolean} [isUnconditional=false] whether the media query should run regardless of whether the conditions are met. Primarily for helping older browsers deal with mobile-first design\n */\nfunction MediaQuery(query, isUnconditional) {\n  this.query = query;\n  this.isUnconditional = isUnconditional;\n  this.handlers = [];\n  this.mql = window.matchMedia(query);\n  var self = this;\n  this.listener = function (mql) {\n    // Chrome passes an MediaQueryListEvent object, while other browsers pass MediaQueryList directly\n    self.mql = mql.currentTarget || mql;\n    self.assess();\n  };\n  this.mql.addListener(this.listener);\n}\nMediaQuery.prototype = {\n  constuctor: MediaQuery,\n  /**\n   * add a handler for this query, triggering if already active\n   *\n   * @param {object} handler\n   * @param {function} handler.match callback for when query is activated\n   * @param {function} [handler.unmatch] callback for when query is deactivated\n   * @param {function} [handler.setup] callback for immediate execution when a query handler is registered\n   * @param {boolean} [handler.deferSetup=false] should the setup callback be deferred until the first time the handler is matched?\n   */\n  addHandler: function (handler) {\n    var qh = new QueryHandler(handler);\n    this.handlers.push(qh);\n    this.matches() && qh.on();\n  },\n  /**\n   * removes the given handler from the collection, and calls it's destroy methods\n   *\n   * @param {object || function} handler the handler to remove\n   */\n  removeHandler: function (handler) {\n    var handlers = this.handlers;\n    each(handlers, function (h, i) {\n      if (h.equals(handler)) {\n        h.destroy();\n        return !handlers.splice(i, 1); //remove from array and exit each early\n      }\n    });\n  },\n  /**\n   * Determine whether the media query should be considered a match\n   *\n   * @return {Boolean} true if media query can be considered a match, false otherwise\n   */\n  matches: function () {\n    return this.mql.matches || this.isUnconditional;\n  },\n  /**\n   * Clears all handlers and unbinds events\n   */\n  clear: function () {\n    each(this.handlers, function (handler) {\n      handler.destroy();\n    });\n    this.mql.removeListener(this.listener);\n    this.handlers.length = 0; //clear array\n  },\n  /*\n      * Assesses the query, turning on all handlers if it matches, turning them off if it doesn't match\n      */\n  assess: function () {\n    var action = this.matches() ? 'on' : 'off';\n    each(this.handlers, function (handler) {\n      handler[action]();\n    });\n  }\n};\nmodule.exports = MediaQuery;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "each", "MediaQuery", "query", "isUnconditional", "handlers", "mql", "window", "matchMedia", "self", "listener", "currentTarget", "assess", "addListener", "prototype", "constuctor", "add<PERSON><PERSON><PERSON>", "handler", "qh", "push", "matches", "on", "<PERSON><PERSON><PERSON><PERSON>", "h", "i", "equals", "destroy", "splice", "clear", "removeListener", "length", "action", "module", "exports"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/enquire.js/src/MediaQuery.js"], "sourcesContent": ["var QueryHandler = require('./QueryHandler');\nvar each = require('./Util').each;\n\n/**\n * Represents a single media query, manages it's state and registered handlers for this query\n *\n * @constructor\n * @param {string} query the media query string\n * @param {boolean} [isUnconditional=false] whether the media query should run regardless of whether the conditions are met. Primarily for helping older browsers deal with mobile-first design\n */\nfunction MediaQuery(query, isUnconditional) {\n    this.query = query;\n    this.isUnconditional = isUnconditional;\n    this.handlers = [];\n    this.mql = window.matchMedia(query);\n\n    var self = this;\n    this.listener = function(mql) {\n        // Chrome passes an MediaQueryListEvent object, while other browsers pass MediaQueryList directly\n        self.mql = mql.currentTarget || mql;\n        self.assess();\n    };\n    this.mql.addListener(this.listener);\n}\n\nMediaQuery.prototype = {\n\n    constuctor : MediaQuery,\n\n    /**\n     * add a handler for this query, triggering if already active\n     *\n     * @param {object} handler\n     * @param {function} handler.match callback for when query is activated\n     * @param {function} [handler.unmatch] callback for when query is deactivated\n     * @param {function} [handler.setup] callback for immediate execution when a query handler is registered\n     * @param {boolean} [handler.deferSetup=false] should the setup callback be deferred until the first time the handler is matched?\n     */\n    addHandler : function(handler) {\n        var qh = new QueryHandler(handler);\n        this.handlers.push(qh);\n\n        this.matches() && qh.on();\n    },\n\n    /**\n     * removes the given handler from the collection, and calls it's destroy methods\n     *\n     * @param {object || function} handler the handler to remove\n     */\n    removeHandler : function(handler) {\n        var handlers = this.handlers;\n        each(handlers, function(h, i) {\n            if(h.equals(handler)) {\n                h.destroy();\n                return !handlers.splice(i,1); //remove from array and exit each early\n            }\n        });\n    },\n\n    /**\n     * Determine whether the media query should be considered a match\n     *\n     * @return {Boolean} true if media query can be considered a match, false otherwise\n     */\n    matches : function() {\n        return this.mql.matches || this.isUnconditional;\n    },\n\n    /**\n     * Clears all handlers and unbinds events\n     */\n    clear : function() {\n        each(this.handlers, function(handler) {\n            handler.destroy();\n        });\n        this.mql.removeListener(this.listener);\n        this.handlers.length = 0; //clear array\n    },\n\n    /*\n        * Assesses the query, turning on all handlers if it matches, turning them off if it doesn't match\n        */\n    assess : function() {\n        var action = this.matches() ? 'on' : 'off';\n\n        each(this.handlers, function(handler) {\n            handler[action]();\n        });\n    }\n};\n\nmodule.exports = MediaQuery;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAC5C,IAAIC,IAAI,GAAGD,OAAO,CAAC,QAAQ,CAAC,CAACC,IAAI;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAEC,eAAe,EAAE;EACxC,IAAI,CAACD,KAAK,GAAGA,KAAK;EAClB,IAAI,CAACC,eAAe,GAAGA,eAAe;EACtC,IAAI,CAACC,QAAQ,GAAG,EAAE;EAClB,IAAI,CAACC,GAAG,GAAGC,MAAM,CAACC,UAAU,CAACL,KAAK,CAAC;EAEnC,IAAIM,IAAI,GAAG,IAAI;EACf,IAAI,CAACC,QAAQ,GAAG,UAASJ,GAAG,EAAE;IAC1B;IACAG,IAAI,CAACH,GAAG,GAAGA,GAAG,CAACK,aAAa,IAAIL,GAAG;IACnCG,IAAI,CAACG,MAAM,CAAC,CAAC;EACjB,CAAC;EACD,IAAI,CAACN,GAAG,CAACO,WAAW,CAAC,IAAI,CAACH,QAAQ,CAAC;AACvC;AAEAR,UAAU,CAACY,SAAS,GAAG;EAEnBC,UAAU,EAAGb,UAAU;EAEvB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIc,UAAU,EAAG,SAAAA,CAASC,OAAO,EAAE;IAC3B,IAAIC,EAAE,GAAG,IAAInB,YAAY,CAACkB,OAAO,CAAC;IAClC,IAAI,CAACZ,QAAQ,CAACc,IAAI,CAACD,EAAE,CAAC;IAEtB,IAAI,CAACE,OAAO,CAAC,CAAC,IAAIF,EAAE,CAACG,EAAE,CAAC,CAAC;EAC7B,CAAC;EAED;AACJ;AACA;AACA;AACA;EACIC,aAAa,EAAG,SAAAA,CAASL,OAAO,EAAE;IAC9B,IAAIZ,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5BJ,IAAI,CAACI,QAAQ,EAAE,UAASkB,CAAC,EAAEC,CAAC,EAAE;MAC1B,IAAGD,CAAC,CAACE,MAAM,CAACR,OAAO,CAAC,EAAE;QAClBM,CAAC,CAACG,OAAO,CAAC,CAAC;QACX,OAAO,CAACrB,QAAQ,CAACsB,MAAM,CAACH,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC;MAClC;IACJ,CAAC,CAAC;EACN,CAAC;EAED;AACJ;AACA;AACA;AACA;EACIJ,OAAO,EAAG,SAAAA,CAAA,EAAW;IACjB,OAAO,IAAI,CAACd,GAAG,CAACc,OAAO,IAAI,IAAI,CAAChB,eAAe;EACnD,CAAC;EAED;AACJ;AACA;EACIwB,KAAK,EAAG,SAAAA,CAAA,EAAW;IACf3B,IAAI,CAAC,IAAI,CAACI,QAAQ,EAAE,UAASY,OAAO,EAAE;MAClCA,OAAO,CAACS,OAAO,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,IAAI,CAACpB,GAAG,CAACuB,cAAc,CAAC,IAAI,CAACnB,QAAQ,CAAC;IACtC,IAAI,CAACL,QAAQ,CAACyB,MAAM,GAAG,CAAC,CAAC,CAAC;EAC9B,CAAC;EAED;AACJ;AACA;EACIlB,MAAM,EAAG,SAAAA,CAAA,EAAW;IAChB,IAAImB,MAAM,GAAG,IAAI,CAACX,OAAO,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK;IAE1CnB,IAAI,CAAC,IAAI,CAACI,QAAQ,EAAE,UAASY,OAAO,EAAE;MAClCA,OAAO,CAACc,MAAM,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC;EACN;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG/B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}