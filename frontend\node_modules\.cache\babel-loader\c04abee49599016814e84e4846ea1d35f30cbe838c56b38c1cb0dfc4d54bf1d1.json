{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useDateTimePickerDefaultizedProps } from \"../DateTimePicker/shared.js\";\nimport { renderTimeViewClock } from \"../timeViewRenderers/index.js\";\nimport { renderDateViewCalendar } from \"../dateViewRenderers/index.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { useStaticPicker } from \"../internals/hooks/useStaticPicker/index.js\";\nimport { validateDateTime } from \"../validation/index.js\";\n/**\n * Demos:\n *\n * - [DateTimePicker](https://mui.com/x/react-date-pickers/date-time-picker/)\n * - [Validation](https://mui.com/x/react-date-pickers/validation/)\n *\n * API:\n *\n * - [StaticDateTimePicker API](https://mui.com/x/api/date-pickers/static-date-time-picker/)\n */\nconst StaticDateTimePicker = /*#__PURE__*/React.forwardRef(function StaticDateTimePicker(inProps, ref) {\n  const defaultizedProps = useDateTimePickerDefaultizedProps(inProps, 'MuiStaticDateTimePicker');\n  const displayStaticWrapperAs = defaultizedProps.displayStaticWrapperAs ?? 'mobile';\n  const ampmInClock = defaultizedProps.ampmInClock ?? displayStaticWrapperAs === 'desktop';\n  const viewRenderers = _extends({\n    day: renderDateViewCalendar,\n    month: renderDateViewCalendar,\n    year: renderDateViewCalendar,\n    hours: renderTimeViewClock,\n    minutes: renderTimeViewClock,\n    seconds: renderTimeViewClock\n  }, defaultizedProps.viewRenderers);\n\n  // Props with the default values specific to the static variant\n  const props = _extends({}, defaultizedProps, {\n    viewRenderers,\n    displayStaticWrapperAs,\n    ampmInClock,\n    yearsPerRow: defaultizedProps.yearsPerRow ?? (displayStaticWrapperAs === 'mobile' ? 3 : 4),\n    slotProps: _extends({}, defaultizedProps.slotProps, {\n      tabs: _extends({\n        hidden: displayStaticWrapperAs === 'desktop'\n      }, defaultizedProps.slotProps?.tabs),\n      toolbar: _extends({\n        hidden: displayStaticWrapperAs === 'desktop',\n        ampmInClock\n      }, defaultizedProps.slotProps?.toolbar)\n    })\n  });\n  const {\n    renderPicker\n  } = useStaticPicker({\n    props,\n    valueManager: singleItemValueManager,\n    valueType: 'date-time',\n    validator: validateDateTime,\n    ref\n  });\n  return renderPicker();\n});\nStaticDateTimePicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * Display ampm controls under the clock (instead of in the toolbar).\n   * @default true on desktop, false on mobile\n   */\n  ampmInClock: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  className: PropTypes.string,\n  /**\n   * Formats the day of week displayed in the calendar header.\n   * @param {TDate} date The date of the day of week provided by the adapter.\n   * @returns {string} The name to display.\n   * @default (date: TDate) => adapter.format(date, 'weekdayShort').charAt(0).toUpperCase()\n   */\n  dayOfWeekFormatter: PropTypes.func,\n  /**\n   * The default value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the picker and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * Force static wrapper inner components to be rendered in mobile or desktop mode.\n   * @default \"mobile\"\n   */\n  displayStaticWrapperAs: PropTypes.oneOf(['desktop', 'mobile']),\n  /**\n   * If `true`, the week number will be display in the calendar.\n   */\n  displayWeekNumber: PropTypes.bool,\n  /**\n   * The day view will show as many weeks as needed after the end of the current month to match this value.\n   * Put it to 6 to have a fixed number of weeks in Gregorian calendars\n   */\n  fixedWeekNumber: PropTypes.number,\n  /**\n   * If `true`, calls `renderLoading` instead of rendering the day calendar.\n   * Can be used to preload information and show it in calendar.\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Locale for components texts.\n   * Allows overriding texts coming from `LocalizationProvider` and `theme`.\n   */\n  localeText: PropTypes.object,\n  /**\n   * Maximal selectable date.\n   * @default 2099-12-31\n   */\n  maxDate: PropTypes.object,\n  /**\n   * Maximal selectable moment of time with binding to date, to set max time in each day use `maxTime`.\n   */\n  maxDateTime: PropTypes.object,\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable date.\n   * @default 1900-01-01\n   */\n  minDate: PropTypes.object,\n  /**\n   * Minimal selectable moment of time with binding to date, to set min time in each day use `minTime`.\n   */\n  minDateTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Months rendered per row.\n   * @default 3\n   */\n  monthsPerRow: PropTypes.oneOf([3, 4]),\n  /**\n   * Callback fired when the value is accepted.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The value that was just accepted.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onAccept: PropTypes.func,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The new value.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when component requests to be closed.\n   * Can be fired when selecting (by default on `desktop` mode) or clearing a value.\n   * @deprecated Please avoid using as it will be removed in next major version.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the error associated with the current value changes.\n   * When a validation error is detected, the `error` parameter contains a non-null value.\n   * This can be used to render an appropriate form error.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TError} error The reason why the current value is not valid.\n   * @param {TValue} value The value associated with the error.\n   */\n  onError: PropTypes.func,\n  /**\n   * Callback fired on month change.\n   * @template TDate\n   * @param {TDate} month The new month.\n   */\n  onMonthChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * Callback fired on year change.\n   * @template TDate\n   * @param {TDate} year The new year.\n   */\n  onYearChange: PropTypes.func,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['day', 'hours', 'minutes', 'month', 'seconds', 'year']),\n  /**\n   * Force rendering in particular orientation.\n   */\n  orientation: PropTypes.oneOf(['landscape', 'portrait']),\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, disable heavy animations.\n   * @default `@media(prefers-reduced-motion: reduce)` || `navigator.userAgent` matches Android <10 or iOS <13\n   */\n  reduceAnimations: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid date-time using the validation props, except callbacks like `shouldDisable<...>`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Component displaying when passed `loading` true.\n   * @returns {React.ReactNode} The node to render when loading.\n   * @default () => <span data-mui-test=\"loading-progress\">...</span>\n   */\n  renderLoading: PropTypes.func,\n  /**\n   * Disable specific date.\n   *\n   * Warning: This function can be called multiple times (for example when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.\n   *\n   * @template TDate\n   * @param {TDate} day The date to test.\n   * @returns {boolean} If `true` the date will be disabled.\n   */\n  shouldDisableDate: PropTypes.func,\n  /**\n   * Disable specific month.\n   * @template TDate\n   * @param {TDate} month The month to test.\n   * @returns {boolean} If `true`, the month will be disabled.\n   */\n  shouldDisableMonth: PropTypes.func,\n  /**\n   * Disable specific time.\n   * @template TDate\n   * @param {TDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  /**\n   * Disable specific year.\n   * @template TDate\n   * @param {TDate} year The year to test.\n   * @returns {boolean} If `true`, the year will be disabled.\n   */\n  shouldDisableYear: PropTypes.func,\n  /**\n   * If `true`, days outside the current month are rendered:\n   *\n   * - if `fixedWeekNumber` is defined, renders days to have the weeks requested.\n   *\n   * - if `fixedWeekNumber` is not defined, renders day to fill the first and last week of the current month.\n   *\n   * - ignored if `calendars` equals more than `1` on range pickers.\n   * @default false\n   */\n  showDaysOutsideCurrentMonth: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['day', 'hours', 'minutes', 'month', 'seconds', 'year']),\n  /**\n   * Define custom view renderers for each section.\n   * If `null`, the section will only have field editing.\n   * If `undefined`, internally defined view will be used.\n   */\n  viewRenderers: PropTypes.shape({\n    day: PropTypes.func,\n    hours: PropTypes.func,\n    minutes: PropTypes.func,\n    month: PropTypes.func,\n    seconds: PropTypes.func,\n    year: PropTypes.func\n  }),\n  /**\n   * Available views.\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'hours', 'minutes', 'month', 'seconds', 'year']).isRequired),\n  /**\n   * Years are displayed in ascending (chronological) order by default.\n   * If `desc`, years are displayed in descending order.\n   * @default 'asc'\n   */\n  yearsOrder: PropTypes.oneOf(['asc', 'desc']),\n  /**\n   * Years rendered per row.\n   * @default `4` when `displayStaticWrapperAs === 'desktop'`, `3` otherwise.\n   */\n  yearsPerRow: PropTypes.oneOf([3, 4])\n};\nexport { StaticDateTimePicker };", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "useDateTimePickerDefaultizedProps", "renderTimeViewClock", "renderDateViewCalendar", "singleItemValueManager", "useStaticPicker", "validateDateTime", "StaticDateTimePicker", "forwardRef", "inProps", "ref", "defaultizedProps", "displayStaticWrapperAs", "ampmInClock", "viewRenderers", "day", "month", "year", "hours", "minutes", "seconds", "props", "yearsPerRow", "slotProps", "tabs", "hidden", "toolbar", "renderPicker", "valueManager", "valueType", "validator", "propTypes", "ampm", "bool", "autoFocus", "className", "string", "dayOfWeekFormatter", "func", "defaultValue", "object", "disabled", "disableFuture", "disableHighlightToday", "disableIgnoringDatePartForTimeValidation", "disablePast", "oneOf", "displayWeekNumber", "fixedWeekNumber", "number", "loading", "localeText", "maxDate", "maxDateTime", "maxTime", "minDate", "minDateTime", "minTime", "minutesStep", "monthsPerRow", "onAccept", "onChange", "onClose", "onError", "onMonthChange", "onViewChange", "onYearChange", "openTo", "orientation", "readOnly", "reduceAnimations", "referenceDate", "renderLoading", "shouldDisableDate", "shouldDisableMonth", "shouldDisableTime", "shouldDisableYear", "showDaysOutsideCurrentMonth", "slots", "sx", "oneOfType", "arrayOf", "timezone", "value", "view", "shape", "views", "isRequired", "yearsOrder"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/StaticDateTimePicker/StaticDateTimePicker.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useDateTimePickerDefaultizedProps } from \"../DateTimePicker/shared.js\";\nimport { renderTimeViewClock } from \"../timeViewRenderers/index.js\";\nimport { renderDateViewCalendar } from \"../dateViewRenderers/index.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { useStaticPicker } from \"../internals/hooks/useStaticPicker/index.js\";\nimport { validateDateTime } from \"../validation/index.js\";\n/**\n * Demos:\n *\n * - [DateTimePicker](https://mui.com/x/react-date-pickers/date-time-picker/)\n * - [Validation](https://mui.com/x/react-date-pickers/validation/)\n *\n * API:\n *\n * - [StaticDateTimePicker API](https://mui.com/x/api/date-pickers/static-date-time-picker/)\n */\nconst StaticDateTimePicker = /*#__PURE__*/React.forwardRef(function StaticDateTimePicker(inProps, ref) {\n  const defaultizedProps = useDateTimePickerDefaultizedProps(inProps, 'MuiStaticDateTimePicker');\n  const displayStaticWrapperAs = defaultizedProps.displayStaticWrapperAs ?? 'mobile';\n  const ampmInClock = defaultizedProps.ampmInClock ?? displayStaticWrapperAs === 'desktop';\n  const viewRenderers = _extends({\n    day: renderDateViewCalendar,\n    month: renderDateViewCalendar,\n    year: renderDateViewCalendar,\n    hours: renderTimeViewClock,\n    minutes: renderTimeViewClock,\n    seconds: renderTimeViewClock\n  }, defaultizedProps.viewRenderers);\n\n  // Props with the default values specific to the static variant\n  const props = _extends({}, defaultizedProps, {\n    viewRenderers,\n    displayStaticWrapperAs,\n    ampmInClock,\n    yearsPerRow: defaultizedProps.yearsPerRow ?? (displayStaticWrapperAs === 'mobile' ? 3 : 4),\n    slotProps: _extends({}, defaultizedProps.slotProps, {\n      tabs: _extends({\n        hidden: displayStaticWrapperAs === 'desktop'\n      }, defaultizedProps.slotProps?.tabs),\n      toolbar: _extends({\n        hidden: displayStaticWrapperAs === 'desktop',\n        ampmInClock\n      }, defaultizedProps.slotProps?.toolbar)\n    })\n  });\n  const {\n    renderPicker\n  } = useStaticPicker({\n    props,\n    valueManager: singleItemValueManager,\n    valueType: 'date-time',\n    validator: validateDateTime,\n    ref\n  });\n  return renderPicker();\n});\nStaticDateTimePicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * Display ampm controls under the clock (instead of in the toolbar).\n   * @default true on desktop, false on mobile\n   */\n  ampmInClock: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  className: PropTypes.string,\n  /**\n   * Formats the day of week displayed in the calendar header.\n   * @param {TDate} date The date of the day of week provided by the adapter.\n   * @returns {string} The name to display.\n   * @default (date: TDate) => adapter.format(date, 'weekdayShort').charAt(0).toUpperCase()\n   */\n  dayOfWeekFormatter: PropTypes.func,\n  /**\n   * The default value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the picker and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * Force static wrapper inner components to be rendered in mobile or desktop mode.\n   * @default \"mobile\"\n   */\n  displayStaticWrapperAs: PropTypes.oneOf(['desktop', 'mobile']),\n  /**\n   * If `true`, the week number will be display in the calendar.\n   */\n  displayWeekNumber: PropTypes.bool,\n  /**\n   * The day view will show as many weeks as needed after the end of the current month to match this value.\n   * Put it to 6 to have a fixed number of weeks in Gregorian calendars\n   */\n  fixedWeekNumber: PropTypes.number,\n  /**\n   * If `true`, calls `renderLoading` instead of rendering the day calendar.\n   * Can be used to preload information and show it in calendar.\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Locale for components texts.\n   * Allows overriding texts coming from `LocalizationProvider` and `theme`.\n   */\n  localeText: PropTypes.object,\n  /**\n   * Maximal selectable date.\n   * @default 2099-12-31\n   */\n  maxDate: PropTypes.object,\n  /**\n   * Maximal selectable moment of time with binding to date, to set max time in each day use `maxTime`.\n   */\n  maxDateTime: PropTypes.object,\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable date.\n   * @default 1900-01-01\n   */\n  minDate: PropTypes.object,\n  /**\n   * Minimal selectable moment of time with binding to date, to set min time in each day use `minTime`.\n   */\n  minDateTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Months rendered per row.\n   * @default 3\n   */\n  monthsPerRow: PropTypes.oneOf([3, 4]),\n  /**\n   * Callback fired when the value is accepted.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The value that was just accepted.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onAccept: PropTypes.func,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The new value.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when component requests to be closed.\n   * Can be fired when selecting (by default on `desktop` mode) or clearing a value.\n   * @deprecated Please avoid using as it will be removed in next major version.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the error associated with the current value changes.\n   * When a validation error is detected, the `error` parameter contains a non-null value.\n   * This can be used to render an appropriate form error.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TError} error The reason why the current value is not valid.\n   * @param {TValue} value The value associated with the error.\n   */\n  onError: PropTypes.func,\n  /**\n   * Callback fired on month change.\n   * @template TDate\n   * @param {TDate} month The new month.\n   */\n  onMonthChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * Callback fired on year change.\n   * @template TDate\n   * @param {TDate} year The new year.\n   */\n  onYearChange: PropTypes.func,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['day', 'hours', 'minutes', 'month', 'seconds', 'year']),\n  /**\n   * Force rendering in particular orientation.\n   */\n  orientation: PropTypes.oneOf(['landscape', 'portrait']),\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, disable heavy animations.\n   * @default `@media(prefers-reduced-motion: reduce)` || `navigator.userAgent` matches Android <10 or iOS <13\n   */\n  reduceAnimations: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid date-time using the validation props, except callbacks like `shouldDisable<...>`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Component displaying when passed `loading` true.\n   * @returns {React.ReactNode} The node to render when loading.\n   * @default () => <span data-mui-test=\"loading-progress\">...</span>\n   */\n  renderLoading: PropTypes.func,\n  /**\n   * Disable specific date.\n   *\n   * Warning: This function can be called multiple times (for example when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.\n   *\n   * @template TDate\n   * @param {TDate} day The date to test.\n   * @returns {boolean} If `true` the date will be disabled.\n   */\n  shouldDisableDate: PropTypes.func,\n  /**\n   * Disable specific month.\n   * @template TDate\n   * @param {TDate} month The month to test.\n   * @returns {boolean} If `true`, the month will be disabled.\n   */\n  shouldDisableMonth: PropTypes.func,\n  /**\n   * Disable specific time.\n   * @template TDate\n   * @param {TDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  /**\n   * Disable specific year.\n   * @template TDate\n   * @param {TDate} year The year to test.\n   * @returns {boolean} If `true`, the year will be disabled.\n   */\n  shouldDisableYear: PropTypes.func,\n  /**\n   * If `true`, days outside the current month are rendered:\n   *\n   * - if `fixedWeekNumber` is defined, renders days to have the weeks requested.\n   *\n   * - if `fixedWeekNumber` is not defined, renders day to fill the first and last week of the current month.\n   *\n   * - ignored if `calendars` equals more than `1` on range pickers.\n   * @default false\n   */\n  showDaysOutsideCurrentMonth: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['day', 'hours', 'minutes', 'month', 'seconds', 'year']),\n  /**\n   * Define custom view renderers for each section.\n   * If `null`, the section will only have field editing.\n   * If `undefined`, internally defined view will be used.\n   */\n  viewRenderers: PropTypes.shape({\n    day: PropTypes.func,\n    hours: PropTypes.func,\n    minutes: PropTypes.func,\n    month: PropTypes.func,\n    seconds: PropTypes.func,\n    year: PropTypes.func\n  }),\n  /**\n   * Available views.\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'hours', 'minutes', 'month', 'seconds', 'year']).isRequired),\n  /**\n   * Years are displayed in ascending (chronological) order by default.\n   * If `desc`, years are displayed in descending order.\n   * @default 'asc'\n   */\n  yearsOrder: PropTypes.oneOf(['asc', 'desc']),\n  /**\n   * Years rendered per row.\n   * @default `4` when `displayStaticWrapperAs === 'desktop'`, `3` otherwise.\n   */\n  yearsPerRow: PropTypes.oneOf([3, 4])\n};\nexport { StaticDateTimePicker };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,iCAAiC,QAAQ,6BAA6B;AAC/E,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,sBAAsB,QAAQ,+BAA+B;AACtE,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,SAASD,oBAAoBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrG,MAAMC,gBAAgB,GAAGV,iCAAiC,CAACQ,OAAO,EAAE,yBAAyB,CAAC;EAC9F,MAAMG,sBAAsB,GAAGD,gBAAgB,CAACC,sBAAsB,IAAI,QAAQ;EAClF,MAAMC,WAAW,GAAGF,gBAAgB,CAACE,WAAW,IAAID,sBAAsB,KAAK,SAAS;EACxF,MAAME,aAAa,GAAGhB,QAAQ,CAAC;IAC7BiB,GAAG,EAAEZ,sBAAsB;IAC3Ba,KAAK,EAAEb,sBAAsB;IAC7Bc,IAAI,EAAEd,sBAAsB;IAC5Be,KAAK,EAAEhB,mBAAmB;IAC1BiB,OAAO,EAAEjB,mBAAmB;IAC5BkB,OAAO,EAAElB;EACX,CAAC,EAAES,gBAAgB,CAACG,aAAa,CAAC;;EAElC;EACA,MAAMO,KAAK,GAAGvB,QAAQ,CAAC,CAAC,CAAC,EAAEa,gBAAgB,EAAE;IAC3CG,aAAa;IACbF,sBAAsB;IACtBC,WAAW;IACXS,WAAW,EAAEX,gBAAgB,CAACW,WAAW,KAAKV,sBAAsB,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1FW,SAAS,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAEa,gBAAgB,CAACY,SAAS,EAAE;MAClDC,IAAI,EAAE1B,QAAQ,CAAC;QACb2B,MAAM,EAAEb,sBAAsB,KAAK;MACrC,CAAC,EAAED,gBAAgB,CAACY,SAAS,EAAEC,IAAI,CAAC;MACpCE,OAAO,EAAE5B,QAAQ,CAAC;QAChB2B,MAAM,EAAEb,sBAAsB,KAAK,SAAS;QAC5CC;MACF,CAAC,EAAEF,gBAAgB,CAACY,SAAS,EAAEG,OAAO;IACxC,CAAC;EACH,CAAC,CAAC;EACF,MAAM;IACJC;EACF,CAAC,GAAGtB,eAAe,CAAC;IAClBgB,KAAK;IACLO,YAAY,EAAExB,sBAAsB;IACpCyB,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAExB,gBAAgB;IAC3BI;EACF,CAAC,CAAC;EACF,OAAOiB,YAAY,CAAC,CAAC;AACvB,CAAC,CAAC;AACFpB,oBAAoB,CAACwB,SAAS,GAAG;EAC/B;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEC,IAAI,EAAEhC,SAAS,CAACiC,IAAI;EACpB;AACF;AACA;AACA;EACEpB,WAAW,EAAEb,SAAS,CAACiC,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEC,SAAS,EAAElC,SAAS,CAACiC,IAAI;EACzBE,SAAS,EAAEnC,SAAS,CAACoC,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEC,kBAAkB,EAAErC,SAAS,CAACsC,IAAI;EAClC;AACF;AACA;AACA;EACEC,YAAY,EAAEvC,SAAS,CAACwC,MAAM;EAC9B;AACF;AACA;AACA;EACEC,QAAQ,EAAEzC,SAAS,CAACiC,IAAI;EACxB;AACF;AACA;AACA;EACES,aAAa,EAAE1C,SAAS,CAACiC,IAAI;EAC7B;AACF;AACA;AACA;EACEU,qBAAqB,EAAE3C,SAAS,CAACiC,IAAI;EACrC;AACF;AACA;AACA;EACEW,wCAAwC,EAAE5C,SAAS,CAACiC,IAAI;EACxD;AACF;AACA;AACA;EACEY,WAAW,EAAE7C,SAAS,CAACiC,IAAI;EAC3B;AACF;AACA;AACA;EACErB,sBAAsB,EAAEZ,SAAS,CAAC8C,KAAK,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;EAC9D;AACF;AACA;EACEC,iBAAiB,EAAE/C,SAAS,CAACiC,IAAI;EACjC;AACF;AACA;AACA;EACEe,eAAe,EAAEhD,SAAS,CAACiD,MAAM;EACjC;AACF;AACA;AACA;AACA;EACEC,OAAO,EAAElD,SAAS,CAACiC,IAAI;EACvB;AACF;AACA;AACA;EACEkB,UAAU,EAAEnD,SAAS,CAACwC,MAAM;EAC5B;AACF;AACA;AACA;EACEY,OAAO,EAAEpD,SAAS,CAACwC,MAAM;EACzB;AACF;AACA;EACEa,WAAW,EAAErD,SAAS,CAACwC,MAAM;EAC7B;AACF;AACA;AACA;EACEc,OAAO,EAAEtD,SAAS,CAACwC,MAAM;EACzB;AACF;AACA;AACA;EACEe,OAAO,EAAEvD,SAAS,CAACwC,MAAM;EACzB;AACF;AACA;EACEgB,WAAW,EAAExD,SAAS,CAACwC,MAAM;EAC7B;AACF;AACA;AACA;EACEiB,OAAO,EAAEzD,SAAS,CAACwC,MAAM;EACzB;AACF;AACA;AACA;EACEkB,WAAW,EAAE1D,SAAS,CAACiD,MAAM;EAC7B;AACF;AACA;AACA;EACEU,YAAY,EAAE3D,SAAS,CAAC8C,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACrC;AACF;AACA;AACA;AACA;AACA;AACA;EACEc,QAAQ,EAAE5D,SAAS,CAACsC,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACEuB,QAAQ,EAAE7D,SAAS,CAACsC,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEwB,OAAO,EAAE9D,SAAS,CAACsC,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEyB,OAAO,EAAE/D,SAAS,CAACsC,IAAI;EACvB;AACF;AACA;AACA;AACA;EACE0B,aAAa,EAAEhE,SAAS,CAACsC,IAAI;EAC7B;AACF;AACA;AACA;AACA;EACE2B,YAAY,EAAEjE,SAAS,CAACsC,IAAI;EAC5B;AACF;AACA;AACA;AACA;EACE4B,YAAY,EAAElE,SAAS,CAACsC,IAAI;EAC5B;AACF;AACA;AACA;AACA;EACE6B,MAAM,EAAEnE,SAAS,CAAC8C,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;EAChF;AACF;AACA;EACEsB,WAAW,EAAEpE,SAAS,CAAC8C,KAAK,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;EACvDuB,QAAQ,EAAErE,SAAS,CAACiC,IAAI;EACxB;AACF;AACA;AACA;EACEqC,gBAAgB,EAAEtE,SAAS,CAACiC,IAAI;EAChC;AACF;AACA;AACA;EACEsC,aAAa,EAAEvE,SAAS,CAACwC,MAAM;EAC/B;AACF;AACA;AACA;AACA;EACEgC,aAAa,EAAExE,SAAS,CAACsC,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEmC,iBAAiB,EAAEzE,SAAS,CAACsC,IAAI;EACjC;AACF;AACA;AACA;AACA;AACA;EACEoC,kBAAkB,EAAE1E,SAAS,CAACsC,IAAI;EAClC;AACF;AACA;AACA;AACA;AACA;AACA;EACEqC,iBAAiB,EAAE3E,SAAS,CAACsC,IAAI;EACjC;AACF;AACA;AACA;AACA;AACA;EACEsC,iBAAiB,EAAE5E,SAAS,CAACsC,IAAI;EACjC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEuC,2BAA2B,EAAE7E,SAAS,CAACiC,IAAI;EAC3C;AACF;AACA;AACA;EACEV,SAAS,EAAEvB,SAAS,CAACwC,MAAM;EAC3B;AACF;AACA;AACA;EACEsC,KAAK,EAAE9E,SAAS,CAACwC,MAAM;EACvB;AACF;AACA;EACEuC,EAAE,EAAE/E,SAAS,CAACgF,SAAS,CAAC,CAAChF,SAAS,CAACiF,OAAO,CAACjF,SAAS,CAACgF,SAAS,CAAC,CAAChF,SAAS,CAACsC,IAAI,EAAEtC,SAAS,CAACwC,MAAM,EAAExC,SAAS,CAACiC,IAAI,CAAC,CAAC,CAAC,EAAEjC,SAAS,CAACsC,IAAI,EAAEtC,SAAS,CAACwC,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;EACE0C,QAAQ,EAAElF,SAAS,CAACoC,MAAM;EAC1B;AACF;AACA;AACA;EACE+C,KAAK,EAAEnF,SAAS,CAACwC,MAAM;EACvB;AACF;AACA;AACA;AACA;EACE4C,IAAI,EAAEpF,SAAS,CAAC8C,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;EAC9E;AACF;AACA;AACA;AACA;EACEhC,aAAa,EAAEd,SAAS,CAACqF,KAAK,CAAC;IAC7BtE,GAAG,EAAEf,SAAS,CAACsC,IAAI;IACnBpB,KAAK,EAAElB,SAAS,CAACsC,IAAI;IACrBnB,OAAO,EAAEnB,SAAS,CAACsC,IAAI;IACvBtB,KAAK,EAAEhB,SAAS,CAACsC,IAAI;IACrBlB,OAAO,EAAEpB,SAAS,CAACsC,IAAI;IACvBrB,IAAI,EAAEjB,SAAS,CAACsC;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEgD,KAAK,EAAEtF,SAAS,CAACiF,OAAO,CAACjF,SAAS,CAAC8C,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAACyC,UAAU,CAAC;EAC7G;AACF;AACA;AACA;AACA;EACEC,UAAU,EAAExF,SAAS,CAAC8C,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;EAC5C;AACF;AACA;AACA;EACExB,WAAW,EAAEtB,SAAS,CAAC8C,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACrC,CAAC;AACD,SAASvC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}