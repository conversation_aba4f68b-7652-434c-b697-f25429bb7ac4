{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"props\", \"getOpenDialogAriaText\"];\nimport * as React from 'react';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport useForkRef from '@mui/utils/useForkRef';\nimport useId from '@mui/utils/useId';\nimport { PickersModalDialog } from \"../../components/PickersModalDialog.js\";\nimport { usePicker } from \"../usePicker/index.js\";\nimport { onSpaceOrEnter } from \"../../utils/utils.js\";\nimport { LocalizationProvider } from \"../../../LocalizationProvider/index.js\";\nimport { PickersLayout } from \"../../../PickersLayout/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Hook managing all the single-date mobile pickers:\n * - MobileDatePicker\n * - MobileDateTimePicker\n * - MobileTimePicker\n */\nexport const useMobilePicker = _ref => {\n  let {\n      props,\n      getOpenDialogAriaText\n    } = _ref,\n    pickerParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const {\n    slots,\n    slotProps: innerSlotProps,\n    className,\n    sx,\n    format,\n    formatDensity,\n    enableAccessibleFieldDOMStructure,\n    selectedSections,\n    onSelectedSectionsChange,\n    timezone,\n    name,\n    label,\n    inputRef,\n    readOnly,\n    disabled,\n    localeText\n  } = props;\n  const fieldRef = React.useRef(null);\n  const labelId = useId();\n  const isToolbarHidden = innerSlotProps?.toolbar?.hidden ?? false;\n  const {\n    open,\n    actions,\n    layoutProps,\n    renderCurrentView,\n    fieldProps: pickerFieldProps\n  } = usePicker(_extends({}, pickerParams, {\n    props,\n    fieldRef,\n    autoFocusView: true,\n    additionalViewProps: {},\n    wrapperVariant: 'mobile'\n  }));\n  const Field = slots.field;\n  const fieldProps = useSlotProps({\n    elementType: Field,\n    externalSlotProps: innerSlotProps?.field,\n    additionalProps: _extends({}, pickerFieldProps, isToolbarHidden && {\n      id: labelId\n    }, !(disabled || readOnly) && {\n      onClick: actions.onOpen,\n      onKeyDown: onSpaceOrEnter(actions.onOpen)\n    }, {\n      readOnly: readOnly ?? true,\n      disabled,\n      className,\n      sx,\n      format,\n      formatDensity,\n      enableAccessibleFieldDOMStructure,\n      selectedSections,\n      onSelectedSectionsChange,\n      timezone,\n      label,\n      name\n    }, inputRef ? {\n      inputRef\n    } : {}),\n    ownerState: props\n  });\n\n  // TODO: Move to `useSlotProps` when https://github.com/mui/material-ui/pull/35088 will be merged\n  fieldProps.inputProps = _extends({}, fieldProps.inputProps, {\n    'aria-label': getOpenDialogAriaText(pickerFieldProps.value)\n  });\n  const slotsForField = _extends({\n    textField: slots.textField\n  }, fieldProps.slots);\n  const Layout = slots.layout ?? PickersLayout;\n  let labelledById = labelId;\n  if (isToolbarHidden) {\n    if (label) {\n      labelledById = `${labelId}-label`;\n    } else {\n      labelledById = undefined;\n    }\n  }\n  const slotProps = _extends({}, innerSlotProps, {\n    toolbar: _extends({}, innerSlotProps?.toolbar, {\n      titleId: labelId\n    }),\n    mobilePaper: _extends({\n      'aria-labelledby': labelledById\n    }, innerSlotProps?.mobilePaper)\n  });\n  const handleFieldRef = useForkRef(fieldRef, fieldProps.unstableFieldRef);\n  const renderPicker = () => /*#__PURE__*/_jsxs(LocalizationProvider, {\n    localeText: localeText,\n    children: [/*#__PURE__*/_jsx(Field, _extends({}, fieldProps, {\n      slots: slotsForField,\n      slotProps: slotProps,\n      unstableFieldRef: handleFieldRef\n    })), /*#__PURE__*/_jsx(PickersModalDialog, _extends({}, actions, {\n      open: open,\n      slots: slots,\n      slotProps: slotProps,\n      children: /*#__PURE__*/_jsx(Layout, _extends({}, layoutProps, slotProps?.layout, {\n        slots: slots,\n        slotProps: slotProps,\n        children: renderCurrentView()\n      }))\n    }))]\n  });\n  return {\n    renderPicker\n  };\n};", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "useSlotProps", "useForkRef", "useId", "PickersModalDialog", "usePicker", "onSpaceOrEnter", "LocalizationProvider", "PickersLayout", "jsx", "_jsx", "jsxs", "_jsxs", "useMobilePicker", "_ref", "props", "getOpenDialogAriaText", "pickerParams", "slots", "slotProps", "innerSlotProps", "className", "sx", "format", "formatDensity", "enableAccessibleFieldDOMStructure", "selectedSections", "onSelectedSectionsChange", "timezone", "name", "label", "inputRef", "readOnly", "disabled", "localeText", "fieldRef", "useRef", "labelId", "isToolbarHidden", "toolbar", "hidden", "open", "actions", "layoutProps", "renderCurrentView", "fieldProps", "pickerFieldProps", "autoFocusView", "additionalViewProps", "wrapperVariant", "Field", "field", "elementType", "externalSlotProps", "additionalProps", "id", "onClick", "onOpen", "onKeyDown", "ownerState", "inputProps", "value", "slotsForField", "textField", "Layout", "layout", "labelledById", "undefined", "titleId", "mobilePaper", "handleFieldRef", "unstableFieldRef", "renderPicker", "children"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useMobilePicker/useMobilePicker.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"props\", \"getOpenDialogAriaText\"];\nimport * as React from 'react';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport useForkRef from '@mui/utils/useForkRef';\nimport useId from '@mui/utils/useId';\nimport { PickersModalDialog } from \"../../components/PickersModalDialog.js\";\nimport { usePicker } from \"../usePicker/index.js\";\nimport { onSpaceOrEnter } from \"../../utils/utils.js\";\nimport { LocalizationProvider } from \"../../../LocalizationProvider/index.js\";\nimport { PickersLayout } from \"../../../PickersLayout/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Hook managing all the single-date mobile pickers:\n * - MobileDatePicker\n * - MobileDateTimePicker\n * - MobileTimePicker\n */\nexport const useMobilePicker = _ref => {\n  let {\n      props,\n      getOpenDialogAriaText\n    } = _ref,\n    pickerParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const {\n    slots,\n    slotProps: innerSlotProps,\n    className,\n    sx,\n    format,\n    formatDensity,\n    enableAccessibleFieldDOMStructure,\n    selectedSections,\n    onSelectedSectionsChange,\n    timezone,\n    name,\n    label,\n    inputRef,\n    readOnly,\n    disabled,\n    localeText\n  } = props;\n  const fieldRef = React.useRef(null);\n  const labelId = useId();\n  const isToolbarHidden = innerSlotProps?.toolbar?.hidden ?? false;\n  const {\n    open,\n    actions,\n    layoutProps,\n    renderCurrentView,\n    fieldProps: pickerFieldProps\n  } = usePicker(_extends({}, pickerParams, {\n    props,\n    fieldRef,\n    autoFocusView: true,\n    additionalViewProps: {},\n    wrapperVariant: 'mobile'\n  }));\n  const Field = slots.field;\n  const fieldProps = useSlotProps({\n    elementType: Field,\n    externalSlotProps: innerSlotProps?.field,\n    additionalProps: _extends({}, pickerFieldProps, isToolbarHidden && {\n      id: labelId\n    }, !(disabled || readOnly) && {\n      onClick: actions.onOpen,\n      onKeyDown: onSpaceOrEnter(actions.onOpen)\n    }, {\n      readOnly: readOnly ?? true,\n      disabled,\n      className,\n      sx,\n      format,\n      formatDensity,\n      enableAccessibleFieldDOMStructure,\n      selectedSections,\n      onSelectedSectionsChange,\n      timezone,\n      label,\n      name\n    }, inputRef ? {\n      inputRef\n    } : {}),\n    ownerState: props\n  });\n\n  // TODO: Move to `useSlotProps` when https://github.com/mui/material-ui/pull/35088 will be merged\n  fieldProps.inputProps = _extends({}, fieldProps.inputProps, {\n    'aria-label': getOpenDialogAriaText(pickerFieldProps.value)\n  });\n  const slotsForField = _extends({\n    textField: slots.textField\n  }, fieldProps.slots);\n  const Layout = slots.layout ?? PickersLayout;\n  let labelledById = labelId;\n  if (isToolbarHidden) {\n    if (label) {\n      labelledById = `${labelId}-label`;\n    } else {\n      labelledById = undefined;\n    }\n  }\n  const slotProps = _extends({}, innerSlotProps, {\n    toolbar: _extends({}, innerSlotProps?.toolbar, {\n      titleId: labelId\n    }),\n    mobilePaper: _extends({\n      'aria-labelledby': labelledById\n    }, innerSlotProps?.mobilePaper)\n  });\n  const handleFieldRef = useForkRef(fieldRef, fieldProps.unstableFieldRef);\n  const renderPicker = () => /*#__PURE__*/_jsxs(LocalizationProvider, {\n    localeText: localeText,\n    children: [/*#__PURE__*/_jsx(Field, _extends({}, fieldProps, {\n      slots: slotsForField,\n      slotProps: slotProps,\n      unstableFieldRef: handleFieldRef\n    })), /*#__PURE__*/_jsx(PickersModalDialog, _extends({}, actions, {\n      open: open,\n      slots: slots,\n      slotProps: slotProps,\n      children: /*#__PURE__*/_jsx(Layout, _extends({}, layoutProps, slotProps?.layout, {\n        slots: slots,\n        slotProps: slotProps,\n        children: renderCurrentView()\n      }))\n    }))]\n  });\n  return {\n    renderPicker\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,uBAAuB,CAAC;AACpD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,KAAK,MAAM,kBAAkB;AACpC,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAGC,IAAI,IAAI;EACrC,IAAI;MACAC,KAAK;MACLC;IACF,CAAC,GAAGF,IAAI;IACRG,YAAY,GAAGnB,6BAA6B,CAACgB,IAAI,EAAEf,SAAS,CAAC;EAC/D,MAAM;IACJmB,KAAK;IACLC,SAAS,EAAEC,cAAc;IACzBC,SAAS;IACTC,EAAE;IACFC,MAAM;IACNC,aAAa;IACbC,iCAAiC;IACjCC,gBAAgB;IAChBC,wBAAwB;IACxBC,QAAQ;IACRC,IAAI;IACJC,KAAK;IACLC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC;EACF,CAAC,GAAGnB,KAAK;EACT,MAAMoB,QAAQ,GAAGnC,KAAK,CAACoC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMC,OAAO,GAAGlC,KAAK,CAAC,CAAC;EACvB,MAAMmC,eAAe,GAAGlB,cAAc,EAAEmB,OAAO,EAAEC,MAAM,IAAI,KAAK;EAChE,MAAM;IACJC,IAAI;IACJC,OAAO;IACPC,WAAW;IACXC,iBAAiB;IACjBC,UAAU,EAAEC;EACd,CAAC,GAAGzC,SAAS,CAACR,QAAQ,CAAC,CAAC,CAAC,EAAEoB,YAAY,EAAE;IACvCF,KAAK;IACLoB,QAAQ;IACRY,aAAa,EAAE,IAAI;IACnBC,mBAAmB,EAAE,CAAC,CAAC;IACvBC,cAAc,EAAE;EAClB,CAAC,CAAC,CAAC;EACH,MAAMC,KAAK,GAAGhC,KAAK,CAACiC,KAAK;EACzB,MAAMN,UAAU,GAAG5C,YAAY,CAAC;IAC9BmD,WAAW,EAAEF,KAAK;IAClBG,iBAAiB,EAAEjC,cAAc,EAAE+B,KAAK;IACxCG,eAAe,EAAEzD,QAAQ,CAAC,CAAC,CAAC,EAAEiD,gBAAgB,EAAER,eAAe,IAAI;MACjEiB,EAAE,EAAElB;IACN,CAAC,EAAE,EAAEJ,QAAQ,IAAID,QAAQ,CAAC,IAAI;MAC5BwB,OAAO,EAAEd,OAAO,CAACe,MAAM;MACvBC,SAAS,EAAEpD,cAAc,CAACoC,OAAO,CAACe,MAAM;IAC1C,CAAC,EAAE;MACDzB,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BC,QAAQ;MACRZ,SAAS;MACTC,EAAE;MACFC,MAAM;MACNC,aAAa;MACbC,iCAAiC;MACjCC,gBAAgB;MAChBC,wBAAwB;MACxBC,QAAQ;MACRE,KAAK;MACLD;IACF,CAAC,EAAEE,QAAQ,GAAG;MACZA;IACF,CAAC,GAAG,CAAC,CAAC,CAAC;IACP4B,UAAU,EAAE5C;EACd,CAAC,CAAC;;EAEF;EACA8B,UAAU,CAACe,UAAU,GAAG/D,QAAQ,CAAC,CAAC,CAAC,EAAEgD,UAAU,CAACe,UAAU,EAAE;IAC1D,YAAY,EAAE5C,qBAAqB,CAAC8B,gBAAgB,CAACe,KAAK;EAC5D,CAAC,CAAC;EACF,MAAMC,aAAa,GAAGjE,QAAQ,CAAC;IAC7BkE,SAAS,EAAE7C,KAAK,CAAC6C;EACnB,CAAC,EAAElB,UAAU,CAAC3B,KAAK,CAAC;EACpB,MAAM8C,MAAM,GAAG9C,KAAK,CAAC+C,MAAM,IAAIzD,aAAa;EAC5C,IAAI0D,YAAY,GAAG7B,OAAO;EAC1B,IAAIC,eAAe,EAAE;IACnB,IAAIR,KAAK,EAAE;MACToC,YAAY,GAAG,GAAG7B,OAAO,QAAQ;IACnC,CAAC,MAAM;MACL6B,YAAY,GAAGC,SAAS;IAC1B;EACF;EACA,MAAMhD,SAAS,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAEuB,cAAc,EAAE;IAC7CmB,OAAO,EAAE1C,QAAQ,CAAC,CAAC,CAAC,EAAEuB,cAAc,EAAEmB,OAAO,EAAE;MAC7C6B,OAAO,EAAE/B;IACX,CAAC,CAAC;IACFgC,WAAW,EAAExE,QAAQ,CAAC;MACpB,iBAAiB,EAAEqE;IACrB,CAAC,EAAE9C,cAAc,EAAEiD,WAAW;EAChC,CAAC,CAAC;EACF,MAAMC,cAAc,GAAGpE,UAAU,CAACiC,QAAQ,EAAEU,UAAU,CAAC0B,gBAAgB,CAAC;EACxE,MAAMC,YAAY,GAAGA,CAAA,KAAM,aAAa5D,KAAK,CAACL,oBAAoB,EAAE;IAClE2B,UAAU,EAAEA,UAAU;IACtBuC,QAAQ,EAAE,CAAC,aAAa/D,IAAI,CAACwC,KAAK,EAAErD,QAAQ,CAAC,CAAC,CAAC,EAAEgD,UAAU,EAAE;MAC3D3B,KAAK,EAAE4C,aAAa;MACpB3C,SAAS,EAAEA,SAAS;MACpBoD,gBAAgB,EAAED;IACpB,CAAC,CAAC,CAAC,EAAE,aAAa5D,IAAI,CAACN,kBAAkB,EAAEP,QAAQ,CAAC,CAAC,CAAC,EAAE6C,OAAO,EAAE;MAC/DD,IAAI,EAAEA,IAAI;MACVvB,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA,SAAS;MACpBsD,QAAQ,EAAE,aAAa/D,IAAI,CAACsD,MAAM,EAAEnE,QAAQ,CAAC,CAAC,CAAC,EAAE8C,WAAW,EAAExB,SAAS,EAAE8C,MAAM,EAAE;QAC/E/C,KAAK,EAAEA,KAAK;QACZC,SAAS,EAAEA,SAAS;QACpBsD,QAAQ,EAAE7B,iBAAiB,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,OAAO;IACL4B;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}