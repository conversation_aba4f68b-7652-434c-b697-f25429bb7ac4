{"ast": null, "code": "import root from './_root.js';\nimport stubFalse from './stubFalse.js';\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined;\n\n/**\n * Checks if `value` is a buffer.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\n * @example\n *\n * _.isBuffer(new Buffer(2));\n * // => true\n *\n * _.isBuffer(new Uint8Array(2));\n * // => false\n */\nvar isBuffer = nativeIsBuffer || stubFalse;\nexport default isBuffer;", "map": {"version": 3, "names": ["root", "stubFalse", "freeExports", "exports", "nodeType", "freeModule", "module", "moduleExports", "<PERSON><PERSON><PERSON>", "undefined", "nativeIsBuffer", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/lodash-es/isBuffer.js"], "sourcesContent": ["import root from './_root.js';\nimport stubFalse from './stubFalse.js';\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined;\n\n/**\n * Checks if `value` is a buffer.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\n * @example\n *\n * _.isBuffer(new Buffer(2));\n * // => true\n *\n * _.isBuffer(new Uint8Array(2));\n * // => false\n */\nvar isBuffer = nativeIsBuffer || stubFalse;\n\nexport default isBuffer;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;AAC7B,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,WAAW,GAAG,OAAOC,OAAO,IAAI,QAAQ,IAAIA,OAAO,IAAI,CAACA,OAAO,CAACC,QAAQ,IAAID,OAAO;;AAEvF;AACA,IAAIE,UAAU,GAAGH,WAAW,IAAI,OAAOI,MAAM,IAAI,QAAQ,IAAIA,MAAM,IAAI,CAACA,MAAM,CAACF,QAAQ,IAAIE,MAAM;;AAEjG;AACA,IAAIC,aAAa,GAAGF,UAAU,IAAIA,UAAU,CAACF,OAAO,KAAKD,WAAW;;AAEpE;AACA,IAAIM,MAAM,GAAGD,aAAa,GAAGP,IAAI,CAACQ,MAAM,GAAGC,SAAS;;AAEpD;AACA,IAAIC,cAAc,GAAGF,MAAM,GAAGA,MAAM,CAACG,QAAQ,GAAGF,SAAS;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,QAAQ,GAAGD,cAAc,IAAIT,SAAS;AAE1C,eAAeU,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}