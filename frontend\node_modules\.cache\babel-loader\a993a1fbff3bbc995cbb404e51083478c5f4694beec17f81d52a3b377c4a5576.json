{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.carryValue = carryValue;\nvar _utils = require(\"../../utils\");\nfunction carryValue(element, state, newValue) {\n  const value = (0, _utils.getValue)(element);\n  state.carryValue = value !== newValue && value === '' && (0, _utils.hasUnreliableEmptyValue)(element) ? newValue : undefined;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "carryValue", "_utils", "require", "element", "state", "newValue", "getValue", "hasUnreliableEmptyValue", "undefined"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/user-event/dist/keyboard/shared/carryValue.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.carryValue = carryValue;\n\nvar _utils = require(\"../../utils\");\n\nfunction carryValue(element, state, newValue) {\n  const value = (0, _utils.getValue)(element);\n  state.carryValue = value !== newValue && value === '' && (0, _utils.hasUnreliableEmptyValue)(element) ? newValue : undefined;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,UAAU,GAAGA,UAAU;AAE/B,IAAIC,MAAM,GAAGC,OAAO,CAAC,aAAa,CAAC;AAEnC,SAASF,UAAUA,CAACG,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EAC5C,MAAMN,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,CAACK,QAAQ,EAAEH,OAAO,CAAC;EAC3CC,KAAK,CAACJ,UAAU,GAAGD,KAAK,KAAKM,QAAQ,IAAIN,KAAK,KAAK,EAAE,IAAI,CAAC,CAAC,EAAEE,MAAM,CAACM,uBAAuB,EAAEJ,OAAO,CAAC,GAAGE,QAAQ,GAAGG,SAAS;AAC9H", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}