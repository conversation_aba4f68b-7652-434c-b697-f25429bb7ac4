{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useThemeProps } from '@mui/material/styles';\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { TimePickerToolbar } from \"./TimePickerToolbar.js\";\nimport { applyDefaultViewProps } from \"../internals/utils/views.js\";\nexport function useTimePickerDefaultizedProps(props, name) {\n  const utils = useUtils();\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  const ampm = themeProps.ampm ?? utils.is12HourCycleInCurrentLocale();\n  const localeText = React.useMemo(() => {\n    if (themeProps.localeText?.toolbarTitle == null) {\n      return themeProps.localeText;\n    }\n    return _extends({}, themeProps.localeText, {\n      timePickerToolbarTitle: themeProps.localeText.toolbarTitle\n    });\n  }, [themeProps.localeText]);\n  return _extends({}, themeProps, {\n    ampm,\n    localeText\n  }, applyDefaultViewProps({\n    views: themeProps.views,\n    openTo: themeProps.openTo,\n    defaultViews: ['hours', 'minutes'],\n    defaultOpenTo: 'hours'\n  }), {\n    disableFuture: themeProps.disableFuture ?? false,\n    disablePast: themeProps.disablePast ?? false,\n    slots: _extends({\n      toolbar: TimePickerToolbar\n    }, themeProps.slots),\n    slotProps: _extends({}, themeProps.slotProps, {\n      toolbar: _extends({\n        ampm,\n        ampmInClock: themeProps.ampmInClock\n      }, themeProps.slotProps?.toolbar)\n    })\n  });\n}", "map": {"version": 3, "names": ["_extends", "React", "useThemeProps", "useUtils", "TimePickerToolbar", "applyDefaultViewProps", "useTimePickerDefaultizedProps", "props", "name", "utils", "themeProps", "ampm", "is12HourCycleInCurrentLocale", "localeText", "useMemo", "toolbarTitle", "timePickerToolbarTitle", "views", "openTo", "defaultViews", "defaultOpenTo", "disableFuture", "disablePast", "slots", "toolbar", "slotProps", "ampmInClock"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/TimePicker/shared.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useThemeProps } from '@mui/material/styles';\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { TimePickerToolbar } from \"./TimePickerToolbar.js\";\nimport { applyDefaultViewProps } from \"../internals/utils/views.js\";\nexport function useTimePickerDefaultizedProps(props, name) {\n  const utils = useUtils();\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  const ampm = themeProps.ampm ?? utils.is12HourCycleInCurrentLocale();\n  const localeText = React.useMemo(() => {\n    if (themeProps.localeText?.toolbarTitle == null) {\n      return themeProps.localeText;\n    }\n    return _extends({}, themeProps.localeText, {\n      timePickerToolbarTitle: themeProps.localeText.toolbarTitle\n    });\n  }, [themeProps.localeText]);\n  return _extends({}, themeProps, {\n    ampm,\n    localeText\n  }, applyDefaultViewProps({\n    views: themeProps.views,\n    openTo: themeProps.openTo,\n    defaultViews: ['hours', 'minutes'],\n    defaultOpenTo: 'hours'\n  }), {\n    disableFuture: themeProps.disableFuture ?? false,\n    disablePast: themeProps.disablePast ?? false,\n    slots: _extends({\n      toolbar: TimePickerToolbar\n    }, themeProps.slots),\n    slotProps: _extends({}, themeProps.slotProps, {\n      toolbar: _extends({\n        ampm,\n        ampmInClock: themeProps.ampmInClock\n      }, themeProps.slotProps?.toolbar)\n    })\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE,OAAO,SAASC,6BAA6BA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACzD,MAAMC,KAAK,GAAGN,QAAQ,CAAC,CAAC;EACxB,MAAMO,UAAU,GAAGR,aAAa,CAAC;IAC/BK,KAAK;IACLC;EACF,CAAC,CAAC;EACF,MAAMG,IAAI,GAAGD,UAAU,CAACC,IAAI,IAAIF,KAAK,CAACG,4BAA4B,CAAC,CAAC;EACpE,MAAMC,UAAU,GAAGZ,KAAK,CAACa,OAAO,CAAC,MAAM;IACrC,IAAIJ,UAAU,CAACG,UAAU,EAAEE,YAAY,IAAI,IAAI,EAAE;MAC/C,OAAOL,UAAU,CAACG,UAAU;IAC9B;IACA,OAAOb,QAAQ,CAAC,CAAC,CAAC,EAAEU,UAAU,CAACG,UAAU,EAAE;MACzCG,sBAAsB,EAAEN,UAAU,CAACG,UAAU,CAACE;IAChD,CAAC,CAAC;EACJ,CAAC,EAAE,CAACL,UAAU,CAACG,UAAU,CAAC,CAAC;EAC3B,OAAOb,QAAQ,CAAC,CAAC,CAAC,EAAEU,UAAU,EAAE;IAC9BC,IAAI;IACJE;EACF,CAAC,EAAER,qBAAqB,CAAC;IACvBY,KAAK,EAAEP,UAAU,CAACO,KAAK;IACvBC,MAAM,EAAER,UAAU,CAACQ,MAAM;IACzBC,YAAY,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;IAClCC,aAAa,EAAE;EACjB,CAAC,CAAC,EAAE;IACFC,aAAa,EAAEX,UAAU,CAACW,aAAa,IAAI,KAAK;IAChDC,WAAW,EAAEZ,UAAU,CAACY,WAAW,IAAI,KAAK;IAC5CC,KAAK,EAAEvB,QAAQ,CAAC;MACdwB,OAAO,EAAEpB;IACX,CAAC,EAAEM,UAAU,CAACa,KAAK,CAAC;IACpBE,SAAS,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAEU,UAAU,CAACe,SAAS,EAAE;MAC5CD,OAAO,EAAExB,QAAQ,CAAC;QAChBW,IAAI;QACJe,WAAW,EAAEhB,UAAU,CAACgB;MAC1B,CAAC,EAAEhB,UAAU,CAACe,SAAS,EAAED,OAAO;IAClC,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}