{"ast": null, "code": "/**\n * Delegate to handle a media query being matched and unmatched.\n *\n * @param {object} options\n * @param {function} options.match callback for when the media query is matched\n * @param {function} [options.unmatch] callback for when the media query is unmatched\n * @param {function} [options.setup] one-time callback triggered the first time a query is matched\n * @param {boolean} [options.deferSetup=false] should the setup callback be run immediately, rather than first time query is matched?\n * @constructor\n */\nfunction QueryHandler(options) {\n  this.options = options;\n  !options.deferSetup && this.setup();\n}\nQueryHandler.prototype = {\n  constructor: QueryHandler,\n  /**\n   * coordinates setup of the handler\n   *\n   * @function\n   */\n  setup: function () {\n    if (this.options.setup) {\n      this.options.setup();\n    }\n    this.initialised = true;\n  },\n  /**\n   * coordinates setup and triggering of the handler\n   *\n   * @function\n   */\n  on: function () {\n    !this.initialised && this.setup();\n    this.options.match && this.options.match();\n  },\n  /**\n   * coordinates the unmatch event for the handler\n   *\n   * @function\n   */\n  off: function () {\n    this.options.unmatch && this.options.unmatch();\n  },\n  /**\n   * called when a handler is to be destroyed.\n   * delegates to the destroy or unmatch callbacks, depending on availability.\n   *\n   * @function\n   */\n  destroy: function () {\n    this.options.destroy ? this.options.destroy() : this.off();\n  },\n  /**\n   * determines equality by reference.\n   * if object is supplied compare options, if function, compare match callback\n   *\n   * @function\n   * @param {object || function} [target] the target for comparison\n   */\n  equals: function (target) {\n    return this.options === target || this.options.match === target;\n  }\n};\nmodule.exports = QueryHandler;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "deferSetup", "setup", "prototype", "constructor", "initialised", "on", "match", "off", "unmatch", "destroy", "equals", "target", "module", "exports"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/enquire.js/src/QueryHandler.js"], "sourcesContent": ["/**\n * Delegate to handle a media query being matched and unmatched.\n *\n * @param {object} options\n * @param {function} options.match callback for when the media query is matched\n * @param {function} [options.unmatch] callback for when the media query is unmatched\n * @param {function} [options.setup] one-time callback triggered the first time a query is matched\n * @param {boolean} [options.deferSetup=false] should the setup callback be run immediately, rather than first time query is matched?\n * @constructor\n */\nfunction QueryHandler(options) {\n    this.options = options;\n    !options.deferSetup && this.setup();\n}\n\nQueryHandler.prototype = {\n\n    constructor : QueryHandler,\n\n    /**\n     * coordinates setup of the handler\n     *\n     * @function\n     */\n    setup : function() {\n        if(this.options.setup) {\n            this.options.setup();\n        }\n        this.initialised = true;\n    },\n\n    /**\n     * coordinates setup and triggering of the handler\n     *\n     * @function\n     */\n    on : function() {\n        !this.initialised && this.setup();\n        this.options.match && this.options.match();\n    },\n\n    /**\n     * coordinates the unmatch event for the handler\n     *\n     * @function\n     */\n    off : function() {\n        this.options.unmatch && this.options.unmatch();\n    },\n\n    /**\n     * called when a handler is to be destroyed.\n     * delegates to the destroy or unmatch callbacks, depending on availability.\n     *\n     * @function\n     */\n    destroy : function() {\n        this.options.destroy ? this.options.destroy() : this.off();\n    },\n\n    /**\n     * determines equality by reference.\n     * if object is supplied compare options, if function, compare match callback\n     *\n     * @function\n     * @param {object || function} [target] the target for comparison\n     */\n    equals : function(target) {\n        return this.options === target || this.options.match === target;\n    }\n\n};\n\nmodule.exports = QueryHandler;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAYA,CAACC,OAAO,EAAE;EAC3B,IAAI,CAACA,OAAO,GAAGA,OAAO;EACtB,CAACA,OAAO,CAACC,UAAU,IAAI,IAAI,CAACC,KAAK,CAAC,CAAC;AACvC;AAEAH,YAAY,CAACI,SAAS,GAAG;EAErBC,WAAW,EAAGL,YAAY;EAE1B;AACJ;AACA;AACA;AACA;EACIG,KAAK,EAAG,SAAAA,CAAA,EAAW;IACf,IAAG,IAAI,CAACF,OAAO,CAACE,KAAK,EAAE;MACnB,IAAI,CAACF,OAAO,CAACE,KAAK,CAAC,CAAC;IACxB;IACA,IAAI,CAACG,WAAW,GAAG,IAAI;EAC3B,CAAC;EAED;AACJ;AACA;AACA;AACA;EACIC,EAAE,EAAG,SAAAA,CAAA,EAAW;IACZ,CAAC,IAAI,CAACD,WAAW,IAAI,IAAI,CAACH,KAAK,CAAC,CAAC;IACjC,IAAI,CAACF,OAAO,CAACO,KAAK,IAAI,IAAI,CAACP,OAAO,CAACO,KAAK,CAAC,CAAC;EAC9C,CAAC;EAED;AACJ;AACA;AACA;AACA;EACIC,GAAG,EAAG,SAAAA,CAAA,EAAW;IACb,IAAI,CAACR,OAAO,CAACS,OAAO,IAAI,IAAI,CAACT,OAAO,CAACS,OAAO,CAAC,CAAC;EAClD,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAO,EAAG,SAAAA,CAAA,EAAW;IACjB,IAAI,CAACV,OAAO,CAACU,OAAO,GAAG,IAAI,CAACV,OAAO,CAACU,OAAO,CAAC,CAAC,GAAG,IAAI,CAACF,GAAG,CAAC,CAAC;EAC9D,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;EACIG,MAAM,EAAG,SAAAA,CAASC,MAAM,EAAE;IACtB,OAAO,IAAI,CAACZ,OAAO,KAAKY,MAAM,IAAI,IAAI,CAACZ,OAAO,CAACO,KAAK,KAAKK,MAAM;EACnE;AAEJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGf,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}