import { Ava<PERSON>, Badge, IconButton } from "@mui/material";
import React from "react";
import SearchIcon from "@mui/icons-material/Search";
import { green } from "@mui/material/colors";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import "./Navbar.css";
import { Person } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";


export const Navbar = () => {
  const {auth,card} = useSelector(store => store);
  const navigate = useNavigate();

  const handleAvatarClick=()=> {
    if(auth.user?.role === "RESTAURANT_OWNER"){
      navigate("/admin/restaurant")
    }else{
      navigate("/my-account")
    }
  }
  return (
    <div className="px-5 z-50 py-[.8rem] bg-[#93c47d] lg:px-20 flex justify-between">
      <div className="lg:mr-10 cursor-pointer flex items-center space-x-4">
        <li onClick={()=>navigate("/")} className="logo font-semibold text-gray-300 text-2xl">
          FlavorDash
        </li>
      </div>

      <div className="flex items-center space-x-2 lg:space-x-10">
        <div className="">
          <IconButton>
            <SearchIcon sx={{ fontSize: "1.5rem" }} />
          </IconButton>
        </div>

        <div className="">
          {auth.user ? (
            <Avatar onClick={handleAvatarClick} sx={{ bgcolor: "white", color: green.A200 }}>{auth.user?.fullName[0].toUpperCase()}</Avatar>
          ) : (
            <IconButton onClick={()=> navigate("/account/login")}>
              <Person />
            </IconButton>
          )}
        </div>

        <div className="">
          <IconButton onClick={()=> navigate("/card")}>
            <Badge color="secondary" badgeContent={card.card?.items.length}>
              <ShoppingCartIcon sx={{ fontSize: "1.5rem" }} />
            </Badge>
          </IconButton>
        </div>
      </div>
    </div>
  );
};

export default Navbar;
