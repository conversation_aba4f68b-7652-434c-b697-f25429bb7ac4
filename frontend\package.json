{"name": "foodordering-react", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^6.1.1", "@mui/material": "^6.1.1", "@mui/x-date-pickers": "^7.18.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.7", "dayjs": "^1.11.13", "formik": "^2.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^9.1.2", "react-router-dom": "^6.26.2", "react-scripts": "5.0.1", "react-slick": "^0.30.2", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "slick-carousel": "^1.8.1", "web-vitals": "^2.1.4", "yup": "^1.4.0", "yupp": "^0.3.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.12"}}