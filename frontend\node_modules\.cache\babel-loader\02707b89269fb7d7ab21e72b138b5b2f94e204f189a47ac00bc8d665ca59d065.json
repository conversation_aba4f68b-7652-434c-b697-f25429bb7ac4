{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar sliderRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-haspopup': null,\n    'aria-invalid': null,\n    'aria-readonly': null,\n    'aria-valuetext': null,\n    'aria-orientation': 'horizontal',\n    'aria-valuemax': '100',\n    'aria-valuemin': '0'\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        name: 'type',\n        value: 'range'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-valuenow': null\n  },\n  superClass: [['roletype', 'widget', 'input'], ['roletype', 'structure', 'range']]\n};\nvar _default = sliderRole;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "sliderRole", "abstract", "accessibleNameRequired", "baseConcepts", "childrenPresentational", "nameFrom", "prohibitedProps", "props", "relatedConcepts", "concept", "attributes", "name", "module", "requireContextRole", "requiredContextRole", "requiredOwnedElements", "requiredProps", "superClass", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/etc/roles/literal/sliderRole.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar sliderRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-haspopup': null,\n    'aria-invalid': null,\n    'aria-readonly': null,\n    'aria-valuetext': null,\n    'aria-orientation': 'horizontal',\n    'aria-valuemax': '100',\n    'aria-valuemin': '0'\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        name: 'type',\n        value: 'range'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-valuenow': null\n  },\n  superClass: [['roletype', 'widget', 'input'], ['roletype', 'structure', 'range']]\n};\nvar _default = sliderRole;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,UAAU,GAAG;EACfC,QAAQ,EAAE,KAAK;EACfC,sBAAsB,EAAE,IAAI;EAC5BC,YAAY,EAAE,EAAE;EAChBC,sBAAsB,EAAE,IAAI;EAC5BC,QAAQ,EAAE,CAAC,QAAQ,CAAC;EACpBC,eAAe,EAAE,EAAE;EACnBC,KAAK,EAAE;IACL,mBAAmB,EAAE,IAAI;IACzB,eAAe,EAAE,IAAI;IACrB,cAAc,EAAE,IAAI;IACpB,eAAe,EAAE,IAAI;IACrB,gBAAgB,EAAE,IAAI;IACtB,kBAAkB,EAAE,YAAY;IAChC,eAAe,EAAE,KAAK;IACtB,eAAe,EAAE;EACnB,CAAC;EACDC,eAAe,EAAE,CAAC;IAChBC,OAAO,EAAE;MACPC,UAAU,EAAE,CAAC;QACXC,IAAI,EAAE,MAAM;QACZb,KAAK,EAAE;MACT,CAAC,CAAC;MACFa,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,CAAC;EACFC,kBAAkB,EAAE,EAAE;EACtBC,mBAAmB,EAAE,EAAE;EACvBC,qBAAqB,EAAE,EAAE;EACzBC,aAAa,EAAE;IACb,eAAe,EAAE;EACnB,CAAC;EACDC,UAAU,EAAE,CAAC,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;AAClF,CAAC;AACD,IAAIC,QAAQ,GAAGlB,UAAU;AACzBH,OAAO,CAACE,OAAO,GAAGmB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}