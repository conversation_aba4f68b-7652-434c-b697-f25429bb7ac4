package com.aalperen.Food.Ordering.controller.admin;

import com.aalperen.Food.Ordering.entity.Category;
import com.aalperen.Food.Ordering.entity.User;
import com.aalperen.Food.Ordering.service.CategoryService;
import com.aalperen.Food.Ordering.service.UserService;
import jakarta.persistence.Id;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private UserService userService;


    @PostMapping("/admin/category")
    public ResponseEntity<Category> createCategory(@RequestBody Category category,
                                                   @RequestHeader("Authorization")String jwt ) throws Exception {

        User user = userService.findUserByToken(jwt);

        Category createdCategory = categoryService.createCategory(category.getName(), user.getId());

        return new ResponseEntity<>(createdCategory, HttpStatus.CREATED);
    }


    @GetMapping("/category/restaurant/{id}")
    public ResponseEntity<List<Category>> getCategoryByRestaurantId(@PathVariable Long id,@RequestHeader("Authorization")String jwt ) throws Exception {

        User user = userService.findUserByToken(jwt);

        List<Category> categories = categoryService.findCategoryByRestaurantId(id);

        return new ResponseEntity<>(categories, HttpStatus.OK);
    }
}
