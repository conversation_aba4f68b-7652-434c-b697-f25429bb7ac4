[{"C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\store.js": "4", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Theme\\DarkTheme.js": "5", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Card\\action.js": "6", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Auth\\Action.js": "7", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Restaurant\\action.js": "8", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Routes\\Routers.jsx": "9", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Auth\\Reducer.js": "10", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Card\\reducer.js": "11", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Restaurant\\reducer.js": "12", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Menu\\reducer.js": "13", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Ingredients\\reducer.js": "14", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Order\\reducer.js": "15", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\RestaurantOrder\\reducer.js": "16", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Card\\actionType.js": "17", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Auth\\ActionType.js": "18", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Restaurant\\actionType.js": "19", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Routes\\AdminRoute.jsx": "20", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Menu\\actionType.js": "21", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Routes\\CustomerRoute.jsx": "22", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Ingredients\\actionType.js": "23", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\RestaurantOrder\\actionType.js": "24", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Order\\actionType.js": "25", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Config\\api.js": "26", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Config\\config.js": "27", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Navbar\\Navbar.jsx": "28", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Home\\Home.jsx": "29", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Restaurant\\RestaurantDetails.jsx": "30", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Card\\Cart.jsx": "31", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Auth\\Auth.jsx": "32", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Profile.jsx": "33", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\PaymentFail\\PaymentFail.jsx": "34", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\PaymentSuccess\\PaymentSuccess.jsx": "35", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Admin\\Admin.jsx": "36", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\CreateRestaurantForm\\CreateRestaurantForm.jsx": "37", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Card\\AddressCard.jsx": "38", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Card\\CardItem.jsx": "39", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Order\\action.js": "40", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Menu\\action.js": "41", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Home\\MultiItemCarousel.jsx": "42", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Restaurant\\RestaurantCard.jsx": "43", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Restaurant\\MenuCard.jsx": "44", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Auth\\LoginForm.jsx": "45", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Auth\\RegisterForm.jsx": "46", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\UserProfile.jsx": "47", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\ProfileNav.jsx": "48", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Orders.jsx": "49", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Favorites.jsx": "50", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Address.jsx": "51", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Events.jsx": "52", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Notifications.jsx": "53", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Payments.jsx": "54", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\RestaurantOrder\\action.js": "55", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Admin\\AdminSidebar.jsx": "56", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Admin\\RestaurantDetails.jsx": "57", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Util\\UploadToCloudinary.js": "58", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Dashboard\\Dashboard.jsx": "59", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Menu\\Menu.jsx": "60", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Category\\Category.jsx": "61", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Menu\\CreateMenuForm.jsx": "62", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Ingredients\\Ingredients.jsx": "63", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Orders\\Orders.jsx": "64", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Event\\Event.jsx": "65", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Details\\Details.jsx": "66", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Home\\TopMeel.js": "67", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Config\\catogorizeIngredients.js": "68", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Home\\CarouselItem.jsx": "69", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\OrderCard.jsx": "70", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\EvenetCard.jsx": "71", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Ingredients\\action.js": "72", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Menu\\MenuTable.jsx": "73", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Orders\\OrderTable.jsx": "74", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Category\\CategoryTable.jsx": "75", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Ingredients\\IngredientsCategoryTable.jsx": "76", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Ingredients\\IngredientsTable.jsx": "77", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Category\\CreateCategoryForm.jsx": "78", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Ingredients\\CreateIngredientCategoryForm.jsx": "79", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Ingredients\\CreateIngredientForm.jsx": "80", "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Data\\RestaurantSeedData.js": "81"}, {"size": 776, "mtime": 1754081309053, "results": "82", "hashOfConfig": "83"}, {"size": 1111, "mtime": 1754081308839, "results": "84", "hashOfConfig": "83"}, {"size": 362, "mtime": 1754081309210, "results": "85", "hashOfConfig": "83"}, {"size": 792, "mtime": 1754081313446, "results": "86", "hashOfConfig": "83"}, {"size": 473, "mtime": 1754081315631, "results": "87", "hashOfConfig": "83"}, {"size": 4954, "mtime": 1754081313979, "results": "88", "hashOfConfig": "83"}, {"size": 3217, "mtime": 1754081313640, "results": "89", "hashOfConfig": "83"}, {"size": 9179, "mtime": 1754081314982, "results": "90", "hashOfConfig": "83"}, {"size": 443, "mtime": 1754081315557, "results": "91", "hashOfConfig": "83"}, {"size": 1759, "mtime": 1754081313862, "results": "92", "hashOfConfig": "83"}, {"size": 2249, "mtime": 1754081314131, "results": "93", "hashOfConfig": "83"}, {"size": 3777, "mtime": 1754081315149, "results": "94", "hashOfConfig": "83"}, {"size": 2688, "mtime": 1754081314599, "results": "95", "hashOfConfig": "83"}, {"size": 1076, "mtime": 1754081314351, "results": "96", "hashOfConfig": "83"}, {"size": 1146, "mtime": 1754081314866, "results": "97", "hashOfConfig": "83"}, {"size": 1141, "mtime": 1754081315367, "results": "98", "hashOfConfig": "83"}, {"size": 1144, "mtime": 1754081314059, "results": "99", "hashOfConfig": "83"}, {"size": 802, "mtime": 1754081313756, "results": "100", "hashOfConfig": "83"}, {"size": 2793, "mtime": 1754081315074, "results": "101", "hashOfConfig": "83"}, {"size": 616, "mtime": 1754081315439, "results": "102", "hashOfConfig": "83"}, {"size": 1198, "mtime": 1754081314528, "results": "103", "hashOfConfig": "83"}, {"size": 1206, "mtime": 1754081315500, "results": "104", "hashOfConfig": "83"}, {"size": 817, "mtime": 1754081314283, "results": "105", "hashOfConfig": "83"}, {"size": 452, "mtime": 1754081315300, "results": "106", "hashOfConfig": "83"}, {"size": 616, "mtime": 1754081314775, "results": "107", "hashOfConfig": "83"}, {"size": 194, "mtime": 1754082811071, "results": "108", "hashOfConfig": "83"}, {"size": 191, "mtime": 1754081311612, "results": "109", "hashOfConfig": "83"}, {"size": 1921, "mtime": 1754084722915, "results": "110", "hashOfConfig": "83"}, {"size": 4615, "mtime": 1754085271170, "results": "111", "hashOfConfig": "83"}, {"size": 5960, "mtime": 1754081313323, "results": "112", "hashOfConfig": "83"}, {"size": 7852, "mtime": 1754081311376, "results": "113", "hashOfConfig": "83"}, {"size": 778, "mtime": 1754081311002, "results": "114", "hashOfConfig": "83"}, {"size": 1298, "mtime": 1754081312963, "results": "115", "hashOfConfig": "83"}, {"size": 1119, "mtime": 1754081312249, "results": "116", "hashOfConfig": "83"}, {"size": 1148, "mtime": 1754081312332, "results": "117", "hashOfConfig": "83"}, {"size": 2240, "mtime": 1754081309380, "results": "118", "hashOfConfig": "83"}, {"size": 9327, "mtime": 1754081309801, "results": "119", "hashOfConfig": "83"}, {"size": 696, "mtime": 1754081311228, "results": "120", "hashOfConfig": "83"}, {"size": 2406, "mtime": 1754081311300, "results": "121", "hashOfConfig": "83"}, {"size": 2057, "mtime": 1754081314692, "results": "122", "hashOfConfig": "83"}, {"size": 5172, "mtime": 1754081314451, "results": "123", "hashOfConfig": "83"}, {"size": 1501, "mtime": 1754085332870, "results": "124", "hashOfConfig": "83"}, {"size": 3879, "mtime": 1754085202351, "results": "125", "hashOfConfig": "83"}, {"size": 3646, "mtime": 1754081313198, "results": "126", "hashOfConfig": "83"}, {"size": 1648, "mtime": 1754081311082, "results": "127", "hashOfConfig": "83"}, {"size": 2725, "mtime": 1754081311152, "results": "128", "hashOfConfig": "83"}, {"size": 846, "mtime": 1754081313112, "results": "129", "hashOfConfig": "83"}, {"size": 2223, "mtime": 1754081313033, "results": "130", "hashOfConfig": "83"}, {"size": 807, "mtime": 1754081312846, "results": "131", "hashOfConfig": "83"}, {"size": 543, "mtime": 1754081312664, "results": "132", "hashOfConfig": "83"}, {"size": 129, "mtime": 1754081312423, "results": "133", "hashOfConfig": "83"}, {"size": 261, "mtime": 1754081312610, "results": "134", "hashOfConfig": "83"}, {"size": 147, "mtime": 1754081312728, "results": "135", "hashOfConfig": "83"}, {"size": 132, "mtime": 1754081312903, "results": "136", "hashOfConfig": "83"}, {"size": 1608, "mtime": 1754081315237, "results": "137", "hashOfConfig": "83"}, {"size": 2436, "mtime": 1754081309450, "results": "138", "hashOfConfig": "83"}, {"size": 6910, "mtime": 1754081309515, "results": "139", "hashOfConfig": "83"}, {"size": 556, "mtime": 1754081310919, "results": "140", "hashOfConfig": "83"}, {"size": 524, "mtime": 1754081309871, "results": "141", "hashOfConfig": "83"}, {"size": 251, "mtime": 1754081310579, "results": "142", "hashOfConfig": "83"}, {"size": 215, "mtime": 1754081309581, "results": "143", "hashOfConfig": "83"}, {"size": 9518, "mtime": 1754081310522, "results": "144", "hashOfConfig": "83"}, {"size": 607, "mtime": 1754081310301, "results": "145", "hashOfConfig": "83"}, {"size": 1258, "mtime": 1754081310826, "results": "146", "hashOfConfig": "83"}, {"size": 4428, "mtime": 1754081310099, "results": "147", "hashOfConfig": "83"}, {"size": 129, "mtime": 1754081309956, "results": "148", "hashOfConfig": "83"}, {"size": 1538, "mtime": 1754081312023, "results": "149", "hashOfConfig": "83"}, {"size": 306, "mtime": 1754081311527, "results": "150", "hashOfConfig": "83"}, {"size": 447, "mtime": 1754081311723, "results": "151", "hashOfConfig": "83"}, {"size": 705, "mtime": 1754081312789, "results": "152", "hashOfConfig": "83"}, {"size": 1398, "mtime": 1754081312518, "results": "153", "hashOfConfig": "83"}, {"size": 3285, "mtime": 1754081314220, "results": "154", "hashOfConfig": "83"}, {"size": 3746, "mtime": 1754081310654, "results": "155", "hashOfConfig": "83"}, {"size": 4978, "mtime": 1754081310749, "results": "156", "hashOfConfig": "83"}, {"size": 2800, "mtime": 1754081309644, "results": "157", "hashOfConfig": "83"}, {"size": 2954, "mtime": 1754081310367, "results": "158", "hashOfConfig": "83"}, {"size": 3944, "mtime": 1754081310436, "results": "159", "hashOfConfig": "83"}, {"size": 1496, "mtime": 1754081309722, "results": "160", "hashOfConfig": "83"}, {"size": 1562, "mtime": 1754081310173, "results": "161", "hashOfConfig": "83"}, {"size": 2146, "mtime": 1754081310242, "results": "162", "hashOfConfig": "83"}, {"size": 14963, "mtime": 1754085063097, "results": "163", "hashOfConfig": "83"}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1yaasqg", {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\App.js", ["407", "408"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\store.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Theme\\DarkTheme.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Card\\action.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Auth\\Action.js", ["409", "410"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Restaurant\\action.js", ["411"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Routes\\Routers.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Auth\\Reducer.js", ["412", "413"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Card\\reducer.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Restaurant\\reducer.js", ["414"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Menu\\reducer.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Ingredients\\reducer.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Order\\reducer.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\RestaurantOrder\\reducer.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Card\\actionType.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Auth\\ActionType.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Restaurant\\actionType.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Routes\\AdminRoute.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Menu\\actionType.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Routes\\CustomerRoute.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Ingredients\\actionType.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\RestaurantOrder\\actionType.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Order\\actionType.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Config\\api.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Config\\config.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Navbar\\Navbar.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Home\\Home.jsx", ["415", "416", "417", "418"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Restaurant\\RestaurantDetails.jsx", ["419", "420", "421", "422", "423", "424"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Card\\Cart.jsx", ["425"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Auth\\Auth.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Profile.jsx", ["426"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\PaymentFail\\PaymentFail.jsx", ["427"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\PaymentSuccess\\PaymentSuccess.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Admin\\Admin.jsx", ["428", "429", "430", "431", "432"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\CreateRestaurantForm\\CreateRestaurantForm.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Card\\AddressCard.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Card\\CardItem.jsx", ["433", "434"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Order\\action.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Menu\\action.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Home\\MultiItemCarousel.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Restaurant\\RestaurantCard.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Restaurant\\MenuCard.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Auth\\LoginForm.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Auth\\RegisterForm.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\UserProfile.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\ProfileNav.jsx", ["435"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Orders.jsx", ["436", "437"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Favorites.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Address.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Events.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Notifications.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\Payments.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\RestaurantOrder\\action.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Admin\\AdminSidebar.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Admin\\RestaurantDetails.jsx", ["438"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Util\\UploadToCloudinary.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Dashboard\\Dashboard.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Menu\\Menu.jsx", ["439", "440"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Category\\Category.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Menu\\CreateMenuForm.jsx", ["441"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Ingredients\\Ingredients.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Orders\\Orders.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Event\\Event.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Details\\Details.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Home\\TopMeel.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Config\\catogorizeIngredients.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Home\\CarouselItem.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\OrderCard.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\Profile\\EvenetCard.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Components\\State\\Ingredients\\action.js", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Menu\\MenuTable.jsx", ["442", "443", "444", "445", "446"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Orders\\OrderTable.jsx", ["447", "448"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Category\\CategoryTable.jsx", ["449", "450", "451", "452", "453", "454", "455"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Ingredients\\IngredientsCategoryTable.jsx", ["456", "457", "458", "459", "460"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Ingredients\\IngredientsTable.jsx", ["461", "462", "463", "464"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Category\\CreateCategoryForm.jsx", ["465"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Ingredients\\CreateIngredientCategoryForm.jsx", ["466", "467", "468", "469"], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\AdminComponents\\Ingredients\\CreateIngredientForm.jsx", [], [], "C:\\Users\\<USER>\\IdeaProjects\\FoodOrdering-main\\FoodOrdering-main\\frontend\\src\\Data\\RestaurantSeedData.js", [], [], {"ruleId": "470", "severity": 1, "message": "471", "line": 19, "column": 5, "nodeType": "472", "endLine": 19, "endColumn": 17, "suggestions": "473"}, {"ruleId": "470", "severity": 1, "message": "474", "line": 24, "column": 5, "nodeType": "472", "endLine": 24, "endColumn": 16, "suggestions": "475"}, {"ruleId": "476", "severity": 1, "message": "477", "line": 1, "column": 8, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 13}, {"ruleId": "476", "severity": 1, "message": "480", "line": 4, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 4, "endColumn": 14}, {"ruleId": "476", "severity": 1, "message": "480", "line": 1, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 14}, {"ruleId": "481", "severity": 1, "message": "482", "line": 30, "column": 5, "nodeType": "483", "messageId": "484", "endLine": 31, "endColumn": 72}, {"ruleId": "481", "severity": 1, "message": "482", "line": 66, "column": 5, "nodeType": "483", "messageId": "484", "endLine": 67, "endColumn": 82}, {"ruleId": "481", "severity": 1, "message": "482", "line": 100, "column": 5, "nodeType": "483", "messageId": "484", "endLine": 108, "endColumn": 9}, {"ruleId": "476", "severity": 1, "message": "485", "line": 5, "column": 8, "nodeType": "478", "messageId": "479", "endLine": 5, "endColumn": 12}, {"ruleId": "476", "severity": 1, "message": "486", "line": 9, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 9, "endColumn": 18}, {"ruleId": "476", "severity": 1, "message": "487", "line": 15, "column": 11, "nodeType": "478", "messageId": "479", "endLine": 15, "endColumn": 19}, {"ruleId": "470", "severity": 1, "message": "488", "line": 33, "column": 7, "nodeType": "472", "endLine": 33, "endColumn": 9, "suggestions": "489"}, {"ruleId": "476", "severity": 1, "message": "487", "line": 34, "column": 9, "nodeType": "478", "messageId": "479", "endLine": 34, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "490", "line": 40, "column": 11, "nodeType": "478", "messageId": "479", "endLine": 40, "endColumn": 15}, {"ruleId": "476", "severity": 1, "message": "491", "line": 45, "column": 13, "nodeType": "478", "messageId": "479", "endLine": 45, "endColumn": 17}, {"ruleId": "470", "severity": 1, "message": "492", "line": 64, "column": 5, "nodeType": "472", "endLine": 64, "endColumn": 7, "suggestions": "493"}, {"ruleId": "470", "severity": 1, "message": "492", "line": 75, "column": 5, "nodeType": "472", "endLine": 75, "endColumn": 32, "suggestions": "494"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 79, "column": 9, "nodeType": "497", "endLine": 79, "endColumn": 50}, {"ruleId": "476", "severity": 1, "message": "498", "line": 44, "column": 7, "nodeType": "478", "messageId": "479", "endLine": 44, "endColumn": 12}, {"ruleId": "476", "severity": 1, "message": "499", "line": 15, "column": 24, "nodeType": "478", "messageId": "479", "endLine": 15, "endColumn": 38}, {"ruleId": "476", "severity": 1, "message": "500", "line": 3, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 3, "endColumn": 15}, {"ruleId": "476", "severity": 1, "message": "501", "line": 10, "column": 9, "nodeType": "478", "messageId": "479", "endLine": 10, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "502", "line": 14, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 14, "endColumn": 27}, {"ruleId": "476", "severity": 1, "message": "503", "line": 15, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 15, "endColumn": 36}, {"ruleId": "476", "severity": 1, "message": "504", "line": 16, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 16, "endColumn": 22}, {"ruleId": "470", "severity": 1, "message": "505", "line": 30, "column": 5, "nodeType": "472", "endLine": 30, "endColumn": 7, "suggestions": "506"}, {"ruleId": "476", "severity": 1, "message": "507", "line": 11, "column": 15, "nodeType": "478", "messageId": "479", "endLine": 11, "endColumn": 19}, {"ruleId": "476", "severity": 1, "message": "487", "line": 12, "column": 9, "nodeType": "478", "messageId": "479", "endLine": 12, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "508", "line": 5, "column": 8, "nodeType": "478", "messageId": "479", "endLine": 5, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "507", "line": 7, "column": 11, "nodeType": "478", "messageId": "479", "endLine": 7, "endColumn": 15}, {"ruleId": "470", "severity": 1, "message": "509", "line": 13, "column": 6, "nodeType": "472", "endLine": 13, "endColumn": 18, "suggestions": "510"}, {"ruleId": "476", "severity": 1, "message": "511", "line": 7, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 7, "endColumn": 13}, {"ruleId": "476", "severity": 1, "message": "512", "line": 1, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 14}, {"ruleId": "476", "severity": 1, "message": "513", "line": 1, "column": 16, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 26}, {"ruleId": "470", "severity": 1, "message": "514", "line": 62, "column": 6, "nodeType": "472", "endLine": 62, "endColumn": 8, "suggestions": "515"}, {"ruleId": "476", "severity": 1, "message": "516", "line": 5, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 5, "endColumn": 14}, {"ruleId": "476", "severity": 1, "message": "503", "line": 25, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 25, "endColumn": 29}, {"ruleId": "476", "severity": 1, "message": "517", "line": 28, "column": 7, "nodeType": "478", "messageId": "479", "endLine": 28, "endColumn": 13}, {"ruleId": "476", "severity": 1, "message": "518", "line": 32, "column": 23, "nodeType": "478", "messageId": "479", "endLine": 32, "endColumn": 33}, {"ruleId": "470", "severity": 1, "message": "519", "line": 41, "column": 6, "nodeType": "472", "endLine": 41, "endColumn": 8, "suggestions": "520"}, {"ruleId": "476", "severity": 1, "message": "518", "line": 43, "column": 23, "nodeType": "478", "messageId": "479", "endLine": 43, "endColumn": 33}, {"ruleId": "470", "severity": 1, "message": "521", "line": 54, "column": 6, "nodeType": "472", "endLine": 54, "endColumn": 8, "suggestions": "522"}, {"ruleId": "476", "severity": 1, "message": "516", "line": 4, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 4, "endColumn": 14}, {"ruleId": "476", "severity": 1, "message": "513", "line": 8, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 8, "endColumn": 13}, {"ruleId": "476", "severity": 1, "message": "523", "line": 19, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 19, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "517", "line": 34, "column": 7, "nodeType": "478", "messageId": "479", "endLine": 34, "endColumn": 13}, {"ruleId": "476", "severity": 1, "message": "524", "line": 38, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 38, "endColumn": 18}, {"ruleId": "476", "severity": 1, "message": "525", "line": 40, "column": 9, "nodeType": "478", "messageId": "479", "endLine": 40, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "487", "line": 42, "column": 9, "nodeType": "478", "messageId": "479", "endLine": 42, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "516", "line": 1, "column": 21, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 32}, {"ruleId": "476", "severity": 1, "message": "523", "line": 11, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 11, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "526", "line": 12, "column": 8, "nodeType": "478", "messageId": "479", "endLine": 12, "endColumn": 28}, {"ruleId": "476", "severity": 1, "message": "517", "line": 17, "column": 7, "nodeType": "478", "messageId": "479", "endLine": 17, "endColumn": 13}, {"ruleId": "470", "severity": 1, "message": "514", "line": 41, "column": 5, "nodeType": "472", "endLine": 41, "endColumn": 7, "suggestions": "527"}, {"ruleId": "476", "severity": 1, "message": "516", "line": 5, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 5, "endColumn": 14}, {"ruleId": "476", "severity": 1, "message": "523", "line": 19, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 19, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "517", "line": 27, "column": 7, "nodeType": "478", "messageId": "479", "endLine": 27, "endColumn": 13}, {"ruleId": "470", "severity": 1, "message": "514", "line": 56, "column": 6, "nodeType": "472", "endLine": 56, "endColumn": 8, "suggestions": "528"}, {"ruleId": "476", "severity": 1, "message": "529", "line": 8, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 8, "endColumn": 20}, {"ruleId": "476", "severity": 1, "message": "530", "line": 1, "column": 18, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 29}, {"ruleId": "476", "severity": 1, "message": "531", "line": 1, "column": 31, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 41}, {"ruleId": "476", "severity": 1, "message": "532", "line": 1, "column": 43, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 51}, {"ruleId": "476", "severity": 1, "message": "533", "line": 1, "column": 53, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 59}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch' and 'jwt'. Either include them or remove the dependency array.", "ArrayExpression", ["534"], "React Hook useEffect has missing dependencies: 'auth.token', 'dispatch', and 'jwt'. Either include them or remove the dependency array.", ["535"], "no-unused-vars", "'axios' is defined but never used.", "Identifier", "unusedVar", "'type' is defined but never used.", "no-duplicate-case", "Duplicate case label.", "SwitchCase", "unexpected", "'Auth' is defined but never used.", "'findCard' is defined but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch', 'jwt', and 'restaurant.restaurants'. Either include them or remove the dependency array.", ["536"], "'auth' is assigned a value but never used.", "'city' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch', 'id', and 'jwt'. Either include them or remove the dependency array.", ["537"], ["538"], "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement", "'items' is assigned a value but never used.", "'setOpenSidebar' is assigned a value but never used.", "'green' is defined but never used.", "'Details' is defined but never used.", "'getRestaurantById' is defined but never used.", "'getMenuItemsByRestaurantId' is defined but never used.", "'getUserOrder' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch', 'restaurant.usersRestaurant?.id', and 'token'. Either include them or remove the dependency array.", ["539"], "'card' is assigned a value but never used.", "'HomeIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'token'. Either include them or remove the dependency array.", ["540"], "'IconButton' is defined but never used.", "'Card' is defined but never used.", "'Typography' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch', 'jwt', and 'restaurant.usersRestaurant.id'. Either include them or remove the dependency array.", ["541"], "'CardActions' is defined but never used.", "'orders' is assigned a value but never used.", "'ingredient' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'restaurant.usersRestaurant.id'. Either include them or remove the dependency array.", ["542"], "React Hook useEffect has missing dependencies: 'dispatch', 'jwt', and 'restaurant.usersRestaurant?.id'. Either include them or remove the dependency array.", ["543"], "'Delete' is defined but never used.", "'category' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "'CreateIngredientForm' is defined but never used.", ["544"], ["545"], "'restaurant' is assigned a value but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'MenuItem' is defined but never used.", "'Select' is defined but never used.", {"desc": "546", "fix": "547"}, {"desc": "548", "fix": "549"}, {"desc": "550", "fix": "551"}, {"desc": "552", "fix": "553"}, {"desc": "554", "fix": "555"}, {"desc": "556", "fix": "557"}, {"desc": "558", "fix": "559"}, {"desc": "560", "fix": "561"}, {"desc": "562", "fix": "563"}, {"desc": "564", "fix": "565"}, {"desc": "560", "fix": "566"}, {"desc": "560", "fix": "567"}, "Update the dependencies array to be: [auth.token, dispatch, jwt]", {"range": "568", "text": "569"}, "Update the dependencies array to be: [auth.token, auth.user, dispatch, jwt]", {"range": "570", "text": "571"}, "Update the dependencies array to be: [dispatch, jwt, restaurant.restaurants]", {"range": "572", "text": "573"}, "Update the dependencies array to be: [dispatch, id, jwt]", {"range": "574", "text": "575"}, "Update the dependencies array to be: [selectedCategory, foodType, dispatch, jwt, id]", {"range": "576", "text": "577"}, "Update the dependencies array to be: [dispatch, restaurant.usersRestaurant?.id, token]", {"range": "578", "text": "579"}, "Update the dependencies array to be: [auth.token, dispatch, token]", {"range": "580", "text": "581"}, "Update the dependencies array to be: [dispatch, jwt, restaurant.usersRestaurant.id]", {"range": "582", "text": "583"}, "Update the dependencies array to be: [dispatch, restaurant.usersRestaurant.id]", {"range": "584", "text": "585"}, "Update the dependencies array to be: [dispatch, jwt, restaurant.usersRestaurant?.id]", {"range": "586", "text": "587"}, {"range": "588", "text": "583"}, {"range": "589", "text": "583"}, [683, 695], "[auth.token, dispatch, jwt]", [781, 792], "[auth.token, auth.user, dispatch, jwt]", [1289, 1291], "[dispatch, jwt, restaurant.restaurants]", [1914, 1916], "[dispatch, id, jwt]", [2281, 2308], "[selectedCategory, foodType, dispatch, jwt, id]", [1414, 1416], "[dispatch, restaurant.usersRestaurant?.id, token]", [425, 437], "[auth.token, dispatch, token]", [1665, 1667], "[dispatch, jwt, restaurant.usersRestaurant.id]", [1217, 1219], "[dispatch, restaurant.usersRestaurant.id]", [1461, 1463], "[dispatch, jwt, restaurant.usersRestaurant?.id]", [1509, 1511], [1539, 1541]]