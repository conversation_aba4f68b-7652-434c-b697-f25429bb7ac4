{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, '__esModule', {\n  value: true\n});\nexports.printText = exports.printProps = exports.printElementAsLeaf = exports.printElement = exports.printComment = exports.printChildren = void 0;\nvar _escapeHTML = _interopRequireDefault(require('./escapeHTML'));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n// Return empty string if keys is empty.\nconst printProps = (keys, props, config, indentation, depth, refs, printer) => {\n  const indentationNext = indentation + config.indent;\n  const colors = config.colors;\n  return keys.map(key => {\n    const value = props[key];\n    let printed = printer(value, config, indentationNext, depth, refs);\n    if (typeof value !== 'string') {\n      if (printed.indexOf('\\n') !== -1) {\n        printed = config.spacingOuter + indentationNext + printed + config.spacingOuter + indentation;\n      }\n      printed = '{' + printed + '}';\n    }\n    return config.spacingInner + indentation + colors.prop.open + key + colors.prop.close + '=' + colors.value.open + printed + colors.value.close;\n  }).join('');\n}; // Return empty string if children is empty.\n\nexports.printProps = printProps;\nconst printChildren = (children, config, indentation, depth, refs, printer) => children.map(child => config.spacingOuter + indentation + (typeof child === 'string' ? printText(child, config) : printer(child, config, indentation, depth, refs))).join('');\nexports.printChildren = printChildren;\nconst printText = (text, config) => {\n  const contentColor = config.colors.content;\n  return contentColor.open + (0, _escapeHTML.default)(text) + contentColor.close;\n};\nexports.printText = printText;\nconst printComment = (comment, config) => {\n  const commentColor = config.colors.comment;\n  return commentColor.open + '<!--' + (0, _escapeHTML.default)(comment) + '-->' + commentColor.close;\n}; // Separate the functions to format props, children, and element,\n// so a plugin could override a particular function, if needed.\n// Too bad, so sad: the traditional (but unnecessary) space\n// in a self-closing tagColor requires a second test of printedProps.\n\nexports.printComment = printComment;\nconst printElement = (type, printedProps, printedChildren, config, indentation) => {\n  const tagColor = config.colors.tag;\n  return tagColor.open + '<' + type + (printedProps && tagColor.close + printedProps + config.spacingOuter + indentation + tagColor.open) + (printedChildren ? '>' + tagColor.close + printedChildren + config.spacingOuter + indentation + tagColor.open + '</' + type : (printedProps && !config.min ? '' : ' ') + '/') + '>' + tagColor.close;\n};\nexports.printElement = printElement;\nconst printElementAsLeaf = (type, config) => {\n  const tagColor = config.colors.tag;\n  return tagColor.open + '<' + type + tagColor.close + ' …' + tagColor.open + ' />' + tagColor.close;\n};\nexports.printElementAsLeaf = printElementAsLeaf;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "printText", "printProps", "printElementAsLeaf", "printElement", "printComment", "printChildren", "_escapeHTML", "_interopRequireDefault", "require", "obj", "__esModule", "default", "keys", "props", "config", "indentation", "depth", "refs", "printer", "indentationNext", "indent", "colors", "map", "key", "printed", "indexOf", "spacingOuter", "spacingInner", "prop", "open", "close", "join", "children", "child", "text", "contentColor", "content", "comment", "commentColor", "type", "printedProps", "printedChildren", "tagColor", "tag", "min"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/pretty-format/build/plugins/lib/markup.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, '__esModule', {\n  value: true\n});\nexports.printText =\n  exports.printProps =\n  exports.printElementAsLeaf =\n  exports.printElement =\n  exports.printComment =\n  exports.printChildren =\n    void 0;\n\nvar _escapeHTML = _interopRequireDefault(require('./escapeHTML'));\n\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {default: obj};\n}\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n// Return empty string if keys is empty.\nconst printProps = (keys, props, config, indentation, depth, refs, printer) => {\n  const indentationNext = indentation + config.indent;\n  const colors = config.colors;\n  return keys\n    .map(key => {\n      const value = props[key];\n      let printed = printer(value, config, indentationNext, depth, refs);\n\n      if (typeof value !== 'string') {\n        if (printed.indexOf('\\n') !== -1) {\n          printed =\n            config.spacingOuter +\n            indentationNext +\n            printed +\n            config.spacingOuter +\n            indentation;\n        }\n\n        printed = '{' + printed + '}';\n      }\n\n      return (\n        config.spacingInner +\n        indentation +\n        colors.prop.open +\n        key +\n        colors.prop.close +\n        '=' +\n        colors.value.open +\n        printed +\n        colors.value.close\n      );\n    })\n    .join('');\n}; // Return empty string if children is empty.\n\nexports.printProps = printProps;\n\nconst printChildren = (children, config, indentation, depth, refs, printer) =>\n  children\n    .map(\n      child =>\n        config.spacingOuter +\n        indentation +\n        (typeof child === 'string'\n          ? printText(child, config)\n          : printer(child, config, indentation, depth, refs))\n    )\n    .join('');\n\nexports.printChildren = printChildren;\n\nconst printText = (text, config) => {\n  const contentColor = config.colors.content;\n  return (\n    contentColor.open + (0, _escapeHTML.default)(text) + contentColor.close\n  );\n};\n\nexports.printText = printText;\n\nconst printComment = (comment, config) => {\n  const commentColor = config.colors.comment;\n  return (\n    commentColor.open +\n    '<!--' +\n    (0, _escapeHTML.default)(comment) +\n    '-->' +\n    commentColor.close\n  );\n}; // Separate the functions to format props, children, and element,\n// so a plugin could override a particular function, if needed.\n// Too bad, so sad: the traditional (but unnecessary) space\n// in a self-closing tagColor requires a second test of printedProps.\n\nexports.printComment = printComment;\n\nconst printElement = (\n  type,\n  printedProps,\n  printedChildren,\n  config,\n  indentation\n) => {\n  const tagColor = config.colors.tag;\n  return (\n    tagColor.open +\n    '<' +\n    type +\n    (printedProps &&\n      tagColor.close +\n        printedProps +\n        config.spacingOuter +\n        indentation +\n        tagColor.open) +\n    (printedChildren\n      ? '>' +\n        tagColor.close +\n        printedChildren +\n        config.spacingOuter +\n        indentation +\n        tagColor.open +\n        '</' +\n        type\n      : (printedProps && !config.min ? '' : ' ') + '/') +\n    '>' +\n    tagColor.close\n  );\n};\n\nexports.printElement = printElement;\n\nconst printElementAsLeaf = (type, config) => {\n  const tagColor = config.colors.tag;\n  return (\n    tagColor.open +\n    '<' +\n    type +\n    tagColor.close +\n    ' …' +\n    tagColor.open +\n    ' />' +\n    tagColor.close\n  );\n};\n\nexports.printElementAsLeaf = printElementAsLeaf;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,SAAS,GACfF,OAAO,CAACG,UAAU,GAClBH,OAAO,CAACI,kBAAkB,GAC1BJ,OAAO,CAACK,YAAY,GACpBL,OAAO,CAACM,YAAY,GACpBN,OAAO,CAACO,aAAa,GACnB,KAAK,CAAC;AAEV,IAAIC,WAAW,GAAGC,sBAAsB,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;AAEjE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EACnC,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAACE,OAAO,EAAEF;EAAG,CAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMR,UAAU,GAAGA,CAACW,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,KAAK;EAC7E,MAAMC,eAAe,GAAGJ,WAAW,GAAGD,MAAM,CAACM,MAAM;EACnD,MAAMC,MAAM,GAAGP,MAAM,CAACO,MAAM;EAC5B,OAAOT,IAAI,CACRU,GAAG,CAACC,GAAG,IAAI;IACV,MAAMxB,KAAK,GAAGc,KAAK,CAACU,GAAG,CAAC;IACxB,IAAIC,OAAO,GAAGN,OAAO,CAACnB,KAAK,EAAEe,MAAM,EAAEK,eAAe,EAAEH,KAAK,EAAEC,IAAI,CAAC;IAElE,IAAI,OAAOlB,KAAK,KAAK,QAAQ,EAAE;MAC7B,IAAIyB,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAChCD,OAAO,GACLV,MAAM,CAACY,YAAY,GACnBP,eAAe,GACfK,OAAO,GACPV,MAAM,CAACY,YAAY,GACnBX,WAAW;MACf;MAEAS,OAAO,GAAG,GAAG,GAAGA,OAAO,GAAG,GAAG;IAC/B;IAEA,OACEV,MAAM,CAACa,YAAY,GACnBZ,WAAW,GACXM,MAAM,CAACO,IAAI,CAACC,IAAI,GAChBN,GAAG,GACHF,MAAM,CAACO,IAAI,CAACE,KAAK,GACjB,GAAG,GACHT,MAAM,CAACtB,KAAK,CAAC8B,IAAI,GACjBL,OAAO,GACPH,MAAM,CAACtB,KAAK,CAAC+B,KAAK;EAEtB,CAAC,CAAC,CACDC,IAAI,CAAC,EAAE,CAAC;AACb,CAAC,CAAC,CAAC;;AAEHjC,OAAO,CAACG,UAAU,GAAGA,UAAU;AAE/B,MAAMI,aAAa,GAAGA,CAAC2B,QAAQ,EAAElB,MAAM,EAAEC,WAAW,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,KACxEc,QAAQ,CACLV,GAAG,CACFW,KAAK,IACHnB,MAAM,CAACY,YAAY,GACnBX,WAAW,IACV,OAAOkB,KAAK,KAAK,QAAQ,GACtBjC,SAAS,CAACiC,KAAK,EAAEnB,MAAM,CAAC,GACxBI,OAAO,CAACe,KAAK,EAAEnB,MAAM,EAAEC,WAAW,EAAEC,KAAK,EAAEC,IAAI,CAAC,CACxD,CAAC,CACAc,IAAI,CAAC,EAAE,CAAC;AAEbjC,OAAO,CAACO,aAAa,GAAGA,aAAa;AAErC,MAAML,SAAS,GAAGA,CAACkC,IAAI,EAAEpB,MAAM,KAAK;EAClC,MAAMqB,YAAY,GAAGrB,MAAM,CAACO,MAAM,CAACe,OAAO;EAC1C,OACED,YAAY,CAACN,IAAI,GAAG,CAAC,CAAC,EAAEvB,WAAW,CAACK,OAAO,EAAEuB,IAAI,CAAC,GAAGC,YAAY,CAACL,KAAK;AAE3E,CAAC;AAEDhC,OAAO,CAACE,SAAS,GAAGA,SAAS;AAE7B,MAAMI,YAAY,GAAGA,CAACiC,OAAO,EAAEvB,MAAM,KAAK;EACxC,MAAMwB,YAAY,GAAGxB,MAAM,CAACO,MAAM,CAACgB,OAAO;EAC1C,OACEC,YAAY,CAACT,IAAI,GACjB,MAAM,GACN,CAAC,CAAC,EAAEvB,WAAW,CAACK,OAAO,EAAE0B,OAAO,CAAC,GACjC,KAAK,GACLC,YAAY,CAACR,KAAK;AAEtB,CAAC,CAAC,CAAC;AACH;AACA;AACA;;AAEAhC,OAAO,CAACM,YAAY,GAAGA,YAAY;AAEnC,MAAMD,YAAY,GAAGA,CACnBoC,IAAI,EACJC,YAAY,EACZC,eAAe,EACf3B,MAAM,EACNC,WAAW,KACR;EACH,MAAM2B,QAAQ,GAAG5B,MAAM,CAACO,MAAM,CAACsB,GAAG;EAClC,OACED,QAAQ,CAACb,IAAI,GACb,GAAG,GACHU,IAAI,IACHC,YAAY,IACXE,QAAQ,CAACZ,KAAK,GACZU,YAAY,GACZ1B,MAAM,CAACY,YAAY,GACnBX,WAAW,GACX2B,QAAQ,CAACb,IAAI,CAAC,IACjBY,eAAe,GACZ,GAAG,GACHC,QAAQ,CAACZ,KAAK,GACdW,eAAe,GACf3B,MAAM,CAACY,YAAY,GACnBX,WAAW,GACX2B,QAAQ,CAACb,IAAI,GACb,IAAI,GACJU,IAAI,GACJ,CAACC,YAAY,IAAI,CAAC1B,MAAM,CAAC8B,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC,GACnD,GAAG,GACHF,QAAQ,CAACZ,KAAK;AAElB,CAAC;AAEDhC,OAAO,CAACK,YAAY,GAAGA,YAAY;AAEnC,MAAMD,kBAAkB,GAAGA,CAACqC,IAAI,EAAEzB,MAAM,KAAK;EAC3C,MAAM4B,QAAQ,GAAG5B,MAAM,CAACO,MAAM,CAACsB,GAAG;EAClC,OACED,QAAQ,CAACb,IAAI,GACb,GAAG,GACHU,IAAI,GACJG,QAAQ,CAACZ,KAAK,GACd,IAAI,GACJY,QAAQ,CAACb,IAAI,GACb,KAAK,GACLa,QAAQ,CAACZ,KAAK;AAElB,CAAC;AAEDhC,OAAO,CAACI,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}