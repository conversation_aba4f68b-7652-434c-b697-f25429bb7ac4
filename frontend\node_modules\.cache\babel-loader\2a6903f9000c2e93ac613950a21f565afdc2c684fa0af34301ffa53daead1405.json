{"ast": null, "code": "var MediaQuery = require('./MediaQuery');\nvar Util = require('./Util');\nvar each = Util.each;\nvar isFunction = Util.isFunction;\nvar isArray = Util.isArray;\n\n/**\n * Allows for registration of query handlers.\n * Manages the query handler's state and is responsible for wiring up browser events\n *\n * @constructor\n */\nfunction MediaQueryDispatch() {\n  if (!window.matchMedia) {\n    throw new Error('matchMedia not present, legacy browsers require a polyfill');\n  }\n  this.queries = {};\n  this.browserIsIncapable = !window.matchMedia('only all').matches;\n}\nMediaQueryDispatch.prototype = {\n  constructor: MediaQueryDispatch,\n  /**\n   * Registers a handler for the given media query\n   *\n   * @param {string} q the media query\n   * @param {object || Array || Function} options either a single query handler object, a function, or an array of query handlers\n   * @param {function} options.match fired when query matched\n   * @param {function} [options.unmatch] fired when a query is no longer matched\n   * @param {function} [options.setup] fired when handler first triggered\n   * @param {boolean} [options.deferSetup=false] whether setup should be run immediately or deferred until query is first matched\n   * @param {boolean} [shouldDegrade=false] whether this particular media query should always run on incapable browsers\n   */\n  register: function (q, options, shouldDegrade) {\n    var queries = this.queries,\n      isUnconditional = shouldDegrade && this.browserIsIncapable;\n    if (!queries[q]) {\n      queries[q] = new MediaQuery(q, isUnconditional);\n    }\n\n    //normalise to object in an array\n    if (isFunction(options)) {\n      options = {\n        match: options\n      };\n    }\n    if (!isArray(options)) {\n      options = [options];\n    }\n    each(options, function (handler) {\n      if (isFunction(handler)) {\n        handler = {\n          match: handler\n        };\n      }\n      queries[q].addHandler(handler);\n    });\n    return this;\n  },\n  /**\n   * unregisters a query and all it's handlers, or a specific handler for a query\n   *\n   * @param {string} q the media query to target\n   * @param {object || function} [handler] specific handler to unregister\n   */\n  unregister: function (q, handler) {\n    var query = this.queries[q];\n    if (query) {\n      if (handler) {\n        query.removeHandler(handler);\n      } else {\n        query.clear();\n        delete this.queries[q];\n      }\n    }\n    return this;\n  }\n};\nmodule.exports = MediaQueryDispatch;", "map": {"version": 3, "names": ["MediaQuery", "require", "<PERSON><PERSON>", "each", "isFunction", "isArray", "MediaQueryDispatch", "window", "matchMedia", "Error", "queries", "browserIsIncapable", "matches", "prototype", "constructor", "register", "q", "options", "<PERSON><PERSON><PERSON><PERSON>", "isUnconditional", "match", "handler", "add<PERSON><PERSON><PERSON>", "unregister", "query", "<PERSON><PERSON><PERSON><PERSON>", "clear", "module", "exports"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/enquire.js/src/MediaQueryDispatch.js"], "sourcesContent": ["var MediaQuery = require('./MediaQuery');\nvar Util = require('./Util');\nvar each = Util.each;\nvar isFunction = Util.isFunction;\nvar isArray = Util.isArray;\n\n/**\n * Allows for registration of query handlers.\n * Manages the query handler's state and is responsible for wiring up browser events\n *\n * @constructor\n */\nfunction MediaQueryDispatch () {\n    if(!window.matchMedia) {\n        throw new Error('matchMedia not present, legacy browsers require a polyfill');\n    }\n\n    this.queries = {};\n    this.browserIsIncapable = !window.matchMedia('only all').matches;\n}\n\nMediaQueryDispatch.prototype = {\n\n    constructor : MediaQueryDispatch,\n\n    /**\n     * Registers a handler for the given media query\n     *\n     * @param {string} q the media query\n     * @param {object || Array || Function} options either a single query handler object, a function, or an array of query handlers\n     * @param {function} options.match fired when query matched\n     * @param {function} [options.unmatch] fired when a query is no longer matched\n     * @param {function} [options.setup] fired when handler first triggered\n     * @param {boolean} [options.deferSetup=false] whether setup should be run immediately or deferred until query is first matched\n     * @param {boolean} [shouldDegrade=false] whether this particular media query should always run on incapable browsers\n     */\n    register : function(q, options, shouldDegrade) {\n        var queries         = this.queries,\n            isUnconditional = shouldDegrade && this.browserIsIncapable;\n\n        if(!queries[q]) {\n            queries[q] = new MediaQuery(q, isUnconditional);\n        }\n\n        //normalise to object in an array\n        if(isFunction(options)) {\n            options = { match : options };\n        }\n        if(!isArray(options)) {\n            options = [options];\n        }\n        each(options, function(handler) {\n            if (isFunction(handler)) {\n                handler = { match : handler };\n            }\n            queries[q].addHandler(handler);\n        });\n\n        return this;\n    },\n\n    /**\n     * unregisters a query and all it's handlers, or a specific handler for a query\n     *\n     * @param {string} q the media query to target\n     * @param {object || function} [handler] specific handler to unregister\n     */\n    unregister : function(q, handler) {\n        var query = this.queries[q];\n\n        if(query) {\n            if(handler) {\n                query.removeHandler(handler);\n            }\n            else {\n                query.clear();\n                delete this.queries[q];\n            }\n        }\n\n        return this;\n    }\n};\n\nmodule.exports = MediaQueryDispatch;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,cAAc,CAAC;AACxC,IAAIC,IAAI,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAC5B,IAAIE,IAAI,GAAGD,IAAI,CAACC,IAAI;AACpB,IAAIC,UAAU,GAAGF,IAAI,CAACE,UAAU;AAChC,IAAIC,OAAO,GAAGH,IAAI,CAACG,OAAO;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAAA,EAAI;EAC3B,IAAG,CAACC,MAAM,CAACC,UAAU,EAAE;IACnB,MAAM,IAAIC,KAAK,CAAC,4DAA4D,CAAC;EACjF;EAEA,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACjB,IAAI,CAACC,kBAAkB,GAAG,CAACJ,MAAM,CAACC,UAAU,CAAC,UAAU,CAAC,CAACI,OAAO;AACpE;AAEAN,kBAAkB,CAACO,SAAS,GAAG;EAE3BC,WAAW,EAAGR,kBAAkB;EAEhC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIS,QAAQ,EAAG,SAAAA,CAASC,CAAC,EAAEC,OAAO,EAAEC,aAAa,EAAE;IAC3C,IAAIR,OAAO,GAAW,IAAI,CAACA,OAAO;MAC9BS,eAAe,GAAGD,aAAa,IAAI,IAAI,CAACP,kBAAkB;IAE9D,IAAG,CAACD,OAAO,CAACM,CAAC,CAAC,EAAE;MACZN,OAAO,CAACM,CAAC,CAAC,GAAG,IAAIhB,UAAU,CAACgB,CAAC,EAAEG,eAAe,CAAC;IACnD;;IAEA;IACA,IAAGf,UAAU,CAACa,OAAO,CAAC,EAAE;MACpBA,OAAO,GAAG;QAAEG,KAAK,EAAGH;MAAQ,CAAC;IACjC;IACA,IAAG,CAACZ,OAAO,CAACY,OAAO,CAAC,EAAE;MAClBA,OAAO,GAAG,CAACA,OAAO,CAAC;IACvB;IACAd,IAAI,CAACc,OAAO,EAAE,UAASI,OAAO,EAAE;MAC5B,IAAIjB,UAAU,CAACiB,OAAO,CAAC,EAAE;QACrBA,OAAO,GAAG;UAAED,KAAK,EAAGC;QAAQ,CAAC;MACjC;MACAX,OAAO,CAACM,CAAC,CAAC,CAACM,UAAU,CAACD,OAAO,CAAC;IAClC,CAAC,CAAC;IAEF,OAAO,IAAI;EACf,CAAC;EAED;AACJ;AACA;AACA;AACA;AACA;EACIE,UAAU,EAAG,SAAAA,CAASP,CAAC,EAAEK,OAAO,EAAE;IAC9B,IAAIG,KAAK,GAAG,IAAI,CAACd,OAAO,CAACM,CAAC,CAAC;IAE3B,IAAGQ,KAAK,EAAE;MACN,IAAGH,OAAO,EAAE;QACRG,KAAK,CAACC,aAAa,CAACJ,OAAO,CAAC;MAChC,CAAC,MACI;QACDG,KAAK,CAACE,KAAK,CAAC,CAAC;QACb,OAAO,IAAI,CAAChB,OAAO,CAACM,CAAC,CAAC;MAC1B;IACJ;IAEA,OAAO,IAAI;EACf;AACJ,CAAC;AAEDW,MAAM,CAACC,OAAO,GAAGtB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}