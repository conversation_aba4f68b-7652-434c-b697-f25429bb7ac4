{"ast": null, "code": "import memoize from './memoize.js';\n\n/** Used as the maximum memoize cache size. */\nvar MAX_MEMOIZE_SIZE = 500;\n\n/**\n * A specialized version of `_.memoize` which clears the memoized function's\n * cache when it exceeds `MAX_MEMOIZE_SIZE`.\n *\n * @private\n * @param {Function} func The function to have its output memoized.\n * @returns {Function} Returns the new memoized function.\n */\nfunction memoizeCapped(func) {\n  var result = memoize(func, function (key) {\n    if (cache.size === MAX_MEMOIZE_SIZE) {\n      cache.clear();\n    }\n    return key;\n  });\n  var cache = result.cache;\n  return result;\n}\nexport default memoizeCapped;", "map": {"version": 3, "names": ["memoize", "MAX_MEMOIZE_SIZE", "memoizeCapped", "func", "result", "key", "cache", "size", "clear"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/lodash-es/_memoizeCapped.js"], "sourcesContent": ["import memoize from './memoize.js';\n\n/** Used as the maximum memoize cache size. */\nvar MAX_MEMOIZE_SIZE = 500;\n\n/**\n * A specialized version of `_.memoize` which clears the memoized function's\n * cache when it exceeds `MAX_MEMOIZE_SIZE`.\n *\n * @private\n * @param {Function} func The function to have its output memoized.\n * @returns {Function} Returns the new memoized function.\n */\nfunction memoizeCapped(func) {\n  var result = memoize(func, function(key) {\n    if (cache.size === MAX_MEMOIZE_SIZE) {\n      cache.clear();\n    }\n    return key;\n  });\n\n  var cache = result.cache;\n  return result;\n}\n\nexport default memoizeCapped;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;;AAElC;AACA,IAAIC,gBAAgB,GAAG,GAAG;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,MAAM,GAAGJ,OAAO,CAACG,IAAI,EAAE,UAASE,GAAG,EAAE;IACvC,IAAIC,KAAK,CAACC,IAAI,KAAKN,gBAAgB,EAAE;MACnCK,KAAK,CAACE,KAAK,CAAC,CAAC;IACf;IACA,OAAOH,GAAG;EACZ,CAAC,CAAC;EAEF,IAAIC,KAAK,GAAGF,MAAM,CAACE,KAAK;EACxB,OAAOF,MAAM;AACf;AAEA,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}