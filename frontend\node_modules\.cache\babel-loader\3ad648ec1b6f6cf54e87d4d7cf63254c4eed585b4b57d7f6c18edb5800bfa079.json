{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\n// eslint-disable-next-line no-unused-vars\nfunction iteratorProxy() {\n  var values = this;\n  var index = 0;\n  var iter = {\n    '@@iterator': function iterator() {\n      return iter;\n    },\n    next: function next() {\n      if (index < values.length) {\n        var value = values[index];\n        index = index + 1;\n        return {\n          done: false,\n          value: value\n        };\n      } else {\n        return {\n          done: true\n        };\n      }\n    }\n  };\n  return iter;\n}\nvar _default = iteratorProxy;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "iteratorProxy", "values", "index", "iter", "iterator", "next", "length", "done", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/util/iteratorProxy.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\n// eslint-disable-next-line no-unused-vars\nfunction iteratorProxy() {\n  var values = this;\n  var index = 0;\n  var iter = {\n    '@@iterator': function iterator() {\n      return iter;\n    },\n    next: function next() {\n      if (index < values.length) {\n        var value = values[index];\n        index = index + 1;\n        return {\n          done: false,\n          value: value\n        };\n      } else {\n        return {\n          done: true\n        };\n      }\n    }\n  };\n  return iter;\n}\nvar _default = iteratorProxy;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;;AAExB;AACA,SAASC,aAAaA,CAAA,EAAG;EACvB,IAAIC,MAAM,GAAG,IAAI;EACjB,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,IAAI,GAAG;IACT,YAAY,EAAE,SAASC,QAAQA,CAAA,EAAG;MAChC,OAAOD,IAAI;IACb,CAAC;IACDE,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;MACpB,IAAIH,KAAK,GAAGD,MAAM,CAACK,MAAM,EAAE;QACzB,IAAIR,KAAK,GAAGG,MAAM,CAACC,KAAK,CAAC;QACzBA,KAAK,GAAGA,KAAK,GAAG,CAAC;QACjB,OAAO;UACLK,IAAI,EAAE,KAAK;UACXT,KAAK,EAAEA;QACT,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACLS,IAAI,EAAE;QACR,CAAC;MACH;IACF;EACF,CAAC;EACD,OAAOJ,IAAI;AACb;AACA,IAAIK,QAAQ,GAAGR,aAAa;AAC5BH,OAAO,CAACE,OAAO,GAAGS,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}