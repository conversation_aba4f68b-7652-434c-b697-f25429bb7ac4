{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport { refType } from '@mui/utils';\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { TimeField } from \"../TimeField/index.js\";\nimport { useTimePickerDefaultizedProps } from \"../TimePicker/shared.js\";\nimport { usePickersTranslations } from \"../hooks/usePickersTranslations.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { extractValidationProps, validateTime } from \"../validation/index.js\";\nimport { ClockIcon } from \"../icons/index.js\";\nimport { useDesktopPicker } from \"../internals/hooks/useDesktopPicker/index.js\";\nimport { renderDigitalClockTimeView, renderMultiSectionDigitalClockTimeView } from \"../timeViewRenderers/index.js\";\nimport { resolveTimeFormat } from \"../internals/utils/time-utils.js\";\nimport { resolveTimeViewsResponse } from \"../internals/utils/date-time-utils.js\";\nimport { buildGetOpenDialogAriaText } from \"../locales/utils/getPickersLocalization.js\";\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [Validation](https://mui.com/x/react-date-pickers/validation/)\n *\n * API:\n *\n * - [DesktopTimePicker API](https://mui.com/x/api/date-pickers/desktop-time-picker/)\n */\nconst DesktopTimePicker = /*#__PURE__*/React.forwardRef(function DesktopTimePicker(inProps, ref) {\n  const translations = usePickersTranslations();\n  const utils = useUtils();\n\n  // Props with the default values common to all time pickers\n  const defaultizedProps = useTimePickerDefaultizedProps(inProps, 'MuiDesktopTimePicker');\n  const {\n    shouldRenderTimeInASingleColumn,\n    views: resolvedViews,\n    timeSteps\n  } = resolveTimeViewsResponse(defaultizedProps);\n  const renderTimeView = shouldRenderTimeInASingleColumn ? renderDigitalClockTimeView : renderMultiSectionDigitalClockTimeView;\n  const viewRenderers = _extends({\n    hours: renderTimeView,\n    minutes: renderTimeView,\n    seconds: renderTimeView,\n    meridiem: renderTimeView\n  }, defaultizedProps.viewRenderers);\n  const ampmInClock = defaultizedProps.ampmInClock ?? true;\n  const actionBarActions = shouldRenderTimeInASingleColumn ? [] : ['accept'];\n  // Need to avoid adding the `meridiem` view when unexpected renderer is specified\n  const shouldHoursRendererContainMeridiemView = viewRenderers.hours?.name === renderMultiSectionDigitalClockTimeView.name;\n  const views = !shouldHoursRendererContainMeridiemView ? resolvedViews.filter(view => view !== 'meridiem') : resolvedViews;\n\n  // Props with the default values specific to the desktop variant\n  const props = _extends({}, defaultizedProps, {\n    ampmInClock,\n    timeSteps,\n    viewRenderers,\n    format: resolveTimeFormat(utils, defaultizedProps),\n    // Setting only `hours` time view in case of single column time picker\n    // Allows for easy view lifecycle management\n    views: shouldRenderTimeInASingleColumn ? ['hours'] : views,\n    slots: _extends({\n      field: TimeField,\n      openPickerIcon: ClockIcon\n    }, defaultizedProps.slots),\n    slotProps: _extends({}, defaultizedProps.slotProps, {\n      field: ownerState => _extends({}, resolveComponentProps(defaultizedProps.slotProps?.field, ownerState), extractValidationProps(defaultizedProps), {\n        ref\n      }),\n      toolbar: _extends({\n        hidden: true,\n        ampmInClock\n      }, defaultizedProps.slotProps?.toolbar),\n      actionBar: _extends({\n        actions: actionBarActions\n      }, defaultizedProps.slotProps?.actionBar)\n    })\n  });\n  const {\n    renderPicker\n  } = useDesktopPicker({\n    props,\n    valueManager: singleItemValueManager,\n    valueType: 'time',\n    getOpenDialogAriaText: buildGetOpenDialogAriaText({\n      utils,\n      formatKey: 'fullTime',\n      contextTranslation: translations.openTimePickerDialogue,\n      propsTranslation: props.localeText?.openTimePickerDialogue\n    }),\n    validator: validateTime\n  });\n  return renderPicker();\n});\nDesktopTimePicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * Display ampm controls under the clock (instead of in the toolbar).\n   * @default true on desktop, false on mobile\n   */\n  ampmInClock: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  className: PropTypes.string,\n  /**\n   * If `true`, the popover or modal will close after submitting the full date.\n   * @default `true` for desktop, `false` for mobile (based on the chosen wrapper and `desktopModeMediaQuery` prop).\n   */\n  closeOnSelect: PropTypes.bool,\n  /**\n   * The default value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the picker and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, the open picker button will not be rendered (renders only the field).\n   * @default false\n   */\n  disableOpenPicker: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * @default false\n   */\n  enableAccessibleFieldDOMStructure: PropTypes.any,\n  /**\n   * Format of the date when rendered in the input(s).\n   * Defaults to localized format based on the used `views`.\n   */\n  format: PropTypes.string,\n  /**\n   * Density of the format when rendered in the input.\n   * Setting `formatDensity` to `\"spacious\"` will add a space before and after each `/`, `-` and `.` character.\n   * @default \"dense\"\n   */\n  formatDensity: PropTypes.oneOf(['dense', 'spacious']),\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * The label content.\n   */\n  label: PropTypes.node,\n  /**\n   * Locale for components texts.\n   * Allows overriding texts coming from `LocalizationProvider` and `theme`.\n   */\n  localeText: PropTypes.object,\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Name attribute used by the `input` element in the Field.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value is accepted.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The value that was just accepted.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onAccept: PropTypes.func,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The new value.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see `open`).\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the error associated with the current value changes.\n   * When a validation error is detected, the `error` parameter contains a non-null value.\n   * This can be used to render an appropriate form error.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TError} error The reason why the current value is not valid.\n   * @param {TValue} value The value associated with the error.\n   */\n  onError: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see `open`).\n   */\n  onOpen: PropTypes.func,\n  /**\n   * Callback fired when the selected sections change.\n   * @param {FieldSelectedSections} newValue The new selected sections.\n   */\n  onSelectedSectionsChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * Control the popup or dialog open state.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Force rendering in particular orientation.\n   */\n  orientation: PropTypes.oneOf(['landscape', 'portrait']),\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, disable heavy animations.\n   * @default `@media(prefers-reduced-motion: reduce)` || `navigator.userAgent` matches Android <10 or iOS <13\n   */\n  reduceAnimations: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid date-time using the validation props, except callbacks like `shouldDisable<...>`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * The currently selected sections.\n   * This prop accepts four formats:\n   * 1. If a number is provided, the section at this index will be selected.\n   * 2. If a string of type `FieldSectionType` is provided, the first section with that name will be selected.\n   * 3. If `\"all\"` is provided, all the sections will be selected.\n   * 4. If `null` is provided, no section will be selected.\n   * If not provided, the selected sections will be handled internally.\n   */\n  selectedSections: PropTypes.oneOfType([PropTypes.oneOf(['all', 'day', 'empty', 'hours', 'meridiem', 'minutes', 'month', 'seconds', 'weekDay', 'year']), PropTypes.number]),\n  /**\n   * Disable specific time.\n   * @template TDate\n   * @param {TDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  /**\n   * If `true`, disabled digital clock items will not be rendered.\n   * @default false\n   */\n  skipDisabled: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Amount of time options below or at which the single column time renderer is used.\n   * @default 24\n   */\n  thresholdToRenderTimeInASingleColumn: PropTypes.number,\n  /**\n   * The time steps between two time unit options.\n   * For example, if `timeStep.minutes = 8`, then the available minute options will be `[0, 8, 16, 24, 32, 40, 48, 56]`.\n   * When single column time renderer is used, only `timeStep.minutes` will be used.\n   * @default{ hours: 1, minutes: 5, seconds: 5 }\n   */\n  timeSteps: PropTypes.shape({\n    hours: PropTypes.number,\n    minutes: PropTypes.number,\n    seconds: PropTypes.number\n  }),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Define custom view renderers for each section.\n   * If `null`, the section will only have field editing.\n   * If `undefined`, internally defined view will be used.\n   */\n  viewRenderers: PropTypes.shape({\n    hours: PropTypes.func,\n    meridiem: PropTypes.func,\n    minutes: PropTypes.func,\n    seconds: PropTypes.func\n  }),\n  /**\n   * Available views.\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'minutes', 'seconds']).isRequired)\n};\nexport { DesktopTimePicker };", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "resolveComponentProps", "refType", "singleItemValueManager", "TimeField", "useTimePickerDefaultizedProps", "usePickersTranslations", "useUtils", "extractValidationProps", "validateTime", "ClockIcon", "useDesktopPicker", "renderDigitalClockTimeView", "renderMultiSectionDigitalClockTimeView", "resolveTimeFormat", "resolveTimeViewsResponse", "buildGetOpenDialogAriaText", "DesktopTimePicker", "forwardRef", "inProps", "ref", "translations", "utils", "defaultizedProps", "shouldRenderTimeInASingleColumn", "views", "resolvedViews", "timeSteps", "renderTimeView", "viewRenderers", "hours", "minutes", "seconds", "meridiem", "ampmInClock", "actionBarActions", "shouldHoursRendererContainMeridiemView", "name", "filter", "view", "props", "format", "slots", "field", "openPickerIcon", "slotProps", "ownerState", "toolbar", "hidden", "actionBar", "actions", "renderPicker", "valueManager", "valueType", "getOpenDialogAriaText", "formatKey", "contextTranslation", "openTimePickerDialogue", "propsTranslation", "localeText", "validator", "propTypes", "ampm", "bool", "autoFocus", "className", "string", "closeOnSelect", "defaultValue", "object", "disabled", "disableFuture", "disableIgnoringDatePartForTimeValidation", "disableOpenPicker", "disablePast", "enableAccessibleFieldDOMStructure", "any", "formatDensity", "oneOf", "inputRef", "label", "node", "maxTime", "minTime", "minutesStep", "number", "onAccept", "func", "onChange", "onClose", "onError", "onOpen", "onSelectedSectionsChange", "onViewChange", "open", "openTo", "orientation", "readOnly", "reduceAnimations", "referenceDate", "selectedSections", "oneOfType", "shouldDisableTime", "skipDisabled", "sx", "arrayOf", "thresholdToRenderTimeInASingleColumn", "shape", "timezone", "value", "isRequired"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/DesktopTimePicker/DesktopTimePicker.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport { refType } from '@mui/utils';\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { TimeField } from \"../TimeField/index.js\";\nimport { useTimePickerDefaultizedProps } from \"../TimePicker/shared.js\";\nimport { usePickersTranslations } from \"../hooks/usePickersTranslations.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { extractValidationProps, validateTime } from \"../validation/index.js\";\nimport { ClockIcon } from \"../icons/index.js\";\nimport { useDesktopPicker } from \"../internals/hooks/useDesktopPicker/index.js\";\nimport { renderDigitalClockTimeView, renderMultiSectionDigitalClockTimeView } from \"../timeViewRenderers/index.js\";\nimport { resolveTimeFormat } from \"../internals/utils/time-utils.js\";\nimport { resolveTimeViewsResponse } from \"../internals/utils/date-time-utils.js\";\nimport { buildGetOpenDialogAriaText } from \"../locales/utils/getPickersLocalization.js\";\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [Validation](https://mui.com/x/react-date-pickers/validation/)\n *\n * API:\n *\n * - [DesktopTimePicker API](https://mui.com/x/api/date-pickers/desktop-time-picker/)\n */\nconst DesktopTimePicker = /*#__PURE__*/React.forwardRef(function DesktopTimePicker(inProps, ref) {\n  const translations = usePickersTranslations();\n  const utils = useUtils();\n\n  // Props with the default values common to all time pickers\n  const defaultizedProps = useTimePickerDefaultizedProps(inProps, 'MuiDesktopTimePicker');\n  const {\n    shouldRenderTimeInASingleColumn,\n    views: resolvedViews,\n    timeSteps\n  } = resolveTimeViewsResponse(defaultizedProps);\n  const renderTimeView = shouldRenderTimeInASingleColumn ? renderDigitalClockTimeView : renderMultiSectionDigitalClockTimeView;\n  const viewRenderers = _extends({\n    hours: renderTimeView,\n    minutes: renderTimeView,\n    seconds: renderTimeView,\n    meridiem: renderTimeView\n  }, defaultizedProps.viewRenderers);\n  const ampmInClock = defaultizedProps.ampmInClock ?? true;\n  const actionBarActions = shouldRenderTimeInASingleColumn ? [] : ['accept'];\n  // Need to avoid adding the `meridiem` view when unexpected renderer is specified\n  const shouldHoursRendererContainMeridiemView = viewRenderers.hours?.name === renderMultiSectionDigitalClockTimeView.name;\n  const views = !shouldHoursRendererContainMeridiemView ? resolvedViews.filter(view => view !== 'meridiem') : resolvedViews;\n\n  // Props with the default values specific to the desktop variant\n  const props = _extends({}, defaultizedProps, {\n    ampmInClock,\n    timeSteps,\n    viewRenderers,\n    format: resolveTimeFormat(utils, defaultizedProps),\n    // Setting only `hours` time view in case of single column time picker\n    // Allows for easy view lifecycle management\n    views: shouldRenderTimeInASingleColumn ? ['hours'] : views,\n    slots: _extends({\n      field: TimeField,\n      openPickerIcon: ClockIcon\n    }, defaultizedProps.slots),\n    slotProps: _extends({}, defaultizedProps.slotProps, {\n      field: ownerState => _extends({}, resolveComponentProps(defaultizedProps.slotProps?.field, ownerState), extractValidationProps(defaultizedProps), {\n        ref\n      }),\n      toolbar: _extends({\n        hidden: true,\n        ampmInClock\n      }, defaultizedProps.slotProps?.toolbar),\n      actionBar: _extends({\n        actions: actionBarActions\n      }, defaultizedProps.slotProps?.actionBar)\n    })\n  });\n  const {\n    renderPicker\n  } = useDesktopPicker({\n    props,\n    valueManager: singleItemValueManager,\n    valueType: 'time',\n    getOpenDialogAriaText: buildGetOpenDialogAriaText({\n      utils,\n      formatKey: 'fullTime',\n      contextTranslation: translations.openTimePickerDialogue,\n      propsTranslation: props.localeText?.openTimePickerDialogue\n    }),\n    validator: validateTime\n  });\n  return renderPicker();\n});\nDesktopTimePicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * Display ampm controls under the clock (instead of in the toolbar).\n   * @default true on desktop, false on mobile\n   */\n  ampmInClock: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  className: PropTypes.string,\n  /**\n   * If `true`, the popover or modal will close after submitting the full date.\n   * @default `true` for desktop, `false` for mobile (based on the chosen wrapper and `desktopModeMediaQuery` prop).\n   */\n  closeOnSelect: PropTypes.bool,\n  /**\n   * The default value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the picker and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, the open picker button will not be rendered (renders only the field).\n   * @default false\n   */\n  disableOpenPicker: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * @default false\n   */\n  enableAccessibleFieldDOMStructure: PropTypes.any,\n  /**\n   * Format of the date when rendered in the input(s).\n   * Defaults to localized format based on the used `views`.\n   */\n  format: PropTypes.string,\n  /**\n   * Density of the format when rendered in the input.\n   * Setting `formatDensity` to `\"spacious\"` will add a space before and after each `/`, `-` and `.` character.\n   * @default \"dense\"\n   */\n  formatDensity: PropTypes.oneOf(['dense', 'spacious']),\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * The label content.\n   */\n  label: PropTypes.node,\n  /**\n   * Locale for components texts.\n   * Allows overriding texts coming from `LocalizationProvider` and `theme`.\n   */\n  localeText: PropTypes.object,\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Name attribute used by the `input` element in the Field.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value is accepted.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The value that was just accepted.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onAccept: PropTypes.func,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The new value.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see `open`).\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the error associated with the current value changes.\n   * When a validation error is detected, the `error` parameter contains a non-null value.\n   * This can be used to render an appropriate form error.\n   * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @param {TError} error The reason why the current value is not valid.\n   * @param {TValue} value The value associated with the error.\n   */\n  onError: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see `open`).\n   */\n  onOpen: PropTypes.func,\n  /**\n   * Callback fired when the selected sections change.\n   * @param {FieldSelectedSections} newValue The new selected sections.\n   */\n  onSelectedSectionsChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * Control the popup or dialog open state.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Force rendering in particular orientation.\n   */\n  orientation: PropTypes.oneOf(['landscape', 'portrait']),\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, disable heavy animations.\n   * @default `@media(prefers-reduced-motion: reduce)` || `navigator.userAgent` matches Android <10 or iOS <13\n   */\n  reduceAnimations: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid date-time using the validation props, except callbacks like `shouldDisable<...>`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * The currently selected sections.\n   * This prop accepts four formats:\n   * 1. If a number is provided, the section at this index will be selected.\n   * 2. If a string of type `FieldSectionType` is provided, the first section with that name will be selected.\n   * 3. If `\"all\"` is provided, all the sections will be selected.\n   * 4. If `null` is provided, no section will be selected.\n   * If not provided, the selected sections will be handled internally.\n   */\n  selectedSections: PropTypes.oneOfType([PropTypes.oneOf(['all', 'day', 'empty', 'hours', 'meridiem', 'minutes', 'month', 'seconds', 'weekDay', 'year']), PropTypes.number]),\n  /**\n   * Disable specific time.\n   * @template TDate\n   * @param {TDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  /**\n   * If `true`, disabled digital clock items will not be rendered.\n   * @default false\n   */\n  skipDisabled: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Amount of time options below or at which the single column time renderer is used.\n   * @default 24\n   */\n  thresholdToRenderTimeInASingleColumn: PropTypes.number,\n  /**\n   * The time steps between two time unit options.\n   * For example, if `timeStep.minutes = 8`, then the available minute options will be `[0, 8, 16, 24, 32, 40, 48, 56]`.\n   * When single column time renderer is used, only `timeStep.minutes` will be used.\n   * @default{ hours: 1, minutes: 5, seconds: 5 }\n   */\n  timeSteps: PropTypes.shape({\n    hours: PropTypes.number,\n    minutes: PropTypes.number,\n    seconds: PropTypes.number\n  }),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Define custom view renderers for each section.\n   * If `null`, the section will only have field editing.\n   * If `undefined`, internally defined view will be used.\n   */\n  viewRenderers: PropTypes.shape({\n    hours: PropTypes.func,\n    meridiem: PropTypes.func,\n    minutes: PropTypes.func,\n    seconds: PropTypes.func\n  }),\n  /**\n   * Available views.\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'minutes', 'seconds']).isRequired)\n};\nexport { DesktopTimePicker };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,qBAAqB,MAAM,kCAAkC;AACpE,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,6BAA6B,QAAQ,yBAAyB;AACvE,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,sBAAsB,EAAEC,YAAY,QAAQ,wBAAwB;AAC7E,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,SAASC,0BAA0B,EAAEC,sCAAsC,QAAQ,+BAA+B;AAClH,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,wBAAwB,QAAQ,uCAAuC;AAChF,SAASC,0BAA0B,QAAQ,4CAA4C;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,aAAalB,KAAK,CAACmB,UAAU,CAAC,SAASD,iBAAiBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/F,MAAMC,YAAY,GAAGf,sBAAsB,CAAC,CAAC;EAC7C,MAAMgB,KAAK,GAAGf,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAMgB,gBAAgB,GAAGlB,6BAA6B,CAACc,OAAO,EAAE,sBAAsB,CAAC;EACvF,MAAM;IACJK,+BAA+B;IAC/BC,KAAK,EAAEC,aAAa;IACpBC;EACF,CAAC,GAAGZ,wBAAwB,CAACQ,gBAAgB,CAAC;EAC9C,MAAMK,cAAc,GAAGJ,+BAA+B,GAAGZ,0BAA0B,GAAGC,sCAAsC;EAC5H,MAAMgB,aAAa,GAAG/B,QAAQ,CAAC;IAC7BgC,KAAK,EAAEF,cAAc;IACrBG,OAAO,EAAEH,cAAc;IACvBI,OAAO,EAAEJ,cAAc;IACvBK,QAAQ,EAAEL;EACZ,CAAC,EAAEL,gBAAgB,CAACM,aAAa,CAAC;EAClC,MAAMK,WAAW,GAAGX,gBAAgB,CAACW,WAAW,IAAI,IAAI;EACxD,MAAMC,gBAAgB,GAAGX,+BAA+B,GAAG,EAAE,GAAG,CAAC,QAAQ,CAAC;EAC1E;EACA,MAAMY,sCAAsC,GAAGP,aAAa,CAACC,KAAK,EAAEO,IAAI,KAAKxB,sCAAsC,CAACwB,IAAI;EACxH,MAAMZ,KAAK,GAAG,CAACW,sCAAsC,GAAGV,aAAa,CAACY,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAK,UAAU,CAAC,GAAGb,aAAa;;EAEzH;EACA,MAAMc,KAAK,GAAG1C,QAAQ,CAAC,CAAC,CAAC,EAAEyB,gBAAgB,EAAE;IAC3CW,WAAW;IACXP,SAAS;IACTE,aAAa;IACbY,MAAM,EAAE3B,iBAAiB,CAACQ,KAAK,EAAEC,gBAAgB,CAAC;IAClD;IACA;IACAE,KAAK,EAAED,+BAA+B,GAAG,CAAC,OAAO,CAAC,GAAGC,KAAK;IAC1DiB,KAAK,EAAE5C,QAAQ,CAAC;MACd6C,KAAK,EAAEvC,SAAS;MAChBwC,cAAc,EAAElC;IAClB,CAAC,EAAEa,gBAAgB,CAACmB,KAAK,CAAC;IAC1BG,SAAS,EAAE/C,QAAQ,CAAC,CAAC,CAAC,EAAEyB,gBAAgB,CAACsB,SAAS,EAAE;MAClDF,KAAK,EAAEG,UAAU,IAAIhD,QAAQ,CAAC,CAAC,CAAC,EAAEG,qBAAqB,CAACsB,gBAAgB,CAACsB,SAAS,EAAEF,KAAK,EAAEG,UAAU,CAAC,EAAEtC,sBAAsB,CAACe,gBAAgB,CAAC,EAAE;QAChJH;MACF,CAAC,CAAC;MACF2B,OAAO,EAAEjD,QAAQ,CAAC;QAChBkD,MAAM,EAAE,IAAI;QACZd;MACF,CAAC,EAAEX,gBAAgB,CAACsB,SAAS,EAAEE,OAAO,CAAC;MACvCE,SAAS,EAAEnD,QAAQ,CAAC;QAClBoD,OAAO,EAAEf;MACX,CAAC,EAAEZ,gBAAgB,CAACsB,SAAS,EAAEI,SAAS;IAC1C,CAAC;EACH,CAAC,CAAC;EACF,MAAM;IACJE;EACF,CAAC,GAAGxC,gBAAgB,CAAC;IACnB6B,KAAK;IACLY,YAAY,EAAEjD,sBAAsB;IACpCkD,SAAS,EAAE,MAAM;IACjBC,qBAAqB,EAAEtC,0BAA0B,CAAC;MAChDM,KAAK;MACLiC,SAAS,EAAE,UAAU;MACrBC,kBAAkB,EAAEnC,YAAY,CAACoC,sBAAsB;MACvDC,gBAAgB,EAAElB,KAAK,CAACmB,UAAU,EAAEF;IACtC,CAAC,CAAC;IACFG,SAAS,EAAEnD;EACb,CAAC,CAAC;EACF,OAAO0C,YAAY,CAAC,CAAC;AACvB,CAAC,CAAC;AACFlC,iBAAiB,CAAC4C,SAAS,GAAG;EAC5B;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEC,IAAI,EAAE9D,SAAS,CAAC+D,IAAI;EACpB;AACF;AACA;AACA;EACE7B,WAAW,EAAElC,SAAS,CAAC+D,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEC,SAAS,EAAEhE,SAAS,CAAC+D,IAAI;EACzBE,SAAS,EAAEjE,SAAS,CAACkE,MAAM;EAC3B;AACF;AACA;AACA;EACEC,aAAa,EAAEnE,SAAS,CAAC+D,IAAI;EAC7B;AACF;AACA;AACA;EACEK,YAAY,EAAEpE,SAAS,CAACqE,MAAM;EAC9B;AACF;AACA;AACA;EACEC,QAAQ,EAAEtE,SAAS,CAAC+D,IAAI;EACxB;AACF;AACA;AACA;EACEQ,aAAa,EAAEvE,SAAS,CAAC+D,IAAI;EAC7B;AACF;AACA;AACA;EACES,wCAAwC,EAAExE,SAAS,CAAC+D,IAAI;EACxD;AACF;AACA;AACA;EACEU,iBAAiB,EAAEzE,SAAS,CAAC+D,IAAI;EACjC;AACF;AACA;AACA;EACEW,WAAW,EAAE1E,SAAS,CAAC+D,IAAI;EAC3B;AACF;AACA;EACEY,iCAAiC,EAAE3E,SAAS,CAAC4E,GAAG;EAChD;AACF;AACA;AACA;EACEnC,MAAM,EAAEzC,SAAS,CAACkE,MAAM;EACxB;AACF;AACA;AACA;AACA;EACEW,aAAa,EAAE7E,SAAS,CAAC8E,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;EACrD;AACF;AACA;EACEC,QAAQ,EAAE7E,OAAO;EACjB;AACF;AACA;EACE8E,KAAK,EAAEhF,SAAS,CAACiF,IAAI;EACrB;AACF;AACA;AACA;EACEtB,UAAU,EAAE3D,SAAS,CAACqE,MAAM;EAC5B;AACF;AACA;AACA;EACEa,OAAO,EAAElF,SAAS,CAACqE,MAAM;EACzB;AACF;AACA;AACA;EACEc,OAAO,EAAEnF,SAAS,CAACqE,MAAM;EACzB;AACF;AACA;AACA;EACEe,WAAW,EAAEpF,SAAS,CAACqF,MAAM;EAC7B;AACF;AACA;EACEhD,IAAI,EAAErC,SAAS,CAACkE,MAAM;EACtB;AACF;AACA;AACA;AACA;AACA;AACA;EACEoB,QAAQ,EAAEtF,SAAS,CAACuF,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,QAAQ,EAAExF,SAAS,CAACuF,IAAI;EACxB;AACF;AACA;AACA;EACEE,OAAO,EAAEzF,SAAS,CAACuF,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,OAAO,EAAE1F,SAAS,CAACuF,IAAI;EACvB;AACF;AACA;AACA;EACEI,MAAM,EAAE3F,SAAS,CAACuF,IAAI;EACtB;AACF;AACA;AACA;EACEK,wBAAwB,EAAE5F,SAAS,CAACuF,IAAI;EACxC;AACF;AACA;AACA;AACA;EACEM,YAAY,EAAE7F,SAAS,CAACuF,IAAI;EAC5B;AACF;AACA;AACA;EACEO,IAAI,EAAE9F,SAAS,CAAC+D,IAAI;EACpB;AACF;AACA;AACA;AACA;EACEgC,MAAM,EAAE/F,SAAS,CAAC8E,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACpE;AACF;AACA;EACEkB,WAAW,EAAEhG,SAAS,CAAC8E,KAAK,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;EACvDmB,QAAQ,EAAEjG,SAAS,CAAC+D,IAAI;EACxB;AACF;AACA;AACA;EACEmC,gBAAgB,EAAElG,SAAS,CAAC+D,IAAI;EAChC;AACF;AACA;AACA;EACEoC,aAAa,EAAEnG,SAAS,CAACqE,MAAM;EAC/B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE+B,gBAAgB,EAAEpG,SAAS,CAACqG,SAAS,CAAC,CAACrG,SAAS,CAAC8E,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,EAAE9E,SAAS,CAACqF,MAAM,CAAC,CAAC;EAC1K;AACF;AACA;AACA;AACA;AACA;AACA;EACEiB,iBAAiB,EAAEtG,SAAS,CAACuF,IAAI;EACjC;AACF;AACA;AACA;EACEgB,YAAY,EAAEvG,SAAS,CAAC+D,IAAI;EAC5B;AACF;AACA;AACA;EACElB,SAAS,EAAE7C,SAAS,CAACqE,MAAM;EAC3B;AACF;AACA;AACA;EACE3B,KAAK,EAAE1C,SAAS,CAACqE,MAAM;EACvB;AACF;AACA;EACEmC,EAAE,EAAExG,SAAS,CAACqG,SAAS,CAAC,CAACrG,SAAS,CAACyG,OAAO,CAACzG,SAAS,CAACqG,SAAS,CAAC,CAACrG,SAAS,CAACuF,IAAI,EAAEvF,SAAS,CAACqE,MAAM,EAAErE,SAAS,CAAC+D,IAAI,CAAC,CAAC,CAAC,EAAE/D,SAAS,CAACuF,IAAI,EAAEvF,SAAS,CAACqE,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEqC,oCAAoC,EAAE1G,SAAS,CAACqF,MAAM;EACtD;AACF;AACA;AACA;AACA;AACA;EACE1D,SAAS,EAAE3B,SAAS,CAAC2G,KAAK,CAAC;IACzB7E,KAAK,EAAE9B,SAAS,CAACqF,MAAM;IACvBtD,OAAO,EAAE/B,SAAS,CAACqF,MAAM;IACzBrD,OAAO,EAAEhC,SAAS,CAACqF;EACrB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;EACEuB,QAAQ,EAAE5G,SAAS,CAACkE,MAAM;EAC1B;AACF;AACA;AACA;EACE2C,KAAK,EAAE7G,SAAS,CAACqE,MAAM;EACvB;AACF;AACA;AACA;AACA;EACE9B,IAAI,EAAEvC,SAAS,CAAC8E,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EAClE;AACF;AACA;AACA;AACA;EACEjD,aAAa,EAAE7B,SAAS,CAAC2G,KAAK,CAAC;IAC7B7E,KAAK,EAAE9B,SAAS,CAACuF,IAAI;IACrBtD,QAAQ,EAAEjC,SAAS,CAACuF,IAAI;IACxBxD,OAAO,EAAE/B,SAAS,CAACuF,IAAI;IACvBvD,OAAO,EAAEhC,SAAS,CAACuF;EACrB,CAAC,CAAC;EACF;AACF;AACA;EACE9D,KAAK,EAAEzB,SAAS,CAACyG,OAAO,CAACzG,SAAS,CAAC8E,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAACgC,UAAU;AACtF,CAAC;AACD,SAAS7F,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}