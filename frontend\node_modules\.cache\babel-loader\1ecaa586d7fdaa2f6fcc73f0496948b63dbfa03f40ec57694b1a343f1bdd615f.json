{"ast": null, "code": "'use client';\n\n/* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\nimport * as React from 'react';\nexport default function useControlled({\n  controlled,\n  default: defaultProp,\n  name,\n  state = 'value'\n}) {\n  // isControlled is ignored in the hook dependency lists as it should never change.\n  const {\n    current: isControlled\n  } = React.useRef(controlled !== undefined);\n  const [valueState, setValue] = React.useState(defaultProp);\n  const value = isControlled ? controlled : valueState;\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isControlled !== (controlled !== undefined)) {\n        console.error([`MUI: A component is changing the ${isControlled ? '' : 'un'}controlled ${state} state of ${name} to be ${isControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled ${name} ` + 'element for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [state, name, controlled]);\n    const {\n      current: defaultValue\n    } = React.useRef(defaultProp);\n    React.useEffect(() => {\n      if (!isControlled && !Object.is(defaultValue, defaultProp)) {\n        console.error([`MUI: A component is changing the default ${state} state of an uncontrolled ${name} after being initialized. ` + `To suppress this warning opt to use a controlled ${name}.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultProp)]);\n  }\n  const setValueIfUncontrolled = React.useCallback(newValue => {\n    if (!isControlled) {\n      setValue(newValue);\n    }\n  }, []);\n  return [value, setValueIfUncontrolled];\n}", "map": {"version": 3, "names": ["React", "useControlled", "controlled", "default", "defaultProp", "name", "state", "current", "isControlled", "useRef", "undefined", "valueState", "setValue", "useState", "value", "process", "env", "NODE_ENV", "useEffect", "console", "error", "join", "defaultValue", "Object", "is", "JSON", "stringify", "setValueIfUncontrolled", "useCallback", "newValue"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@mui/x-date-pickers/node_modules/@mui/utils/esm/useControlled/useControlled.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\nimport * as React from 'react';\nexport default function useControlled({\n  controlled,\n  default: defaultProp,\n  name,\n  state = 'value'\n}) {\n  // isControlled is ignored in the hook dependency lists as it should never change.\n  const {\n    current: isControlled\n  } = React.useRef(controlled !== undefined);\n  const [valueState, setValue] = React.useState(defaultProp);\n  const value = isControlled ? controlled : valueState;\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isControlled !== (controlled !== undefined)) {\n        console.error([`MUI: A component is changing the ${isControlled ? '' : 'un'}controlled ${state} state of ${name} to be ${isControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled ${name} ` + 'element for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [state, name, controlled]);\n    const {\n      current: defaultValue\n    } = React.useRef(defaultProp);\n    React.useEffect(() => {\n      if (!isControlled && !Object.is(defaultValue, defaultProp)) {\n        console.error([`MUI: A component is changing the default ${state} state of an uncontrolled ${name} after being initialized. ` + `To suppress this warning opt to use a controlled ${name}.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultProp)]);\n  }\n  const setValueIfUncontrolled = React.useCallback(newValue => {\n    if (!isControlled) {\n      setValue(newValue);\n    }\n  }, []);\n  return [value, setValueIfUncontrolled];\n}"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,aAAaA,CAAC;EACpCC,UAAU;EACVC,OAAO,EAAEC,WAAW;EACpBC,IAAI;EACJC,KAAK,GAAG;AACV,CAAC,EAAE;EACD;EACA,MAAM;IACJC,OAAO,EAAEC;EACX,CAAC,GAAGR,KAAK,CAACS,MAAM,CAACP,UAAU,KAAKQ,SAAS,CAAC;EAC1C,MAAM,CAACC,UAAU,EAAEC,QAAQ,CAAC,GAAGZ,KAAK,CAACa,QAAQ,CAACT,WAAW,CAAC;EAC1D,MAAMU,KAAK,GAAGN,YAAY,GAAGN,UAAU,GAAGS,UAAU;EACpD,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCjB,KAAK,CAACkB,SAAS,CAAC,MAAM;MACpB,IAAIV,YAAY,MAAMN,UAAU,KAAKQ,SAAS,CAAC,EAAE;QAC/CS,OAAO,CAACC,KAAK,CAAC,CAAC,oCAAoCZ,YAAY,GAAG,EAAE,GAAG,IAAI,cAAcF,KAAK,aAAaD,IAAI,UAAUG,YAAY,GAAG,IAAI,GAAG,EAAE,aAAa,EAAE,6EAA6E,EAAE,qDAAqDH,IAAI,GAAG,GAAG,4CAA4C,EAAE,4HAA4H,EAAE,sDAAsD,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/hB;IACF,CAAC,EAAE,CAACf,KAAK,EAAED,IAAI,EAAEH,UAAU,CAAC,CAAC;IAC7B,MAAM;MACJK,OAAO,EAAEe;IACX,CAAC,GAAGtB,KAAK,CAACS,MAAM,CAACL,WAAW,CAAC;IAC7BJ,KAAK,CAACkB,SAAS,CAAC,MAAM;MACpB,IAAI,CAACV,YAAY,IAAI,CAACe,MAAM,CAACC,EAAE,CAACF,YAAY,EAAElB,WAAW,CAAC,EAAE;QAC1De,OAAO,CAACC,KAAK,CAAC,CAAC,4CAA4Cd,KAAK,6BAA6BD,IAAI,4BAA4B,GAAG,oDAAoDA,IAAI,GAAG,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1M;IACF,CAAC,EAAE,CAACI,IAAI,CAACC,SAAS,CAACtB,WAAW,CAAC,CAAC,CAAC;EACnC;EACA,MAAMuB,sBAAsB,GAAG3B,KAAK,CAAC4B,WAAW,CAACC,QAAQ,IAAI;IAC3D,IAAI,CAACrB,YAAY,EAAE;MACjBI,QAAQ,CAACiB,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACf,KAAK,EAAEa,sBAAsB,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}