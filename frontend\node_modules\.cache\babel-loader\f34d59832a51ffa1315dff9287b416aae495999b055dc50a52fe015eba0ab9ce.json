{"ast": null, "code": "var has = Object.prototype.hasOwnProperty;\nfunction dequal(foo, bar) {\n  var ctor, len;\n  if (foo === bar) return true;\n  if (foo && bar && (ctor = foo.constructor) === bar.constructor) {\n    if (ctor === Date) return foo.getTime() === bar.getTime();\n    if (ctor === RegExp) return foo.toString() === bar.toString();\n    if (ctor === Array) {\n      if ((len = foo.length) === bar.length) {\n        while (len-- && dequal(foo[len], bar[len]));\n      }\n      return len === -1;\n    }\n    if (!ctor || typeof foo === 'object') {\n      len = 0;\n      for (ctor in foo) {\n        if (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n        if (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n      }\n      return Object.keys(bar).length === len;\n    }\n  }\n  return foo !== foo && bar !== bar;\n}\nexports.dequal = dequal;", "map": {"version": 3, "names": ["has", "Object", "prototype", "hasOwnProperty", "dequal", "foo", "bar", "ctor", "len", "constructor", "Date", "getTime", "RegExp", "toString", "Array", "length", "call", "keys", "exports"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/dequal/lite/index.js"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction dequal(foo, bar) {\n\tvar ctor, len;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n\nexports.dequal = dequal;"], "mappings": "AAAA,IAAIA,GAAG,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAEzC,SAASC,MAAMA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACzB,IAAIC,IAAI,EAAEC,GAAG;EACb,IAAIH,GAAG,KAAKC,GAAG,EAAE,OAAO,IAAI;EAE5B,IAAID,GAAG,IAAIC,GAAG,IAAI,CAACC,IAAI,GAACF,GAAG,CAACI,WAAW,MAAMH,GAAG,CAACG,WAAW,EAAE;IAC7D,IAAIF,IAAI,KAAKG,IAAI,EAAE,OAAOL,GAAG,CAACM,OAAO,CAAC,CAAC,KAAKL,GAAG,CAACK,OAAO,CAAC,CAAC;IACzD,IAAIJ,IAAI,KAAKK,MAAM,EAAE,OAAOP,GAAG,CAACQ,QAAQ,CAAC,CAAC,KAAKP,GAAG,CAACO,QAAQ,CAAC,CAAC;IAE7D,IAAIN,IAAI,KAAKO,KAAK,EAAE;MACnB,IAAI,CAACN,GAAG,GAACH,GAAG,CAACU,MAAM,MAAMT,GAAG,CAACS,MAAM,EAAE;QACpC,OAAOP,GAAG,EAAE,IAAIJ,MAAM,CAACC,GAAG,CAACG,GAAG,CAAC,EAAEF,GAAG,CAACE,GAAG,CAAC,CAAC,CAAC;MAC5C;MACA,OAAOA,GAAG,KAAK,CAAC,CAAC;IAClB;IAEA,IAAI,CAACD,IAAI,IAAI,OAAOF,GAAG,KAAK,QAAQ,EAAE;MACrCG,GAAG,GAAG,CAAC;MACP,KAAKD,IAAI,IAAIF,GAAG,EAAE;QACjB,IAAIL,GAAG,CAACgB,IAAI,CAACX,GAAG,EAAEE,IAAI,CAAC,IAAI,EAAEC,GAAG,IAAI,CAACR,GAAG,CAACgB,IAAI,CAACV,GAAG,EAAEC,IAAI,CAAC,EAAE,OAAO,KAAK;QACtE,IAAI,EAAEA,IAAI,IAAID,GAAG,CAAC,IAAI,CAACF,MAAM,CAACC,GAAG,CAACE,IAAI,CAAC,EAAED,GAAG,CAACC,IAAI,CAAC,CAAC,EAAE,OAAO,KAAK;MAClE;MACA,OAAON,MAAM,CAACgB,IAAI,CAACX,GAAG,CAAC,CAACS,MAAM,KAAKP,GAAG;IACvC;EACD;EAEA,OAAOH,GAAG,KAAKA,GAAG,IAAIC,GAAG,KAAKA,GAAG;AAClC;AAEAY,OAAO,CAACd,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}