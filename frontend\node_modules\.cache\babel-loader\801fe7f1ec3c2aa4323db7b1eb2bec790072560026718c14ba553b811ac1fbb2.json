{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar listRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'menu'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'ol'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'ul'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['listitem']],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = listRole;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "listRole", "abstract", "accessibleNameRequired", "baseConcepts", "childrenPresentational", "nameFrom", "prohibitedProps", "props", "relatedConcepts", "concept", "name", "module", "requireContextRole", "requiredContextRole", "requiredOwnedElements", "requiredProps", "superClass", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/etc/roles/literal/listRole.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar listRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'menu'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'ol'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'ul'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['listitem']],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = listRole;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,QAAQ,GAAG;EACbC,QAAQ,EAAE,KAAK;EACfC,sBAAsB,EAAE,KAAK;EAC7BC,YAAY,EAAE,EAAE;EAChBC,sBAAsB,EAAE,KAAK;EAC7BC,QAAQ,EAAE,CAAC,QAAQ,CAAC;EACpBC,eAAe,EAAE,EAAE;EACnBC,KAAK,EAAE,CAAC,CAAC;EACTC,eAAe,EAAE,CAAC;IAChBC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,EAAE;IACDF,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,EAAE;IACDF,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,CAAC;EACFC,kBAAkB,EAAE,EAAE;EACtBC,mBAAmB,EAAE,EAAE;EACvBC,qBAAqB,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC;EACrCC,aAAa,EAAE,CAAC,CAAC;EACjBC,UAAU,EAAE,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC;AACnD,CAAC;AACD,IAAIC,QAAQ,GAAGjB,QAAQ;AACvBH,OAAO,CAACE,OAAO,GAAGkB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}