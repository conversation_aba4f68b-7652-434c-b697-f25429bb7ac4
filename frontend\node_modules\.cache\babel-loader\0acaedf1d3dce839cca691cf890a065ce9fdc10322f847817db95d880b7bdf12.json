{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar listboxRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-invalid': null,\n    'aria-multiselectable': null,\n    'aria-readonly': null,\n    'aria-required': null,\n    'aria-orientation': 'vertical'\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        constraints: ['>1'],\n        name: 'size'\n      }],\n      constraints: ['the size attribute value is greater than 1'],\n      name: 'select'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        name: 'multiple'\n      }],\n      name: 'select'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'datalist'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'list'\n    },\n    module: 'ARIA'\n  }, {\n    concept: {\n      name: 'select'\n    },\n    module: 'XForms'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['option', 'group'], ['option']],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'composite', 'select'], ['roletype', 'structure', 'section', 'group', 'select']]\n};\nvar _default = listboxRole;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "listboxRole", "abstract", "accessibleNameRequired", "baseConcepts", "childrenPresentational", "nameFrom", "prohibitedProps", "props", "relatedConcepts", "concept", "attributes", "constraints", "name", "module", "requireContextRole", "requiredContextRole", "requiredOwnedElements", "requiredProps", "superClass", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/@testing-library/dom/node_modules/aria-query/lib/etc/roles/literal/listboxRole.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar listboxRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-invalid': null,\n    'aria-multiselectable': null,\n    'aria-readonly': null,\n    'aria-required': null,\n    'aria-orientation': 'vertical'\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        constraints: ['>1'],\n        name: 'size'\n      }],\n      constraints: ['the size attribute value is greater than 1'],\n      name: 'select'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        name: 'multiple'\n      }],\n      name: 'select'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'datalist'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'list'\n    },\n    module: 'ARIA'\n  }, {\n    concept: {\n      name: 'select'\n    },\n    module: 'XForms'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['option', 'group'], ['option']],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'composite', 'select'], ['roletype', 'structure', 'section', 'group', 'select']]\n};\nvar _default = listboxRole;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,WAAW,GAAG;EAChBC,QAAQ,EAAE,KAAK;EACfC,sBAAsB,EAAE,IAAI;EAC5BC,YAAY,EAAE,EAAE;EAChBC,sBAAsB,EAAE,KAAK;EAC7BC,QAAQ,EAAE,CAAC,QAAQ,CAAC;EACpBC,eAAe,EAAE,EAAE;EACnBC,KAAK,EAAE;IACL,mBAAmB,EAAE,IAAI;IACzB,eAAe,EAAE,IAAI;IACrB,cAAc,EAAE,IAAI;IACpB,sBAAsB,EAAE,IAAI;IAC5B,eAAe,EAAE,IAAI;IACrB,eAAe,EAAE,IAAI;IACrB,kBAAkB,EAAE;EACtB,CAAC;EACDC,eAAe,EAAE,CAAC;IAChBC,OAAO,EAAE;MACPC,UAAU,EAAE,CAAC;QACXC,WAAW,EAAE,CAAC,IAAI,CAAC;QACnBC,IAAI,EAAE;MACR,CAAC,CAAC;MACFD,WAAW,EAAE,CAAC,4CAA4C,CAAC;MAC3DC,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,EAAE;IACDJ,OAAO,EAAE;MACPC,UAAU,EAAE,CAAC;QACXE,IAAI,EAAE;MACR,CAAC,CAAC;MACFA,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,EAAE;IACDJ,OAAO,EAAE;MACPG,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,EAAE;IACDJ,OAAO,EAAE;MACPG,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,EAAE;IACDJ,OAAO,EAAE;MACPG,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,CAAC;EACFC,kBAAkB,EAAE,EAAE;EACtBC,mBAAmB,EAAE,EAAE;EACvBC,qBAAqB,EAAE,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;EACxDC,aAAa,EAAE,CAAC,CAAC;EACjBC,UAAU,EAAE,CAAC,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC;AACrH,CAAC;AACD,IAAIC,QAAQ,GAAGnB,WAAW;AAC1BH,OAAO,CAACE,OAAO,GAAGoB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}