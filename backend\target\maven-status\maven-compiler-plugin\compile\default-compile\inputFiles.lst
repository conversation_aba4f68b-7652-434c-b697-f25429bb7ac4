C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\config\AppConfig.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\config\JwtConstant.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\config\JwtProvider.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\config\JwtTokenValidator.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\controller\admin\AdminFoodController.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\controller\admin\AdminOrderController.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\controller\admin\AdminRestaurantController.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\controller\admin\CategoryController.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\controller\admin\IngredientsController.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\controller\AuthController.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\controller\CardController.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\controller\FoodController.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\controller\OrderController.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\controller\RestaurantController.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\controller\UserController.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\dto\RestaurantDto.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\entity\Address.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\entity\Card.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\entity\CardItem.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\entity\Category.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\entity\ContactInformation.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\entity\Food.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\entity\IngredientsCategory.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\entity\IngredientsItem.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\entity\Order.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\entity\OrderItem.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\entity\Restaurant.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\entity\User.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\enums\Role.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\FoodOrderingApplication.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\repository\AddressRepository.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\repository\CardItemRepository.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\repository\CardRepository.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\repository\CategoryRepository.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\repository\FoodRepository.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\repository\IngredientsCategoryRepository.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\repository\IngredientsItemRepository.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\repository\OrderItemRepository.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\repository\OrderRepository.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\repository\RestaurantRepository.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\repository\UserRepository.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\request\AddCardItemRequest.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\request\CreateFoodRequest.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\request\CreateRestaurantRequest.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\request\IngredientRequest.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\request\IngredientsCategoryRequest.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\request\LoginRequest.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\request\OrderRequest.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\request\UpdateCardItemRequest.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\response\AuthResponse.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\response\MessageResponse.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\response\PaymentResponse.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\CardService.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\CardServiceImp.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\CategoryService.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\CategoryServiceImp.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\CustomerUserDetailsService.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\FoodService.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\FoodServiceImp.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\IngredientsService.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\IngredientsServiceImp.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\OrderService.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\OrderServiceImp.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\PaymentService.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\PaymentServiceImp.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\RestaurantService.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\RestaurantServiceImp.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\UserService.java
C:\Users\<USER>\IdeaProjects\FoodOrdering-main\FoodOrdering-main\backend\src\main\java\com\aalperen\Food\Ordering\service\UserServiceImp.java
