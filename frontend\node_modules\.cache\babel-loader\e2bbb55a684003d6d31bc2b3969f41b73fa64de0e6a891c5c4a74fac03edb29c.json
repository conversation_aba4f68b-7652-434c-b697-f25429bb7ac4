{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.checkSpecKeys = exports.checkNavigable = exports.changeSlide = exports.canUseDOM = exports.canGoNext = void 0;\nexports.clamp = clamp;\nexports.extractObject = void 0;\nexports.filterSettings = filterSettings;\nexports.validSettings = exports.swipeStart = exports.swipeMove = exports.swipeEnd = exports.slidesOnRight = exports.slidesOnLeft = exports.slideHandler = exports.siblingDirection = exports.safePreventDefault = exports.lazyStartIndex = exports.lazySlidesOnRight = exports.lazySlidesOnLeft = exports.lazyEndIndex = exports.keyHandler = exports.initializedState = exports.getWidth = exports.getTrackLeft = exports.getTrackCSS = exports.getTrackAnimateCSS = exports.getTotalSlides = exports.getSwipeDirection = exports.getSlideCount = exports.getRequiredLazySlides = exports.getPreClones = exports.getPostClones = exports.getOnDemandLazySlides = exports.getNavigableIndexes = exports.getHeight = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _defaultProps = _interopRequireDefault(require(\"../default-props\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : String(i);\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction clamp(number, lowerBound, upperBound) {\n  return Math.max(lowerBound, Math.min(number, upperBound));\n}\nvar safePreventDefault = exports.safePreventDefault = function safePreventDefault(event) {\n  var passiveEvents = [\"onTouchStart\", \"onTouchMove\", \"onWheel\"];\n  if (!passiveEvents.includes(event._reactName)) {\n    event.preventDefault();\n  }\n};\nvar getOnDemandLazySlides = exports.getOnDemandLazySlides = function getOnDemandLazySlides(spec) {\n  var onDemandSlides = [];\n  var startIndex = lazyStartIndex(spec);\n  var endIndex = lazyEndIndex(spec);\n  for (var slideIndex = startIndex; slideIndex < endIndex; slideIndex++) {\n    if (spec.lazyLoadedList.indexOf(slideIndex) < 0) {\n      onDemandSlides.push(slideIndex);\n    }\n  }\n  return onDemandSlides;\n};\n\n// return list of slides that need to be present\nvar getRequiredLazySlides = exports.getRequiredLazySlides = function getRequiredLazySlides(spec) {\n  var requiredSlides = [];\n  var startIndex = lazyStartIndex(spec);\n  var endIndex = lazyEndIndex(spec);\n  for (var slideIndex = startIndex; slideIndex < endIndex; slideIndex++) {\n    requiredSlides.push(slideIndex);\n  }\n  return requiredSlides;\n};\n\n// startIndex that needs to be present\nvar lazyStartIndex = exports.lazyStartIndex = function lazyStartIndex(spec) {\n  return spec.currentSlide - lazySlidesOnLeft(spec);\n};\nvar lazyEndIndex = exports.lazyEndIndex = function lazyEndIndex(spec) {\n  return spec.currentSlide + lazySlidesOnRight(spec);\n};\nvar lazySlidesOnLeft = exports.lazySlidesOnLeft = function lazySlidesOnLeft(spec) {\n  return spec.centerMode ? Math.floor(spec.slidesToShow / 2) + (parseInt(spec.centerPadding) > 0 ? 1 : 0) : 0;\n};\nvar lazySlidesOnRight = exports.lazySlidesOnRight = function lazySlidesOnRight(spec) {\n  return spec.centerMode ? Math.floor((spec.slidesToShow - 1) / 2) + 1 + (parseInt(spec.centerPadding) > 0 ? 1 : 0) : spec.slidesToShow;\n};\n\n// get width of an element\nvar getWidth = exports.getWidth = function getWidth(elem) {\n  return elem && elem.offsetWidth || 0;\n};\nvar getHeight = exports.getHeight = function getHeight(elem) {\n  return elem && elem.offsetHeight || 0;\n};\nvar getSwipeDirection = exports.getSwipeDirection = function getSwipeDirection(touchObject) {\n  var verticalSwiping = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var xDist, yDist, r, swipeAngle;\n  xDist = touchObject.startX - touchObject.curX;\n  yDist = touchObject.startY - touchObject.curY;\n  r = Math.atan2(yDist, xDist);\n  swipeAngle = Math.round(r * 180 / Math.PI);\n  if (swipeAngle < 0) {\n    swipeAngle = 360 - Math.abs(swipeAngle);\n  }\n  if (swipeAngle <= 45 && swipeAngle >= 0 || swipeAngle <= 360 && swipeAngle >= 315) {\n    return \"left\";\n  }\n  if (swipeAngle >= 135 && swipeAngle <= 225) {\n    return \"right\";\n  }\n  if (verticalSwiping === true) {\n    if (swipeAngle >= 35 && swipeAngle <= 135) {\n      return \"up\";\n    } else {\n      return \"down\";\n    }\n  }\n  return \"vertical\";\n};\n\n// whether or not we can go next\nvar canGoNext = exports.canGoNext = function canGoNext(spec) {\n  var canGo = true;\n  if (!spec.infinite) {\n    if (spec.centerMode && spec.currentSlide >= spec.slideCount - 1) {\n      canGo = false;\n    } else if (spec.slideCount <= spec.slidesToShow || spec.currentSlide >= spec.slideCount - spec.slidesToShow) {\n      canGo = false;\n    }\n  }\n  return canGo;\n};\n\n// given an object and a list of keys, return new object with given keys\nvar extractObject = exports.extractObject = function extractObject(spec, keys) {\n  var newObject = {};\n  keys.forEach(function (key) {\n    return newObject[key] = spec[key];\n  });\n  return newObject;\n};\n\n// get initialized state\nvar initializedState = exports.initializedState = function initializedState(spec) {\n  // spec also contains listRef, trackRef\n  var slideCount = _react[\"default\"].Children.count(spec.children);\n  var listNode = spec.listRef;\n  var listWidth = Math.ceil(getWidth(listNode));\n  var trackNode = spec.trackRef && spec.trackRef.node;\n  var trackWidth = Math.ceil(getWidth(trackNode));\n  var slideWidth;\n  if (!spec.vertical) {\n    var centerPaddingAdj = spec.centerMode && parseInt(spec.centerPadding) * 2;\n    if (typeof spec.centerPadding === \"string\" && spec.centerPadding.slice(-1) === \"%\") {\n      centerPaddingAdj *= listWidth / 100;\n    }\n    slideWidth = Math.ceil((listWidth - centerPaddingAdj) / spec.slidesToShow);\n  } else {\n    slideWidth = listWidth;\n  }\n  var slideHeight = listNode && getHeight(listNode.querySelector('[data-index=\"0\"]'));\n  var listHeight = slideHeight * spec.slidesToShow;\n  var currentSlide = spec.currentSlide === undefined ? spec.initialSlide : spec.currentSlide;\n  if (spec.rtl && spec.currentSlide === undefined) {\n    currentSlide = slideCount - 1 - spec.initialSlide;\n  }\n  var lazyLoadedList = spec.lazyLoadedList || [];\n  var slidesToLoad = getOnDemandLazySlides(_objectSpread(_objectSpread({}, spec), {}, {\n    currentSlide: currentSlide,\n    lazyLoadedList: lazyLoadedList\n  }));\n  lazyLoadedList = lazyLoadedList.concat(slidesToLoad);\n  var state = {\n    slideCount: slideCount,\n    slideWidth: slideWidth,\n    listWidth: listWidth,\n    trackWidth: trackWidth,\n    currentSlide: currentSlide,\n    slideHeight: slideHeight,\n    listHeight: listHeight,\n    lazyLoadedList: lazyLoadedList\n  };\n  if (spec.autoplaying === null && spec.autoplay) {\n    state[\"autoplaying\"] = \"playing\";\n  }\n  return state;\n};\nvar slideHandler = exports.slideHandler = function slideHandler(spec) {\n  var waitForAnimate = spec.waitForAnimate,\n    animating = spec.animating,\n    fade = spec.fade,\n    infinite = spec.infinite,\n    index = spec.index,\n    slideCount = spec.slideCount,\n    lazyLoad = spec.lazyLoad,\n    currentSlide = spec.currentSlide,\n    centerMode = spec.centerMode,\n    slidesToScroll = spec.slidesToScroll,\n    slidesToShow = spec.slidesToShow,\n    useCSS = spec.useCSS;\n  var lazyLoadedList = spec.lazyLoadedList;\n  if (waitForAnimate && animating) return {};\n  var animationSlide = index,\n    finalSlide,\n    animationLeft,\n    finalLeft;\n  var state = {},\n    nextState = {};\n  var targetSlide = infinite ? index : clamp(index, 0, slideCount - 1);\n  if (fade) {\n    if (!infinite && (index < 0 || index >= slideCount)) return {};\n    if (index < 0) {\n      animationSlide = index + slideCount;\n    } else if (index >= slideCount) {\n      animationSlide = index - slideCount;\n    }\n    if (lazyLoad && lazyLoadedList.indexOf(animationSlide) < 0) {\n      lazyLoadedList = lazyLoadedList.concat(animationSlide);\n    }\n    state = {\n      animating: true,\n      currentSlide: animationSlide,\n      lazyLoadedList: lazyLoadedList,\n      targetSlide: animationSlide\n    };\n    nextState = {\n      animating: false,\n      targetSlide: animationSlide\n    };\n  } else {\n    finalSlide = animationSlide;\n    if (animationSlide < 0) {\n      finalSlide = animationSlide + slideCount;\n      if (!infinite) finalSlide = 0;else if (slideCount % slidesToScroll !== 0) finalSlide = slideCount - slideCount % slidesToScroll;\n    } else if (!canGoNext(spec) && animationSlide > currentSlide) {\n      animationSlide = finalSlide = currentSlide;\n    } else if (centerMode && animationSlide >= slideCount) {\n      animationSlide = infinite ? slideCount : slideCount - 1;\n      finalSlide = infinite ? 0 : slideCount - 1;\n    } else if (animationSlide >= slideCount) {\n      finalSlide = animationSlide - slideCount;\n      if (!infinite) finalSlide = slideCount - slidesToShow;else if (slideCount % slidesToScroll !== 0) finalSlide = 0;\n    }\n    if (!infinite && animationSlide + slidesToShow >= slideCount) {\n      finalSlide = slideCount - slidesToShow;\n    }\n    animationLeft = getTrackLeft(_objectSpread(_objectSpread({}, spec), {}, {\n      slideIndex: animationSlide\n    }));\n    finalLeft = getTrackLeft(_objectSpread(_objectSpread({}, spec), {}, {\n      slideIndex: finalSlide\n    }));\n    if (!infinite) {\n      if (animationLeft === finalLeft) animationSlide = finalSlide;\n      animationLeft = finalLeft;\n    }\n    if (lazyLoad) {\n      lazyLoadedList = lazyLoadedList.concat(getOnDemandLazySlides(_objectSpread(_objectSpread({}, spec), {}, {\n        currentSlide: animationSlide\n      })));\n    }\n    if (!useCSS) {\n      state = {\n        currentSlide: finalSlide,\n        trackStyle: getTrackCSS(_objectSpread(_objectSpread({}, spec), {}, {\n          left: finalLeft\n        })),\n        lazyLoadedList: lazyLoadedList,\n        targetSlide: targetSlide\n      };\n    } else {\n      state = {\n        animating: true,\n        currentSlide: finalSlide,\n        trackStyle: getTrackAnimateCSS(_objectSpread(_objectSpread({}, spec), {}, {\n          left: animationLeft\n        })),\n        lazyLoadedList: lazyLoadedList,\n        targetSlide: targetSlide\n      };\n      nextState = {\n        animating: false,\n        currentSlide: finalSlide,\n        trackStyle: getTrackCSS(_objectSpread(_objectSpread({}, spec), {}, {\n          left: finalLeft\n        })),\n        swipeLeft: null,\n        targetSlide: targetSlide\n      };\n    }\n  }\n  return {\n    state: state,\n    nextState: nextState\n  };\n};\nvar changeSlide = exports.changeSlide = function changeSlide(spec, options) {\n  var indexOffset, previousInt, slideOffset, unevenOffset, targetSlide;\n  var slidesToScroll = spec.slidesToScroll,\n    slidesToShow = spec.slidesToShow,\n    slideCount = spec.slideCount,\n    currentSlide = spec.currentSlide,\n    previousTargetSlide = spec.targetSlide,\n    lazyLoad = spec.lazyLoad,\n    infinite = spec.infinite;\n  unevenOffset = slideCount % slidesToScroll !== 0;\n  indexOffset = unevenOffset ? 0 : (slideCount - currentSlide) % slidesToScroll;\n  if (options.message === \"previous\") {\n    slideOffset = indexOffset === 0 ? slidesToScroll : slidesToShow - indexOffset;\n    targetSlide = currentSlide - slideOffset;\n    if (lazyLoad && !infinite) {\n      previousInt = currentSlide - slideOffset;\n      targetSlide = previousInt === -1 ? slideCount - 1 : previousInt;\n    }\n    if (!infinite) {\n      targetSlide = previousTargetSlide - slidesToScroll;\n    }\n  } else if (options.message === \"next\") {\n    slideOffset = indexOffset === 0 ? slidesToScroll : indexOffset;\n    targetSlide = currentSlide + slideOffset;\n    if (lazyLoad && !infinite) {\n      targetSlide = (currentSlide + slidesToScroll) % slideCount + indexOffset;\n    }\n    if (!infinite) {\n      targetSlide = previousTargetSlide + slidesToScroll;\n    }\n  } else if (options.message === \"dots\") {\n    // Click on dots\n    targetSlide = options.index * options.slidesToScroll;\n  } else if (options.message === \"children\") {\n    // Click on the slides\n    targetSlide = options.index;\n    if (infinite) {\n      var direction = siblingDirection(_objectSpread(_objectSpread({}, spec), {}, {\n        targetSlide: targetSlide\n      }));\n      if (targetSlide > options.currentSlide && direction === \"left\") {\n        targetSlide = targetSlide - slideCount;\n      } else if (targetSlide < options.currentSlide && direction === \"right\") {\n        targetSlide = targetSlide + slideCount;\n      }\n    }\n  } else if (options.message === \"index\") {\n    targetSlide = Number(options.index);\n  }\n  return targetSlide;\n};\nvar keyHandler = exports.keyHandler = function keyHandler(e, accessibility, rtl) {\n  if (e.target.tagName.match(\"TEXTAREA|INPUT|SELECT\") || !accessibility) return \"\";\n  if (e.keyCode === 37) return rtl ? \"next\" : \"previous\";\n  if (e.keyCode === 39) return rtl ? \"previous\" : \"next\";\n  return \"\";\n};\nvar swipeStart = exports.swipeStart = function swipeStart(e, swipe, draggable) {\n  e.target.tagName === \"IMG\" && safePreventDefault(e);\n  if (!swipe || !draggable && e.type.indexOf(\"mouse\") !== -1) return \"\";\n  return {\n    dragging: true,\n    touchObject: {\n      startX: e.touches ? e.touches[0].pageX : e.clientX,\n      startY: e.touches ? e.touches[0].pageY : e.clientY,\n      curX: e.touches ? e.touches[0].pageX : e.clientX,\n      curY: e.touches ? e.touches[0].pageY : e.clientY\n    }\n  };\n};\nvar swipeMove = exports.swipeMove = function swipeMove(e, spec) {\n  // spec also contains, trackRef and slideIndex\n  var scrolling = spec.scrolling,\n    animating = spec.animating,\n    vertical = spec.vertical,\n    swipeToSlide = spec.swipeToSlide,\n    verticalSwiping = spec.verticalSwiping,\n    rtl = spec.rtl,\n    currentSlide = spec.currentSlide,\n    edgeFriction = spec.edgeFriction,\n    edgeDragged = spec.edgeDragged,\n    onEdge = spec.onEdge,\n    swiped = spec.swiped,\n    swiping = spec.swiping,\n    slideCount = spec.slideCount,\n    slidesToScroll = spec.slidesToScroll,\n    infinite = spec.infinite,\n    touchObject = spec.touchObject,\n    swipeEvent = spec.swipeEvent,\n    listHeight = spec.listHeight,\n    listWidth = spec.listWidth;\n  if (scrolling) return;\n  if (animating) return safePreventDefault(e);\n  if (vertical && swipeToSlide && verticalSwiping) safePreventDefault(e);\n  var swipeLeft,\n    state = {};\n  var curLeft = getTrackLeft(spec);\n  touchObject.curX = e.touches ? e.touches[0].pageX : e.clientX;\n  touchObject.curY = e.touches ? e.touches[0].pageY : e.clientY;\n  touchObject.swipeLength = Math.round(Math.sqrt(Math.pow(touchObject.curX - touchObject.startX, 2)));\n  var verticalSwipeLength = Math.round(Math.sqrt(Math.pow(touchObject.curY - touchObject.startY, 2)));\n  if (!verticalSwiping && !swiping && verticalSwipeLength > 10) {\n    return {\n      scrolling: true\n    };\n  }\n  if (verticalSwiping) touchObject.swipeLength = verticalSwipeLength;\n  var positionOffset = (!rtl ? 1 : -1) * (touchObject.curX > touchObject.startX ? 1 : -1);\n  if (verticalSwiping) positionOffset = touchObject.curY > touchObject.startY ? 1 : -1;\n  var dotCount = Math.ceil(slideCount / slidesToScroll);\n  var swipeDirection = getSwipeDirection(spec.touchObject, verticalSwiping);\n  var touchSwipeLength = touchObject.swipeLength;\n  if (!infinite) {\n    if (currentSlide === 0 && (swipeDirection === \"right\" || swipeDirection === \"down\") || currentSlide + 1 >= dotCount && (swipeDirection === \"left\" || swipeDirection === \"up\") || !canGoNext(spec) && (swipeDirection === \"left\" || swipeDirection === \"up\")) {\n      touchSwipeLength = touchObject.swipeLength * edgeFriction;\n      if (edgeDragged === false && onEdge) {\n        onEdge(swipeDirection);\n        state[\"edgeDragged\"] = true;\n      }\n    }\n  }\n  if (!swiped && swipeEvent) {\n    swipeEvent(swipeDirection);\n    state[\"swiped\"] = true;\n  }\n  if (!vertical) {\n    if (!rtl) {\n      swipeLeft = curLeft + touchSwipeLength * positionOffset;\n    } else {\n      swipeLeft = curLeft - touchSwipeLength * positionOffset;\n    }\n  } else {\n    swipeLeft = curLeft + touchSwipeLength * (listHeight / listWidth) * positionOffset;\n  }\n  if (verticalSwiping) {\n    swipeLeft = curLeft + touchSwipeLength * positionOffset;\n  }\n  state = _objectSpread(_objectSpread({}, state), {}, {\n    touchObject: touchObject,\n    swipeLeft: swipeLeft,\n    trackStyle: getTrackCSS(_objectSpread(_objectSpread({}, spec), {}, {\n      left: swipeLeft\n    }))\n  });\n  if (Math.abs(touchObject.curX - touchObject.startX) < Math.abs(touchObject.curY - touchObject.startY) * 0.8) {\n    return state;\n  }\n  if (touchObject.swipeLength > 10) {\n    state[\"swiping\"] = true;\n    safePreventDefault(e);\n  }\n  return state;\n};\nvar swipeEnd = exports.swipeEnd = function swipeEnd(e, spec) {\n  var dragging = spec.dragging,\n    swipe = spec.swipe,\n    touchObject = spec.touchObject,\n    listWidth = spec.listWidth,\n    touchThreshold = spec.touchThreshold,\n    verticalSwiping = spec.verticalSwiping,\n    listHeight = spec.listHeight,\n    swipeToSlide = spec.swipeToSlide,\n    scrolling = spec.scrolling,\n    onSwipe = spec.onSwipe,\n    targetSlide = spec.targetSlide,\n    currentSlide = spec.currentSlide,\n    infinite = spec.infinite;\n  if (!dragging) {\n    if (swipe) safePreventDefault(e);\n    return {};\n  }\n  var minSwipe = verticalSwiping ? listHeight / touchThreshold : listWidth / touchThreshold;\n  var swipeDirection = getSwipeDirection(touchObject, verticalSwiping);\n  // reset the state of touch related state variables.\n  var state = {\n    dragging: false,\n    edgeDragged: false,\n    scrolling: false,\n    swiping: false,\n    swiped: false,\n    swipeLeft: null,\n    touchObject: {}\n  };\n  if (scrolling) {\n    return state;\n  }\n  if (!touchObject.swipeLength) {\n    return state;\n  }\n  if (touchObject.swipeLength > minSwipe) {\n    safePreventDefault(e);\n    if (onSwipe) {\n      onSwipe(swipeDirection);\n    }\n    var slideCount, newSlide;\n    var activeSlide = infinite ? currentSlide : targetSlide;\n    switch (swipeDirection) {\n      case \"left\":\n      case \"up\":\n        newSlide = activeSlide + getSlideCount(spec);\n        slideCount = swipeToSlide ? checkNavigable(spec, newSlide) : newSlide;\n        state[\"currentDirection\"] = 0;\n        break;\n      case \"right\":\n      case \"down\":\n        newSlide = activeSlide - getSlideCount(spec);\n        slideCount = swipeToSlide ? checkNavigable(spec, newSlide) : newSlide;\n        state[\"currentDirection\"] = 1;\n        break;\n      default:\n        slideCount = activeSlide;\n    }\n    state[\"triggerSlideHandler\"] = slideCount;\n  } else {\n    // Adjust the track back to it's original position.\n    var currentLeft = getTrackLeft(spec);\n    state[\"trackStyle\"] = getTrackAnimateCSS(_objectSpread(_objectSpread({}, spec), {}, {\n      left: currentLeft\n    }));\n  }\n  return state;\n};\nvar getNavigableIndexes = exports.getNavigableIndexes = function getNavigableIndexes(spec) {\n  var max = spec.infinite ? spec.slideCount * 2 : spec.slideCount;\n  var breakpoint = spec.infinite ? spec.slidesToShow * -1 : 0;\n  var counter = spec.infinite ? spec.slidesToShow * -1 : 0;\n  var indexes = [];\n  while (breakpoint < max) {\n    indexes.push(breakpoint);\n    breakpoint = counter + spec.slidesToScroll;\n    counter += Math.min(spec.slidesToScroll, spec.slidesToShow);\n  }\n  return indexes;\n};\nvar checkNavigable = exports.checkNavigable = function checkNavigable(spec, index) {\n  var navigables = getNavigableIndexes(spec);\n  var prevNavigable = 0;\n  if (index > navigables[navigables.length - 1]) {\n    index = navigables[navigables.length - 1];\n  } else {\n    for (var n in navigables) {\n      if (index < navigables[n]) {\n        index = prevNavigable;\n        break;\n      }\n      prevNavigable = navigables[n];\n    }\n  }\n  return index;\n};\nvar getSlideCount = exports.getSlideCount = function getSlideCount(spec) {\n  var centerOffset = spec.centerMode ? spec.slideWidth * Math.floor(spec.slidesToShow / 2) : 0;\n  if (spec.swipeToSlide) {\n    var swipedSlide;\n    var slickList = spec.listRef;\n    var slides = slickList.querySelectorAll && slickList.querySelectorAll(\".slick-slide\") || [];\n    Array.from(slides).every(function (slide) {\n      if (!spec.vertical) {\n        if (slide.offsetLeft - centerOffset + getWidth(slide) / 2 > spec.swipeLeft * -1) {\n          swipedSlide = slide;\n          return false;\n        }\n      } else {\n        if (slide.offsetTop + getHeight(slide) / 2 > spec.swipeLeft * -1) {\n          swipedSlide = slide;\n          return false;\n        }\n      }\n      return true;\n    });\n    if (!swipedSlide) {\n      return 0;\n    }\n    var currentIndex = spec.rtl === true ? spec.slideCount - spec.currentSlide : spec.currentSlide;\n    var slidesTraversed = Math.abs(swipedSlide.dataset.index - currentIndex) || 1;\n    return slidesTraversed;\n  } else {\n    return spec.slidesToScroll;\n  }\n};\nvar checkSpecKeys = exports.checkSpecKeys = function checkSpecKeys(spec, keysArray) {\n  return keysArray.reduce(function (value, key) {\n    return value && spec.hasOwnProperty(key);\n  }, true) ? null : console.error(\"Keys Missing:\", spec);\n};\nvar getTrackCSS = exports.getTrackCSS = function getTrackCSS(spec) {\n  checkSpecKeys(spec, [\"left\", \"variableWidth\", \"slideCount\", \"slidesToShow\", \"slideWidth\"]);\n  var trackWidth, trackHeight;\n  var trackChildren = spec.slideCount + 2 * spec.slidesToShow;\n  if (!spec.vertical) {\n    trackWidth = getTotalSlides(spec) * spec.slideWidth;\n  } else {\n    trackHeight = trackChildren * spec.slideHeight;\n  }\n  var style = {\n    opacity: 1,\n    transition: \"\",\n    WebkitTransition: \"\"\n  };\n  if (spec.useTransform) {\n    var WebkitTransform = !spec.vertical ? \"translate3d(\" + spec.left + \"px, 0px, 0px)\" : \"translate3d(0px, \" + spec.left + \"px, 0px)\";\n    var transform = !spec.vertical ? \"translate3d(\" + spec.left + \"px, 0px, 0px)\" : \"translate3d(0px, \" + spec.left + \"px, 0px)\";\n    var msTransform = !spec.vertical ? \"translateX(\" + spec.left + \"px)\" : \"translateY(\" + spec.left + \"px)\";\n    style = _objectSpread(_objectSpread({}, style), {}, {\n      WebkitTransform: WebkitTransform,\n      transform: transform,\n      msTransform: msTransform\n    });\n  } else {\n    if (spec.vertical) {\n      style[\"top\"] = spec.left;\n    } else {\n      style[\"left\"] = spec.left;\n    }\n  }\n  if (spec.fade) style = {\n    opacity: 1\n  };\n  if (trackWidth) style.width = trackWidth;\n  if (trackHeight) style.height = trackHeight;\n\n  // Fallback for IE8\n  if (window && !window.addEventListener && window.attachEvent) {\n    if (!spec.vertical) {\n      style.marginLeft = spec.left + \"px\";\n    } else {\n      style.marginTop = spec.left + \"px\";\n    }\n  }\n  return style;\n};\nvar getTrackAnimateCSS = exports.getTrackAnimateCSS = function getTrackAnimateCSS(spec) {\n  checkSpecKeys(spec, [\"left\", \"variableWidth\", \"slideCount\", \"slidesToShow\", \"slideWidth\", \"speed\", \"cssEase\"]);\n  var style = getTrackCSS(spec);\n  // useCSS is true by default so it can be undefined\n  if (spec.useTransform) {\n    style.WebkitTransition = \"-webkit-transform \" + spec.speed + \"ms \" + spec.cssEase;\n    style.transition = \"transform \" + spec.speed + \"ms \" + spec.cssEase;\n  } else {\n    if (spec.vertical) {\n      style.transition = \"top \" + spec.speed + \"ms \" + spec.cssEase;\n    } else {\n      style.transition = \"left \" + spec.speed + \"ms \" + spec.cssEase;\n    }\n  }\n  return style;\n};\nvar getTrackLeft = exports.getTrackLeft = function getTrackLeft(spec) {\n  if (spec.unslick) {\n    return 0;\n  }\n  checkSpecKeys(spec, [\"slideIndex\", \"trackRef\", \"infinite\", \"centerMode\", \"slideCount\", \"slidesToShow\", \"slidesToScroll\", \"slideWidth\", \"listWidth\", \"variableWidth\", \"slideHeight\"]);\n  var slideIndex = spec.slideIndex,\n    trackRef = spec.trackRef,\n    infinite = spec.infinite,\n    centerMode = spec.centerMode,\n    slideCount = spec.slideCount,\n    slidesToShow = spec.slidesToShow,\n    slidesToScroll = spec.slidesToScroll,\n    slideWidth = spec.slideWidth,\n    listWidth = spec.listWidth,\n    variableWidth = spec.variableWidth,\n    slideHeight = spec.slideHeight,\n    fade = spec.fade,\n    vertical = spec.vertical;\n  var slideOffset = 0;\n  var targetLeft;\n  var targetSlide;\n  var verticalOffset = 0;\n  if (fade || spec.slideCount === 1) {\n    return 0;\n  }\n  var slidesToOffset = 0;\n  if (infinite) {\n    slidesToOffset = -getPreClones(spec); // bring active slide to the beginning of visual area\n    // if next scroll doesn't have enough children, just reach till the end of original slides instead of shifting slidesToScroll children\n    if (slideCount % slidesToScroll !== 0 && slideIndex + slidesToScroll > slideCount) {\n      slidesToOffset = -(slideIndex > slideCount ? slidesToShow - (slideIndex - slideCount) : slideCount % slidesToScroll);\n    }\n    // shift current slide to center of the frame\n    if (centerMode) {\n      slidesToOffset += parseInt(slidesToShow / 2);\n    }\n  } else {\n    if (slideCount % slidesToScroll !== 0 && slideIndex + slidesToScroll > slideCount) {\n      slidesToOffset = slidesToShow - slideCount % slidesToScroll;\n    }\n    if (centerMode) {\n      slidesToOffset = parseInt(slidesToShow / 2);\n    }\n  }\n  slideOffset = slidesToOffset * slideWidth;\n  verticalOffset = slidesToOffset * slideHeight;\n  if (!vertical) {\n    targetLeft = slideIndex * slideWidth * -1 + slideOffset;\n  } else {\n    targetLeft = slideIndex * slideHeight * -1 + verticalOffset;\n  }\n  if (variableWidth === true) {\n    var targetSlideIndex;\n    var trackElem = trackRef && trackRef.node;\n    targetSlideIndex = slideIndex + getPreClones(spec);\n    targetSlide = trackElem && trackElem.childNodes[targetSlideIndex];\n    targetLeft = targetSlide ? targetSlide.offsetLeft * -1 : 0;\n    if (centerMode === true) {\n      targetSlideIndex = infinite ? slideIndex + getPreClones(spec) : slideIndex;\n      targetSlide = trackElem && trackElem.children[targetSlideIndex];\n      targetLeft = 0;\n      for (var slide = 0; slide < targetSlideIndex; slide++) {\n        targetLeft -= trackElem && trackElem.children[slide] && trackElem.children[slide].offsetWidth;\n      }\n      targetLeft -= parseInt(spec.centerPadding);\n      targetLeft += targetSlide && (listWidth - targetSlide.offsetWidth) / 2;\n    }\n  }\n  return targetLeft;\n};\nvar getPreClones = exports.getPreClones = function getPreClones(spec) {\n  if (spec.unslick || !spec.infinite) {\n    return 0;\n  }\n  if (spec.variableWidth) {\n    return spec.slideCount;\n  }\n  return spec.slidesToShow + (spec.centerMode ? 1 : 0);\n};\nvar getPostClones = exports.getPostClones = function getPostClones(spec) {\n  if (spec.unslick || !spec.infinite) {\n    return 0;\n  }\n  return spec.slideCount;\n};\nvar getTotalSlides = exports.getTotalSlides = function getTotalSlides(spec) {\n  return spec.slideCount === 1 ? 1 : getPreClones(spec) + spec.slideCount + getPostClones(spec);\n};\nvar siblingDirection = exports.siblingDirection = function siblingDirection(spec) {\n  if (spec.targetSlide > spec.currentSlide) {\n    if (spec.targetSlide > spec.currentSlide + slidesOnRight(spec)) {\n      return \"left\";\n    }\n    return \"right\";\n  } else {\n    if (spec.targetSlide < spec.currentSlide - slidesOnLeft(spec)) {\n      return \"right\";\n    }\n    return \"left\";\n  }\n};\nvar slidesOnRight = exports.slidesOnRight = function slidesOnRight(_ref) {\n  var slidesToShow = _ref.slidesToShow,\n    centerMode = _ref.centerMode,\n    rtl = _ref.rtl,\n    centerPadding = _ref.centerPadding;\n  // returns no of slides on the right of active slide\n  if (centerMode) {\n    var right = (slidesToShow - 1) / 2 + 1;\n    if (parseInt(centerPadding) > 0) right += 1;\n    if (rtl && slidesToShow % 2 === 0) right += 1;\n    return right;\n  }\n  if (rtl) {\n    return 0;\n  }\n  return slidesToShow - 1;\n};\nvar slidesOnLeft = exports.slidesOnLeft = function slidesOnLeft(_ref2) {\n  var slidesToShow = _ref2.slidesToShow,\n    centerMode = _ref2.centerMode,\n    rtl = _ref2.rtl,\n    centerPadding = _ref2.centerPadding;\n  // returns no of slides on the left of active slide\n  if (centerMode) {\n    var left = (slidesToShow - 1) / 2 + 1;\n    if (parseInt(centerPadding) > 0) left += 1;\n    if (!rtl && slidesToShow % 2 === 0) left += 1;\n    return left;\n  }\n  if (rtl) {\n    return slidesToShow - 1;\n  }\n  return 0;\n};\nvar canUseDOM = exports.canUseDOM = function canUseDOM() {\n  return !!(typeof window !== \"undefined\" && window.document && window.document.createElement);\n};\nvar validSettings = exports.validSettings = Object.keys(_defaultProps[\"default\"]);\nfunction filterSettings(settings) {\n  return validSettings.reduce(function (acc, settingName) {\n    if (settings.hasOwnProperty(settingName)) {\n      acc[settingName] = settings[settingName];\n    }\n    return acc;\n  }, {});\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "checkSpecKeys", "checkNavigable", "changeSlide", "canUseDOM", "canGoNext", "clamp", "extractObject", "filterSettings", "validSettings", "swipeStart", "swipeMove", "swipeEnd", "slidesOnRight", "slidesOnLeft", "<PERSON><PERSON><PERSON><PERSON>", "siblingDirection", "safePreventDefault", "lazyStartIndex", "lazySlidesOnRight", "lazySlidesOnLeft", "lazyEndIndex", "<PERSON><PERSON><PERSON><PERSON>", "initializedState", "getWidth", "getTrackLeft", "getTrackCSS", "getTrackAnimateCSS", "getTotalSlides", "getSwipeDirection", "getSlideCount", "getRequiredLazySlides", "getPreClones", "getPostClones", "getOnDemandLazySlides", "getNavigableIndexes", "getHeight", "_react", "_interopRequireDefault", "require", "_defaultProps", "obj", "__esModule", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "e", "r", "t", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "key", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "i", "_toPrimitive", "String", "toPrimitive", "call", "TypeError", "Number", "number", "lowerBound", "upperBound", "Math", "max", "min", "event", "passiveEvents", "includes", "_reactName", "preventDefault", "spec", "onDemandSlides", "startIndex", "endIndex", "slideIndex", "lazyLoadedList", "indexOf", "requiredSlides", "currentSlide", "centerMode", "floor", "slidesToShow", "parseInt", "centerPadding", "elem", "offsetWidth", "offsetHeight", "touchObject", "verticalSwiping", "undefined", "xDist", "yDist", "swipeAngle", "startX", "curX", "startY", "curY", "atan2", "round", "PI", "abs", "canGo", "infinite", "slideCount", "newObject", "Children", "count", "children", "listNode", "listRef", "listWidth", "ceil", "trackNode", "trackRef", "node", "trackWidth", "slideWidth", "vertical", "centerPaddingAdj", "slice", "slideHeight", "querySelector", "listHeight", "initialSlide", "rtl", "slidesToLoad", "concat", "state", "autoplaying", "autoplay", "waitForAnimate", "animating", "fade", "index", "lazyLoad", "slidesToScroll", "useCSS", "animationSlide", "finalSlide", "animationLeft", "finalLeft", "nextState", "targetSlide", "trackStyle", "left", "swipeLeft", "options", "indexOffset", "previousInt", "slideOffset", "unevenOffset", "previousTargetSlide", "message", "direction", "accessibility", "target", "tagName", "match", "keyCode", "swipe", "draggable", "type", "dragging", "touches", "pageX", "clientX", "pageY", "clientY", "scrolling", "swipeToSlide", "edgeFriction", "edgeDragged", "onEdge", "swiped", "swiping", "swipeEvent", "curL<PERSON>t", "swipe<PERSON><PERSON><PERSON>", "sqrt", "pow", "verticalSwipeLength", "positionOffset", "dotCount", "swipeDirection", "touchSwipeLength", "touchThreshold", "onSwipe", "minSwipe", "newSlide", "activeSlide", "currentLeft", "breakpoint", "counter", "indexes", "navigables", "prevNavigable", "n", "centerOffset", "swipedSlide", "slickList", "slides", "querySelectorAll", "Array", "from", "every", "slide", "offsetLeft", "offsetTop", "currentIndex", "slidesTraversed", "dataset", "keysArray", "reduce", "hasOwnProperty", "console", "error", "trackHeight", "trackChildren", "style", "opacity", "transition", "WebkitTransition", "useTransform", "WebkitTransform", "transform", "msTransform", "width", "height", "window", "addEventListener", "attachEvent", "marginLeft", "marginTop", "speed", "cssEase", "unslick", "variableWidth", "targetLeft", "verticalOffset", "slidesToOffset", "targetSlideIndex", "trackElem", "childNodes", "_ref", "right", "_ref2", "document", "createElement", "settings", "acc", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/IdeaProjects/FoodOrdering-main/FoodOrdering-main/frontend/node_modules/react-slick/lib/utils/innerSliderUtils.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.checkSpecKeys = exports.checkNavigable = exports.changeSlide = exports.canUseDOM = exports.canGoNext = void 0;\nexports.clamp = clamp;\nexports.extractObject = void 0;\nexports.filterSettings = filterSettings;\nexports.validSettings = exports.swipeStart = exports.swipeMove = exports.swipeEnd = exports.slidesOnRight = exports.slidesOnLeft = exports.slideHandler = exports.siblingDirection = exports.safePreventDefault = exports.lazyStartIndex = exports.lazySlidesOnRight = exports.lazySlidesOnLeft = exports.lazyEndIndex = exports.keyHandler = exports.initializedState = exports.getWidth = exports.getTrackLeft = exports.getTrackCSS = exports.getTrackAnimateCSS = exports.getTotalSlides = exports.getSwipeDirection = exports.getSlideCount = exports.getRequiredLazySlides = exports.getPreClones = exports.getPostClones = exports.getOnDemandLazySlides = exports.getNavigableIndexes = exports.getHeight = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _defaultProps = _interopRequireDefault(require(\"../default-props\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : String(i); }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction clamp(number, lowerBound, upperBound) {\n  return Math.max(lowerBound, Math.min(number, upperBound));\n}\nvar safePreventDefault = exports.safePreventDefault = function safePreventDefault(event) {\n  var passiveEvents = [\"onTouchStart\", \"onTouchMove\", \"onWheel\"];\n  if (!passiveEvents.includes(event._reactName)) {\n    event.preventDefault();\n  }\n};\nvar getOnDemandLazySlides = exports.getOnDemandLazySlides = function getOnDemandLazySlides(spec) {\n  var onDemandSlides = [];\n  var startIndex = lazyStartIndex(spec);\n  var endIndex = lazyEndIndex(spec);\n  for (var slideIndex = startIndex; slideIndex < endIndex; slideIndex++) {\n    if (spec.lazyLoadedList.indexOf(slideIndex) < 0) {\n      onDemandSlides.push(slideIndex);\n    }\n  }\n  return onDemandSlides;\n};\n\n// return list of slides that need to be present\nvar getRequiredLazySlides = exports.getRequiredLazySlides = function getRequiredLazySlides(spec) {\n  var requiredSlides = [];\n  var startIndex = lazyStartIndex(spec);\n  var endIndex = lazyEndIndex(spec);\n  for (var slideIndex = startIndex; slideIndex < endIndex; slideIndex++) {\n    requiredSlides.push(slideIndex);\n  }\n  return requiredSlides;\n};\n\n// startIndex that needs to be present\nvar lazyStartIndex = exports.lazyStartIndex = function lazyStartIndex(spec) {\n  return spec.currentSlide - lazySlidesOnLeft(spec);\n};\nvar lazyEndIndex = exports.lazyEndIndex = function lazyEndIndex(spec) {\n  return spec.currentSlide + lazySlidesOnRight(spec);\n};\nvar lazySlidesOnLeft = exports.lazySlidesOnLeft = function lazySlidesOnLeft(spec) {\n  return spec.centerMode ? Math.floor(spec.slidesToShow / 2) + (parseInt(spec.centerPadding) > 0 ? 1 : 0) : 0;\n};\nvar lazySlidesOnRight = exports.lazySlidesOnRight = function lazySlidesOnRight(spec) {\n  return spec.centerMode ? Math.floor((spec.slidesToShow - 1) / 2) + 1 + (parseInt(spec.centerPadding) > 0 ? 1 : 0) : spec.slidesToShow;\n};\n\n// get width of an element\nvar getWidth = exports.getWidth = function getWidth(elem) {\n  return elem && elem.offsetWidth || 0;\n};\nvar getHeight = exports.getHeight = function getHeight(elem) {\n  return elem && elem.offsetHeight || 0;\n};\nvar getSwipeDirection = exports.getSwipeDirection = function getSwipeDirection(touchObject) {\n  var verticalSwiping = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var xDist, yDist, r, swipeAngle;\n  xDist = touchObject.startX - touchObject.curX;\n  yDist = touchObject.startY - touchObject.curY;\n  r = Math.atan2(yDist, xDist);\n  swipeAngle = Math.round(r * 180 / Math.PI);\n  if (swipeAngle < 0) {\n    swipeAngle = 360 - Math.abs(swipeAngle);\n  }\n  if (swipeAngle <= 45 && swipeAngle >= 0 || swipeAngle <= 360 && swipeAngle >= 315) {\n    return \"left\";\n  }\n  if (swipeAngle >= 135 && swipeAngle <= 225) {\n    return \"right\";\n  }\n  if (verticalSwiping === true) {\n    if (swipeAngle >= 35 && swipeAngle <= 135) {\n      return \"up\";\n    } else {\n      return \"down\";\n    }\n  }\n  return \"vertical\";\n};\n\n// whether or not we can go next\nvar canGoNext = exports.canGoNext = function canGoNext(spec) {\n  var canGo = true;\n  if (!spec.infinite) {\n    if (spec.centerMode && spec.currentSlide >= spec.slideCount - 1) {\n      canGo = false;\n    } else if (spec.slideCount <= spec.slidesToShow || spec.currentSlide >= spec.slideCount - spec.slidesToShow) {\n      canGo = false;\n    }\n  }\n  return canGo;\n};\n\n// given an object and a list of keys, return new object with given keys\nvar extractObject = exports.extractObject = function extractObject(spec, keys) {\n  var newObject = {};\n  keys.forEach(function (key) {\n    return newObject[key] = spec[key];\n  });\n  return newObject;\n};\n\n// get initialized state\nvar initializedState = exports.initializedState = function initializedState(spec) {\n  // spec also contains listRef, trackRef\n  var slideCount = _react[\"default\"].Children.count(spec.children);\n  var listNode = spec.listRef;\n  var listWidth = Math.ceil(getWidth(listNode));\n  var trackNode = spec.trackRef && spec.trackRef.node;\n  var trackWidth = Math.ceil(getWidth(trackNode));\n  var slideWidth;\n  if (!spec.vertical) {\n    var centerPaddingAdj = spec.centerMode && parseInt(spec.centerPadding) * 2;\n    if (typeof spec.centerPadding === \"string\" && spec.centerPadding.slice(-1) === \"%\") {\n      centerPaddingAdj *= listWidth / 100;\n    }\n    slideWidth = Math.ceil((listWidth - centerPaddingAdj) / spec.slidesToShow);\n  } else {\n    slideWidth = listWidth;\n  }\n  var slideHeight = listNode && getHeight(listNode.querySelector('[data-index=\"0\"]'));\n  var listHeight = slideHeight * spec.slidesToShow;\n  var currentSlide = spec.currentSlide === undefined ? spec.initialSlide : spec.currentSlide;\n  if (spec.rtl && spec.currentSlide === undefined) {\n    currentSlide = slideCount - 1 - spec.initialSlide;\n  }\n  var lazyLoadedList = spec.lazyLoadedList || [];\n  var slidesToLoad = getOnDemandLazySlides(_objectSpread(_objectSpread({}, spec), {}, {\n    currentSlide: currentSlide,\n    lazyLoadedList: lazyLoadedList\n  }));\n  lazyLoadedList = lazyLoadedList.concat(slidesToLoad);\n  var state = {\n    slideCount: slideCount,\n    slideWidth: slideWidth,\n    listWidth: listWidth,\n    trackWidth: trackWidth,\n    currentSlide: currentSlide,\n    slideHeight: slideHeight,\n    listHeight: listHeight,\n    lazyLoadedList: lazyLoadedList\n  };\n  if (spec.autoplaying === null && spec.autoplay) {\n    state[\"autoplaying\"] = \"playing\";\n  }\n  return state;\n};\nvar slideHandler = exports.slideHandler = function slideHandler(spec) {\n  var waitForAnimate = spec.waitForAnimate,\n    animating = spec.animating,\n    fade = spec.fade,\n    infinite = spec.infinite,\n    index = spec.index,\n    slideCount = spec.slideCount,\n    lazyLoad = spec.lazyLoad,\n    currentSlide = spec.currentSlide,\n    centerMode = spec.centerMode,\n    slidesToScroll = spec.slidesToScroll,\n    slidesToShow = spec.slidesToShow,\n    useCSS = spec.useCSS;\n  var lazyLoadedList = spec.lazyLoadedList;\n  if (waitForAnimate && animating) return {};\n  var animationSlide = index,\n    finalSlide,\n    animationLeft,\n    finalLeft;\n  var state = {},\n    nextState = {};\n  var targetSlide = infinite ? index : clamp(index, 0, slideCount - 1);\n  if (fade) {\n    if (!infinite && (index < 0 || index >= slideCount)) return {};\n    if (index < 0) {\n      animationSlide = index + slideCount;\n    } else if (index >= slideCount) {\n      animationSlide = index - slideCount;\n    }\n    if (lazyLoad && lazyLoadedList.indexOf(animationSlide) < 0) {\n      lazyLoadedList = lazyLoadedList.concat(animationSlide);\n    }\n    state = {\n      animating: true,\n      currentSlide: animationSlide,\n      lazyLoadedList: lazyLoadedList,\n      targetSlide: animationSlide\n    };\n    nextState = {\n      animating: false,\n      targetSlide: animationSlide\n    };\n  } else {\n    finalSlide = animationSlide;\n    if (animationSlide < 0) {\n      finalSlide = animationSlide + slideCount;\n      if (!infinite) finalSlide = 0;else if (slideCount % slidesToScroll !== 0) finalSlide = slideCount - slideCount % slidesToScroll;\n    } else if (!canGoNext(spec) && animationSlide > currentSlide) {\n      animationSlide = finalSlide = currentSlide;\n    } else if (centerMode && animationSlide >= slideCount) {\n      animationSlide = infinite ? slideCount : slideCount - 1;\n      finalSlide = infinite ? 0 : slideCount - 1;\n    } else if (animationSlide >= slideCount) {\n      finalSlide = animationSlide - slideCount;\n      if (!infinite) finalSlide = slideCount - slidesToShow;else if (slideCount % slidesToScroll !== 0) finalSlide = 0;\n    }\n    if (!infinite && animationSlide + slidesToShow >= slideCount) {\n      finalSlide = slideCount - slidesToShow;\n    }\n    animationLeft = getTrackLeft(_objectSpread(_objectSpread({}, spec), {}, {\n      slideIndex: animationSlide\n    }));\n    finalLeft = getTrackLeft(_objectSpread(_objectSpread({}, spec), {}, {\n      slideIndex: finalSlide\n    }));\n    if (!infinite) {\n      if (animationLeft === finalLeft) animationSlide = finalSlide;\n      animationLeft = finalLeft;\n    }\n    if (lazyLoad) {\n      lazyLoadedList = lazyLoadedList.concat(getOnDemandLazySlides(_objectSpread(_objectSpread({}, spec), {}, {\n        currentSlide: animationSlide\n      })));\n    }\n    if (!useCSS) {\n      state = {\n        currentSlide: finalSlide,\n        trackStyle: getTrackCSS(_objectSpread(_objectSpread({}, spec), {}, {\n          left: finalLeft\n        })),\n        lazyLoadedList: lazyLoadedList,\n        targetSlide: targetSlide\n      };\n    } else {\n      state = {\n        animating: true,\n        currentSlide: finalSlide,\n        trackStyle: getTrackAnimateCSS(_objectSpread(_objectSpread({}, spec), {}, {\n          left: animationLeft\n        })),\n        lazyLoadedList: lazyLoadedList,\n        targetSlide: targetSlide\n      };\n      nextState = {\n        animating: false,\n        currentSlide: finalSlide,\n        trackStyle: getTrackCSS(_objectSpread(_objectSpread({}, spec), {}, {\n          left: finalLeft\n        })),\n        swipeLeft: null,\n        targetSlide: targetSlide\n      };\n    }\n  }\n  return {\n    state: state,\n    nextState: nextState\n  };\n};\nvar changeSlide = exports.changeSlide = function changeSlide(spec, options) {\n  var indexOffset, previousInt, slideOffset, unevenOffset, targetSlide;\n  var slidesToScroll = spec.slidesToScroll,\n    slidesToShow = spec.slidesToShow,\n    slideCount = spec.slideCount,\n    currentSlide = spec.currentSlide,\n    previousTargetSlide = spec.targetSlide,\n    lazyLoad = spec.lazyLoad,\n    infinite = spec.infinite;\n  unevenOffset = slideCount % slidesToScroll !== 0;\n  indexOffset = unevenOffset ? 0 : (slideCount - currentSlide) % slidesToScroll;\n  if (options.message === \"previous\") {\n    slideOffset = indexOffset === 0 ? slidesToScroll : slidesToShow - indexOffset;\n    targetSlide = currentSlide - slideOffset;\n    if (lazyLoad && !infinite) {\n      previousInt = currentSlide - slideOffset;\n      targetSlide = previousInt === -1 ? slideCount - 1 : previousInt;\n    }\n    if (!infinite) {\n      targetSlide = previousTargetSlide - slidesToScroll;\n    }\n  } else if (options.message === \"next\") {\n    slideOffset = indexOffset === 0 ? slidesToScroll : indexOffset;\n    targetSlide = currentSlide + slideOffset;\n    if (lazyLoad && !infinite) {\n      targetSlide = (currentSlide + slidesToScroll) % slideCount + indexOffset;\n    }\n    if (!infinite) {\n      targetSlide = previousTargetSlide + slidesToScroll;\n    }\n  } else if (options.message === \"dots\") {\n    // Click on dots\n    targetSlide = options.index * options.slidesToScroll;\n  } else if (options.message === \"children\") {\n    // Click on the slides\n    targetSlide = options.index;\n    if (infinite) {\n      var direction = siblingDirection(_objectSpread(_objectSpread({}, spec), {}, {\n        targetSlide: targetSlide\n      }));\n      if (targetSlide > options.currentSlide && direction === \"left\") {\n        targetSlide = targetSlide - slideCount;\n      } else if (targetSlide < options.currentSlide && direction === \"right\") {\n        targetSlide = targetSlide + slideCount;\n      }\n    }\n  } else if (options.message === \"index\") {\n    targetSlide = Number(options.index);\n  }\n  return targetSlide;\n};\nvar keyHandler = exports.keyHandler = function keyHandler(e, accessibility, rtl) {\n  if (e.target.tagName.match(\"TEXTAREA|INPUT|SELECT\") || !accessibility) return \"\";\n  if (e.keyCode === 37) return rtl ? \"next\" : \"previous\";\n  if (e.keyCode === 39) return rtl ? \"previous\" : \"next\";\n  return \"\";\n};\nvar swipeStart = exports.swipeStart = function swipeStart(e, swipe, draggable) {\n  e.target.tagName === \"IMG\" && safePreventDefault(e);\n  if (!swipe || !draggable && e.type.indexOf(\"mouse\") !== -1) return \"\";\n  return {\n    dragging: true,\n    touchObject: {\n      startX: e.touches ? e.touches[0].pageX : e.clientX,\n      startY: e.touches ? e.touches[0].pageY : e.clientY,\n      curX: e.touches ? e.touches[0].pageX : e.clientX,\n      curY: e.touches ? e.touches[0].pageY : e.clientY\n    }\n  };\n};\nvar swipeMove = exports.swipeMove = function swipeMove(e, spec) {\n  // spec also contains, trackRef and slideIndex\n  var scrolling = spec.scrolling,\n    animating = spec.animating,\n    vertical = spec.vertical,\n    swipeToSlide = spec.swipeToSlide,\n    verticalSwiping = spec.verticalSwiping,\n    rtl = spec.rtl,\n    currentSlide = spec.currentSlide,\n    edgeFriction = spec.edgeFriction,\n    edgeDragged = spec.edgeDragged,\n    onEdge = spec.onEdge,\n    swiped = spec.swiped,\n    swiping = spec.swiping,\n    slideCount = spec.slideCount,\n    slidesToScroll = spec.slidesToScroll,\n    infinite = spec.infinite,\n    touchObject = spec.touchObject,\n    swipeEvent = spec.swipeEvent,\n    listHeight = spec.listHeight,\n    listWidth = spec.listWidth;\n  if (scrolling) return;\n  if (animating) return safePreventDefault(e);\n  if (vertical && swipeToSlide && verticalSwiping) safePreventDefault(e);\n  var swipeLeft,\n    state = {};\n  var curLeft = getTrackLeft(spec);\n  touchObject.curX = e.touches ? e.touches[0].pageX : e.clientX;\n  touchObject.curY = e.touches ? e.touches[0].pageY : e.clientY;\n  touchObject.swipeLength = Math.round(Math.sqrt(Math.pow(touchObject.curX - touchObject.startX, 2)));\n  var verticalSwipeLength = Math.round(Math.sqrt(Math.pow(touchObject.curY - touchObject.startY, 2)));\n  if (!verticalSwiping && !swiping && verticalSwipeLength > 10) {\n    return {\n      scrolling: true\n    };\n  }\n  if (verticalSwiping) touchObject.swipeLength = verticalSwipeLength;\n  var positionOffset = (!rtl ? 1 : -1) * (touchObject.curX > touchObject.startX ? 1 : -1);\n  if (verticalSwiping) positionOffset = touchObject.curY > touchObject.startY ? 1 : -1;\n  var dotCount = Math.ceil(slideCount / slidesToScroll);\n  var swipeDirection = getSwipeDirection(spec.touchObject, verticalSwiping);\n  var touchSwipeLength = touchObject.swipeLength;\n  if (!infinite) {\n    if (currentSlide === 0 && (swipeDirection === \"right\" || swipeDirection === \"down\") || currentSlide + 1 >= dotCount && (swipeDirection === \"left\" || swipeDirection === \"up\") || !canGoNext(spec) && (swipeDirection === \"left\" || swipeDirection === \"up\")) {\n      touchSwipeLength = touchObject.swipeLength * edgeFriction;\n      if (edgeDragged === false && onEdge) {\n        onEdge(swipeDirection);\n        state[\"edgeDragged\"] = true;\n      }\n    }\n  }\n  if (!swiped && swipeEvent) {\n    swipeEvent(swipeDirection);\n    state[\"swiped\"] = true;\n  }\n  if (!vertical) {\n    if (!rtl) {\n      swipeLeft = curLeft + touchSwipeLength * positionOffset;\n    } else {\n      swipeLeft = curLeft - touchSwipeLength * positionOffset;\n    }\n  } else {\n    swipeLeft = curLeft + touchSwipeLength * (listHeight / listWidth) * positionOffset;\n  }\n  if (verticalSwiping) {\n    swipeLeft = curLeft + touchSwipeLength * positionOffset;\n  }\n  state = _objectSpread(_objectSpread({}, state), {}, {\n    touchObject: touchObject,\n    swipeLeft: swipeLeft,\n    trackStyle: getTrackCSS(_objectSpread(_objectSpread({}, spec), {}, {\n      left: swipeLeft\n    }))\n  });\n  if (Math.abs(touchObject.curX - touchObject.startX) < Math.abs(touchObject.curY - touchObject.startY) * 0.8) {\n    return state;\n  }\n  if (touchObject.swipeLength > 10) {\n    state[\"swiping\"] = true;\n    safePreventDefault(e);\n  }\n  return state;\n};\nvar swipeEnd = exports.swipeEnd = function swipeEnd(e, spec) {\n  var dragging = spec.dragging,\n    swipe = spec.swipe,\n    touchObject = spec.touchObject,\n    listWidth = spec.listWidth,\n    touchThreshold = spec.touchThreshold,\n    verticalSwiping = spec.verticalSwiping,\n    listHeight = spec.listHeight,\n    swipeToSlide = spec.swipeToSlide,\n    scrolling = spec.scrolling,\n    onSwipe = spec.onSwipe,\n    targetSlide = spec.targetSlide,\n    currentSlide = spec.currentSlide,\n    infinite = spec.infinite;\n  if (!dragging) {\n    if (swipe) safePreventDefault(e);\n    return {};\n  }\n  var minSwipe = verticalSwiping ? listHeight / touchThreshold : listWidth / touchThreshold;\n  var swipeDirection = getSwipeDirection(touchObject, verticalSwiping);\n  // reset the state of touch related state variables.\n  var state = {\n    dragging: false,\n    edgeDragged: false,\n    scrolling: false,\n    swiping: false,\n    swiped: false,\n    swipeLeft: null,\n    touchObject: {}\n  };\n  if (scrolling) {\n    return state;\n  }\n  if (!touchObject.swipeLength) {\n    return state;\n  }\n  if (touchObject.swipeLength > minSwipe) {\n    safePreventDefault(e);\n    if (onSwipe) {\n      onSwipe(swipeDirection);\n    }\n    var slideCount, newSlide;\n    var activeSlide = infinite ? currentSlide : targetSlide;\n    switch (swipeDirection) {\n      case \"left\":\n      case \"up\":\n        newSlide = activeSlide + getSlideCount(spec);\n        slideCount = swipeToSlide ? checkNavigable(spec, newSlide) : newSlide;\n        state[\"currentDirection\"] = 0;\n        break;\n      case \"right\":\n      case \"down\":\n        newSlide = activeSlide - getSlideCount(spec);\n        slideCount = swipeToSlide ? checkNavigable(spec, newSlide) : newSlide;\n        state[\"currentDirection\"] = 1;\n        break;\n      default:\n        slideCount = activeSlide;\n    }\n    state[\"triggerSlideHandler\"] = slideCount;\n  } else {\n    // Adjust the track back to it's original position.\n    var currentLeft = getTrackLeft(spec);\n    state[\"trackStyle\"] = getTrackAnimateCSS(_objectSpread(_objectSpread({}, spec), {}, {\n      left: currentLeft\n    }));\n  }\n  return state;\n};\nvar getNavigableIndexes = exports.getNavigableIndexes = function getNavigableIndexes(spec) {\n  var max = spec.infinite ? spec.slideCount * 2 : spec.slideCount;\n  var breakpoint = spec.infinite ? spec.slidesToShow * -1 : 0;\n  var counter = spec.infinite ? spec.slidesToShow * -1 : 0;\n  var indexes = [];\n  while (breakpoint < max) {\n    indexes.push(breakpoint);\n    breakpoint = counter + spec.slidesToScroll;\n    counter += Math.min(spec.slidesToScroll, spec.slidesToShow);\n  }\n  return indexes;\n};\nvar checkNavigable = exports.checkNavigable = function checkNavigable(spec, index) {\n  var navigables = getNavigableIndexes(spec);\n  var prevNavigable = 0;\n  if (index > navigables[navigables.length - 1]) {\n    index = navigables[navigables.length - 1];\n  } else {\n    for (var n in navigables) {\n      if (index < navigables[n]) {\n        index = prevNavigable;\n        break;\n      }\n      prevNavigable = navigables[n];\n    }\n  }\n  return index;\n};\nvar getSlideCount = exports.getSlideCount = function getSlideCount(spec) {\n  var centerOffset = spec.centerMode ? spec.slideWidth * Math.floor(spec.slidesToShow / 2) : 0;\n  if (spec.swipeToSlide) {\n    var swipedSlide;\n    var slickList = spec.listRef;\n    var slides = slickList.querySelectorAll && slickList.querySelectorAll(\".slick-slide\") || [];\n    Array.from(slides).every(function (slide) {\n      if (!spec.vertical) {\n        if (slide.offsetLeft - centerOffset + getWidth(slide) / 2 > spec.swipeLeft * -1) {\n          swipedSlide = slide;\n          return false;\n        }\n      } else {\n        if (slide.offsetTop + getHeight(slide) / 2 > spec.swipeLeft * -1) {\n          swipedSlide = slide;\n          return false;\n        }\n      }\n      return true;\n    });\n    if (!swipedSlide) {\n      return 0;\n    }\n    var currentIndex = spec.rtl === true ? spec.slideCount - spec.currentSlide : spec.currentSlide;\n    var slidesTraversed = Math.abs(swipedSlide.dataset.index - currentIndex) || 1;\n    return slidesTraversed;\n  } else {\n    return spec.slidesToScroll;\n  }\n};\nvar checkSpecKeys = exports.checkSpecKeys = function checkSpecKeys(spec, keysArray) {\n  return keysArray.reduce(function (value, key) {\n    return value && spec.hasOwnProperty(key);\n  }, true) ? null : console.error(\"Keys Missing:\", spec);\n};\nvar getTrackCSS = exports.getTrackCSS = function getTrackCSS(spec) {\n  checkSpecKeys(spec, [\"left\", \"variableWidth\", \"slideCount\", \"slidesToShow\", \"slideWidth\"]);\n  var trackWidth, trackHeight;\n  var trackChildren = spec.slideCount + 2 * spec.slidesToShow;\n  if (!spec.vertical) {\n    trackWidth = getTotalSlides(spec) * spec.slideWidth;\n  } else {\n    trackHeight = trackChildren * spec.slideHeight;\n  }\n  var style = {\n    opacity: 1,\n    transition: \"\",\n    WebkitTransition: \"\"\n  };\n  if (spec.useTransform) {\n    var WebkitTransform = !spec.vertical ? \"translate3d(\" + spec.left + \"px, 0px, 0px)\" : \"translate3d(0px, \" + spec.left + \"px, 0px)\";\n    var transform = !spec.vertical ? \"translate3d(\" + spec.left + \"px, 0px, 0px)\" : \"translate3d(0px, \" + spec.left + \"px, 0px)\";\n    var msTransform = !spec.vertical ? \"translateX(\" + spec.left + \"px)\" : \"translateY(\" + spec.left + \"px)\";\n    style = _objectSpread(_objectSpread({}, style), {}, {\n      WebkitTransform: WebkitTransform,\n      transform: transform,\n      msTransform: msTransform\n    });\n  } else {\n    if (spec.vertical) {\n      style[\"top\"] = spec.left;\n    } else {\n      style[\"left\"] = spec.left;\n    }\n  }\n  if (spec.fade) style = {\n    opacity: 1\n  };\n  if (trackWidth) style.width = trackWidth;\n  if (trackHeight) style.height = trackHeight;\n\n  // Fallback for IE8\n  if (window && !window.addEventListener && window.attachEvent) {\n    if (!spec.vertical) {\n      style.marginLeft = spec.left + \"px\";\n    } else {\n      style.marginTop = spec.left + \"px\";\n    }\n  }\n  return style;\n};\nvar getTrackAnimateCSS = exports.getTrackAnimateCSS = function getTrackAnimateCSS(spec) {\n  checkSpecKeys(spec, [\"left\", \"variableWidth\", \"slideCount\", \"slidesToShow\", \"slideWidth\", \"speed\", \"cssEase\"]);\n  var style = getTrackCSS(spec);\n  // useCSS is true by default so it can be undefined\n  if (spec.useTransform) {\n    style.WebkitTransition = \"-webkit-transform \" + spec.speed + \"ms \" + spec.cssEase;\n    style.transition = \"transform \" + spec.speed + \"ms \" + spec.cssEase;\n  } else {\n    if (spec.vertical) {\n      style.transition = \"top \" + spec.speed + \"ms \" + spec.cssEase;\n    } else {\n      style.transition = \"left \" + spec.speed + \"ms \" + spec.cssEase;\n    }\n  }\n  return style;\n};\nvar getTrackLeft = exports.getTrackLeft = function getTrackLeft(spec) {\n  if (spec.unslick) {\n    return 0;\n  }\n  checkSpecKeys(spec, [\"slideIndex\", \"trackRef\", \"infinite\", \"centerMode\", \"slideCount\", \"slidesToShow\", \"slidesToScroll\", \"slideWidth\", \"listWidth\", \"variableWidth\", \"slideHeight\"]);\n  var slideIndex = spec.slideIndex,\n    trackRef = spec.trackRef,\n    infinite = spec.infinite,\n    centerMode = spec.centerMode,\n    slideCount = spec.slideCount,\n    slidesToShow = spec.slidesToShow,\n    slidesToScroll = spec.slidesToScroll,\n    slideWidth = spec.slideWidth,\n    listWidth = spec.listWidth,\n    variableWidth = spec.variableWidth,\n    slideHeight = spec.slideHeight,\n    fade = spec.fade,\n    vertical = spec.vertical;\n  var slideOffset = 0;\n  var targetLeft;\n  var targetSlide;\n  var verticalOffset = 0;\n  if (fade || spec.slideCount === 1) {\n    return 0;\n  }\n  var slidesToOffset = 0;\n  if (infinite) {\n    slidesToOffset = -getPreClones(spec); // bring active slide to the beginning of visual area\n    // if next scroll doesn't have enough children, just reach till the end of original slides instead of shifting slidesToScroll children\n    if (slideCount % slidesToScroll !== 0 && slideIndex + slidesToScroll > slideCount) {\n      slidesToOffset = -(slideIndex > slideCount ? slidesToShow - (slideIndex - slideCount) : slideCount % slidesToScroll);\n    }\n    // shift current slide to center of the frame\n    if (centerMode) {\n      slidesToOffset += parseInt(slidesToShow / 2);\n    }\n  } else {\n    if (slideCount % slidesToScroll !== 0 && slideIndex + slidesToScroll > slideCount) {\n      slidesToOffset = slidesToShow - slideCount % slidesToScroll;\n    }\n    if (centerMode) {\n      slidesToOffset = parseInt(slidesToShow / 2);\n    }\n  }\n  slideOffset = slidesToOffset * slideWidth;\n  verticalOffset = slidesToOffset * slideHeight;\n  if (!vertical) {\n    targetLeft = slideIndex * slideWidth * -1 + slideOffset;\n  } else {\n    targetLeft = slideIndex * slideHeight * -1 + verticalOffset;\n  }\n  if (variableWidth === true) {\n    var targetSlideIndex;\n    var trackElem = trackRef && trackRef.node;\n    targetSlideIndex = slideIndex + getPreClones(spec);\n    targetSlide = trackElem && trackElem.childNodes[targetSlideIndex];\n    targetLeft = targetSlide ? targetSlide.offsetLeft * -1 : 0;\n    if (centerMode === true) {\n      targetSlideIndex = infinite ? slideIndex + getPreClones(spec) : slideIndex;\n      targetSlide = trackElem && trackElem.children[targetSlideIndex];\n      targetLeft = 0;\n      for (var slide = 0; slide < targetSlideIndex; slide++) {\n        targetLeft -= trackElem && trackElem.children[slide] && trackElem.children[slide].offsetWidth;\n      }\n      targetLeft -= parseInt(spec.centerPadding);\n      targetLeft += targetSlide && (listWidth - targetSlide.offsetWidth) / 2;\n    }\n  }\n  return targetLeft;\n};\nvar getPreClones = exports.getPreClones = function getPreClones(spec) {\n  if (spec.unslick || !spec.infinite) {\n    return 0;\n  }\n  if (spec.variableWidth) {\n    return spec.slideCount;\n  }\n  return spec.slidesToShow + (spec.centerMode ? 1 : 0);\n};\nvar getPostClones = exports.getPostClones = function getPostClones(spec) {\n  if (spec.unslick || !spec.infinite) {\n    return 0;\n  }\n  return spec.slideCount;\n};\nvar getTotalSlides = exports.getTotalSlides = function getTotalSlides(spec) {\n  return spec.slideCount === 1 ? 1 : getPreClones(spec) + spec.slideCount + getPostClones(spec);\n};\nvar siblingDirection = exports.siblingDirection = function siblingDirection(spec) {\n  if (spec.targetSlide > spec.currentSlide) {\n    if (spec.targetSlide > spec.currentSlide + slidesOnRight(spec)) {\n      return \"left\";\n    }\n    return \"right\";\n  } else {\n    if (spec.targetSlide < spec.currentSlide - slidesOnLeft(spec)) {\n      return \"right\";\n    }\n    return \"left\";\n  }\n};\nvar slidesOnRight = exports.slidesOnRight = function slidesOnRight(_ref) {\n  var slidesToShow = _ref.slidesToShow,\n    centerMode = _ref.centerMode,\n    rtl = _ref.rtl,\n    centerPadding = _ref.centerPadding;\n  // returns no of slides on the right of active slide\n  if (centerMode) {\n    var right = (slidesToShow - 1) / 2 + 1;\n    if (parseInt(centerPadding) > 0) right += 1;\n    if (rtl && slidesToShow % 2 === 0) right += 1;\n    return right;\n  }\n  if (rtl) {\n    return 0;\n  }\n  return slidesToShow - 1;\n};\nvar slidesOnLeft = exports.slidesOnLeft = function slidesOnLeft(_ref2) {\n  var slidesToShow = _ref2.slidesToShow,\n    centerMode = _ref2.centerMode,\n    rtl = _ref2.rtl,\n    centerPadding = _ref2.centerPadding;\n  // returns no of slides on the left of active slide\n  if (centerMode) {\n    var left = (slidesToShow - 1) / 2 + 1;\n    if (parseInt(centerPadding) > 0) left += 1;\n    if (!rtl && slidesToShow % 2 === 0) left += 1;\n    return left;\n  }\n  if (rtl) {\n    return slidesToShow - 1;\n  }\n  return 0;\n};\nvar canUseDOM = exports.canUseDOM = function canUseDOM() {\n  return !!(typeof window !== \"undefined\" && window.document && window.document.createElement);\n};\nvar validSettings = exports.validSettings = Object.keys(_defaultProps[\"default\"]);\nfunction filterSettings(settings) {\n  return validSettings.reduce(function (acc, settingName) {\n    if (settings.hasOwnProperty(settingName)) {\n      acc[settingName] = settings[settingName];\n    }\n    return acc;\n  }, {});\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,aAAa,GAAGF,OAAO,CAACG,cAAc,GAAGH,OAAO,CAACI,WAAW,GAAGJ,OAAO,CAACK,SAAS,GAAGL,OAAO,CAACM,SAAS,GAAG,KAAK,CAAC;AACrHN,OAAO,CAACO,KAAK,GAAGA,KAAK;AACrBP,OAAO,CAACQ,aAAa,GAAG,KAAK,CAAC;AAC9BR,OAAO,CAACS,cAAc,GAAGA,cAAc;AACvCT,OAAO,CAACU,aAAa,GAAGV,OAAO,CAACW,UAAU,GAAGX,OAAO,CAACY,SAAS,GAAGZ,OAAO,CAACa,QAAQ,GAAGb,OAAO,CAACc,aAAa,GAAGd,OAAO,CAACe,YAAY,GAAGf,OAAO,CAACgB,YAAY,GAAGhB,OAAO,CAACiB,gBAAgB,GAAGjB,OAAO,CAACkB,kBAAkB,GAAGlB,OAAO,CAACmB,cAAc,GAAGnB,OAAO,CAACoB,iBAAiB,GAAGpB,OAAO,CAACqB,gBAAgB,GAAGrB,OAAO,CAACsB,YAAY,GAAGtB,OAAO,CAACuB,UAAU,GAAGvB,OAAO,CAACwB,gBAAgB,GAAGxB,OAAO,CAACyB,QAAQ,GAAGzB,OAAO,CAAC0B,YAAY,GAAG1B,OAAO,CAAC2B,WAAW,GAAG3B,OAAO,CAAC4B,kBAAkB,GAAG5B,OAAO,CAAC6B,cAAc,GAAG7B,OAAO,CAAC8B,iBAAiB,GAAG9B,OAAO,CAAC+B,aAAa,GAAG/B,OAAO,CAACgC,qBAAqB,GAAGhC,OAAO,CAACiC,YAAY,GAAGjC,OAAO,CAACkC,aAAa,GAAGlC,OAAO,CAACmC,qBAAqB,GAAGnC,OAAO,CAACoC,mBAAmB,GAAGpC,OAAO,CAACqC,SAAS,GAAG,KAAK,CAAC;AAC1rB,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIC,aAAa,GAAGF,sBAAsB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AACvE,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAChG,SAASE,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGvD,MAAM,CAACwD,IAAI,CAACH,CAAC,CAAC;EAAE,IAAIrD,MAAM,CAACyD,qBAAqB,EAAE;IAAE,IAAIV,CAAC,GAAG/C,MAAM,CAACyD,qBAAqB,CAACJ,CAAC,CAAC;IAAEC,CAAC,KAAKP,CAAC,GAAGA,CAAC,CAACW,MAAM,CAAC,UAAUJ,CAAC,EAAE;MAAE,OAAOtD,MAAM,CAAC2D,wBAAwB,CAACN,CAAC,EAAEC,CAAC,CAAC,CAACM,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEL,CAAC,CAACM,IAAI,CAACC,KAAK,CAACP,CAAC,EAAER,CAAC,CAAC;EAAE;EAAE,OAAOQ,CAAC;AAAE;AAC9P,SAASQ,aAAaA,CAACV,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,SAAS,CAACC,MAAM,EAAEX,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIS,SAAS,CAACV,CAAC,CAAC,GAAGU,SAAS,CAACV,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACpD,MAAM,CAACuD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACW,OAAO,CAAC,UAAUZ,CAAC,EAAE;MAAEa,eAAe,CAACd,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGtD,MAAM,CAACoE,yBAAyB,GAAGpE,MAAM,CAACqE,gBAAgB,CAAChB,CAAC,EAAErD,MAAM,CAACoE,yBAAyB,CAACb,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACpD,MAAM,CAACuD,CAAC,CAAC,CAAC,CAACW,OAAO,CAAC,UAAUZ,CAAC,EAAE;MAAEtD,MAAM,CAACC,cAAc,CAACoD,CAAC,EAAEC,CAAC,EAAEtD,MAAM,CAAC2D,wBAAwB,CAACJ,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASc,eAAeA,CAACvB,GAAG,EAAE0B,GAAG,EAAEnE,KAAK,EAAE;EAAEmE,GAAG,GAAGC,cAAc,CAACD,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAI1B,GAAG,EAAE;IAAE5C,MAAM,CAACC,cAAc,CAAC2C,GAAG,EAAE0B,GAAG,EAAE;MAAEnE,KAAK,EAAEA,KAAK;MAAEyD,UAAU,EAAE,IAAI;MAAEY,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE7B,GAAG,CAAC0B,GAAG,CAAC,GAAGnE,KAAK;EAAE;EAAE,OAAOyC,GAAG;AAAE;AAC3O,SAAS2B,cAAcA,CAAChB,CAAC,EAAE;EAAE,IAAImB,CAAC,GAAGC,YAAY,CAACpB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIT,OAAO,CAAC4B,CAAC,CAAC,GAAGA,CAAC,GAAGE,MAAM,CAACF,CAAC,CAAC;AAAE;AAC/G,SAASC,YAAYA,CAACpB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIR,OAAO,CAACS,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACP,MAAM,CAAC6B,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKxB,CAAC,EAAE;IAAE,IAAIqB,CAAC,GAAGrB,CAAC,CAACyB,IAAI,CAACvB,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIR,OAAO,CAAC4B,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKzB,CAAC,GAAGsB,MAAM,GAAGI,MAAM,EAAEzB,CAAC,CAAC;AAAE;AAC3T,SAAS9C,KAAKA,CAACwE,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAE;EAC7C,OAAOC,IAAI,CAACC,GAAG,CAACH,UAAU,EAAEE,IAAI,CAACE,GAAG,CAACL,MAAM,EAAEE,UAAU,CAAC,CAAC;AAC3D;AACA,IAAI/D,kBAAkB,GAAGlB,OAAO,CAACkB,kBAAkB,GAAG,SAASA,kBAAkBA,CAACmE,KAAK,EAAE;EACvF,IAAIC,aAAa,GAAG,CAAC,cAAc,EAAE,aAAa,EAAE,SAAS,CAAC;EAC9D,IAAI,CAACA,aAAa,CAACC,QAAQ,CAACF,KAAK,CAACG,UAAU,CAAC,EAAE;IAC7CH,KAAK,CAACI,cAAc,CAAC,CAAC;EACxB;AACF,CAAC;AACD,IAAItD,qBAAqB,GAAGnC,OAAO,CAACmC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACuD,IAAI,EAAE;EAC/F,IAAIC,cAAc,GAAG,EAAE;EACvB,IAAIC,UAAU,GAAGzE,cAAc,CAACuE,IAAI,CAAC;EACrC,IAAIG,QAAQ,GAAGvE,YAAY,CAACoE,IAAI,CAAC;EACjC,KAAK,IAAII,UAAU,GAAGF,UAAU,EAAEE,UAAU,GAAGD,QAAQ,EAAEC,UAAU,EAAE,EAAE;IACrE,IAAIJ,IAAI,CAACK,cAAc,CAACC,OAAO,CAACF,UAAU,CAAC,GAAG,CAAC,EAAE;MAC/CH,cAAc,CAAChC,IAAI,CAACmC,UAAU,CAAC;IACjC;EACF;EACA,OAAOH,cAAc;AACvB,CAAC;;AAED;AACA,IAAI3D,qBAAqB,GAAGhC,OAAO,CAACgC,qBAAqB,GAAG,SAASA,qBAAqBA,CAAC0D,IAAI,EAAE;EAC/F,IAAIO,cAAc,GAAG,EAAE;EACvB,IAAIL,UAAU,GAAGzE,cAAc,CAACuE,IAAI,CAAC;EACrC,IAAIG,QAAQ,GAAGvE,YAAY,CAACoE,IAAI,CAAC;EACjC,KAAK,IAAII,UAAU,GAAGF,UAAU,EAAEE,UAAU,GAAGD,QAAQ,EAAEC,UAAU,EAAE,EAAE;IACrEG,cAAc,CAACtC,IAAI,CAACmC,UAAU,CAAC;EACjC;EACA,OAAOG,cAAc;AACvB,CAAC;;AAED;AACA,IAAI9E,cAAc,GAAGnB,OAAO,CAACmB,cAAc,GAAG,SAASA,cAAcA,CAACuE,IAAI,EAAE;EAC1E,OAAOA,IAAI,CAACQ,YAAY,GAAG7E,gBAAgB,CAACqE,IAAI,CAAC;AACnD,CAAC;AACD,IAAIpE,YAAY,GAAGtB,OAAO,CAACsB,YAAY,GAAG,SAASA,YAAYA,CAACoE,IAAI,EAAE;EACpE,OAAOA,IAAI,CAACQ,YAAY,GAAG9E,iBAAiB,CAACsE,IAAI,CAAC;AACpD,CAAC;AACD,IAAIrE,gBAAgB,GAAGrB,OAAO,CAACqB,gBAAgB,GAAG,SAASA,gBAAgBA,CAACqE,IAAI,EAAE;EAChF,OAAOA,IAAI,CAACS,UAAU,GAAGjB,IAAI,CAACkB,KAAK,CAACV,IAAI,CAACW,YAAY,GAAG,CAAC,CAAC,IAAIC,QAAQ,CAACZ,IAAI,CAACa,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AAC7G,CAAC;AACD,IAAInF,iBAAiB,GAAGpB,OAAO,CAACoB,iBAAiB,GAAG,SAASA,iBAAiBA,CAACsE,IAAI,EAAE;EACnF,OAAOA,IAAI,CAACS,UAAU,GAAGjB,IAAI,CAACkB,KAAK,CAAC,CAACV,IAAI,CAACW,YAAY,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAIC,QAAQ,CAACZ,IAAI,CAACa,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGb,IAAI,CAACW,YAAY;AACvI,CAAC;;AAED;AACA,IAAI5E,QAAQ,GAAGzB,OAAO,CAACyB,QAAQ,GAAG,SAASA,QAAQA,CAAC+E,IAAI,EAAE;EACxD,OAAOA,IAAI,IAAIA,IAAI,CAACC,WAAW,IAAI,CAAC;AACtC,CAAC;AACD,IAAIpE,SAAS,GAAGrC,OAAO,CAACqC,SAAS,GAAG,SAASA,SAASA,CAACmE,IAAI,EAAE;EAC3D,OAAOA,IAAI,IAAIA,IAAI,CAACE,YAAY,IAAI,CAAC;AACvC,CAAC;AACD,IAAI5E,iBAAiB,GAAG9B,OAAO,CAAC8B,iBAAiB,GAAG,SAASA,iBAAiBA,CAAC6E,WAAW,EAAE;EAC1F,IAAIC,eAAe,GAAG9C,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK+C,SAAS,GAAG/C,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC/F,IAAIgD,KAAK,EAAEC,KAAK,EAAE3D,CAAC,EAAE4D,UAAU;EAC/BF,KAAK,GAAGH,WAAW,CAACM,MAAM,GAAGN,WAAW,CAACO,IAAI;EAC7CH,KAAK,GAAGJ,WAAW,CAACQ,MAAM,GAAGR,WAAW,CAACS,IAAI;EAC7ChE,CAAC,GAAG8B,IAAI,CAACmC,KAAK,CAACN,KAAK,EAAED,KAAK,CAAC;EAC5BE,UAAU,GAAG9B,IAAI,CAACoC,KAAK,CAAClE,CAAC,GAAG,GAAG,GAAG8B,IAAI,CAACqC,EAAE,CAAC;EAC1C,IAAIP,UAAU,GAAG,CAAC,EAAE;IAClBA,UAAU,GAAG,GAAG,GAAG9B,IAAI,CAACsC,GAAG,CAACR,UAAU,CAAC;EACzC;EACA,IAAIA,UAAU,IAAI,EAAE,IAAIA,UAAU,IAAI,CAAC,IAAIA,UAAU,IAAI,GAAG,IAAIA,UAAU,IAAI,GAAG,EAAE;IACjF,OAAO,MAAM;EACf;EACA,IAAIA,UAAU,IAAI,GAAG,IAAIA,UAAU,IAAI,GAAG,EAAE;IAC1C,OAAO,OAAO;EAChB;EACA,IAAIJ,eAAe,KAAK,IAAI,EAAE;IAC5B,IAAII,UAAU,IAAI,EAAE,IAAIA,UAAU,IAAI,GAAG,EAAE;MACzC,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,MAAM;IACf;EACF;EACA,OAAO,UAAU;AACnB,CAAC;;AAED;AACA,IAAI1G,SAAS,GAAGN,OAAO,CAACM,SAAS,GAAG,SAASA,SAASA,CAACoF,IAAI,EAAE;EAC3D,IAAI+B,KAAK,GAAG,IAAI;EAChB,IAAI,CAAC/B,IAAI,CAACgC,QAAQ,EAAE;IAClB,IAAIhC,IAAI,CAACS,UAAU,IAAIT,IAAI,CAACQ,YAAY,IAAIR,IAAI,CAACiC,UAAU,GAAG,CAAC,EAAE;MAC/DF,KAAK,GAAG,KAAK;IACf,CAAC,MAAM,IAAI/B,IAAI,CAACiC,UAAU,IAAIjC,IAAI,CAACW,YAAY,IAAIX,IAAI,CAACQ,YAAY,IAAIR,IAAI,CAACiC,UAAU,GAAGjC,IAAI,CAACW,YAAY,EAAE;MAC3GoB,KAAK,GAAG,KAAK;IACf;EACF;EACA,OAAOA,KAAK;AACd,CAAC;;AAED;AACA,IAAIjH,aAAa,GAAGR,OAAO,CAACQ,aAAa,GAAG,SAASA,aAAaA,CAACkF,IAAI,EAAEpC,IAAI,EAAE;EAC7E,IAAIsE,SAAS,GAAG,CAAC,CAAC;EAClBtE,IAAI,CAACU,OAAO,CAAC,UAAUI,GAAG,EAAE;IAC1B,OAAOwD,SAAS,CAACxD,GAAG,CAAC,GAAGsB,IAAI,CAACtB,GAAG,CAAC;EACnC,CAAC,CAAC;EACF,OAAOwD,SAAS;AAClB,CAAC;;AAED;AACA,IAAIpG,gBAAgB,GAAGxB,OAAO,CAACwB,gBAAgB,GAAG,SAASA,gBAAgBA,CAACkE,IAAI,EAAE;EAChF;EACA,IAAIiC,UAAU,GAAGrF,MAAM,CAAC,SAAS,CAAC,CAACuF,QAAQ,CAACC,KAAK,CAACpC,IAAI,CAACqC,QAAQ,CAAC;EAChE,IAAIC,QAAQ,GAAGtC,IAAI,CAACuC,OAAO;EAC3B,IAAIC,SAAS,GAAGhD,IAAI,CAACiD,IAAI,CAAC1G,QAAQ,CAACuG,QAAQ,CAAC,CAAC;EAC7C,IAAII,SAAS,GAAG1C,IAAI,CAAC2C,QAAQ,IAAI3C,IAAI,CAAC2C,QAAQ,CAACC,IAAI;EACnD,IAAIC,UAAU,GAAGrD,IAAI,CAACiD,IAAI,CAAC1G,QAAQ,CAAC2G,SAAS,CAAC,CAAC;EAC/C,IAAII,UAAU;EACd,IAAI,CAAC9C,IAAI,CAAC+C,QAAQ,EAAE;IAClB,IAAIC,gBAAgB,GAAGhD,IAAI,CAACS,UAAU,IAAIG,QAAQ,CAACZ,IAAI,CAACa,aAAa,CAAC,GAAG,CAAC;IAC1E,IAAI,OAAOb,IAAI,CAACa,aAAa,KAAK,QAAQ,IAAIb,IAAI,CAACa,aAAa,CAACoC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAClFD,gBAAgB,IAAIR,SAAS,GAAG,GAAG;IACrC;IACAM,UAAU,GAAGtD,IAAI,CAACiD,IAAI,CAAC,CAACD,SAAS,GAAGQ,gBAAgB,IAAIhD,IAAI,CAACW,YAAY,CAAC;EAC5E,CAAC,MAAM;IACLmC,UAAU,GAAGN,SAAS;EACxB;EACA,IAAIU,WAAW,GAAGZ,QAAQ,IAAI3F,SAAS,CAAC2F,QAAQ,CAACa,aAAa,CAAC,kBAAkB,CAAC,CAAC;EACnF,IAAIC,UAAU,GAAGF,WAAW,GAAGlD,IAAI,CAACW,YAAY;EAChD,IAAIH,YAAY,GAAGR,IAAI,CAACQ,YAAY,KAAKW,SAAS,GAAGnB,IAAI,CAACqD,YAAY,GAAGrD,IAAI,CAACQ,YAAY;EAC1F,IAAIR,IAAI,CAACsD,GAAG,IAAItD,IAAI,CAACQ,YAAY,KAAKW,SAAS,EAAE;IAC/CX,YAAY,GAAGyB,UAAU,GAAG,CAAC,GAAGjC,IAAI,CAACqD,YAAY;EACnD;EACA,IAAIhD,cAAc,GAAGL,IAAI,CAACK,cAAc,IAAI,EAAE;EAC9C,IAAIkD,YAAY,GAAG9G,qBAAqB,CAAC0B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;IAClFQ,YAAY,EAAEA,YAAY;IAC1BH,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC;EACHA,cAAc,GAAGA,cAAc,CAACmD,MAAM,CAACD,YAAY,CAAC;EACpD,IAAIE,KAAK,GAAG;IACVxB,UAAU,EAAEA,UAAU;IACtBa,UAAU,EAAEA,UAAU;IACtBN,SAAS,EAAEA,SAAS;IACpBK,UAAU,EAAEA,UAAU;IACtBrC,YAAY,EAAEA,YAAY;IAC1B0C,WAAW,EAAEA,WAAW;IACxBE,UAAU,EAAEA,UAAU;IACtB/C,cAAc,EAAEA;EAClB,CAAC;EACD,IAAIL,IAAI,CAAC0D,WAAW,KAAK,IAAI,IAAI1D,IAAI,CAAC2D,QAAQ,EAAE;IAC9CF,KAAK,CAAC,aAAa,CAAC,GAAG,SAAS;EAClC;EACA,OAAOA,KAAK;AACd,CAAC;AACD,IAAInI,YAAY,GAAGhB,OAAO,CAACgB,YAAY,GAAG,SAASA,YAAYA,CAAC0E,IAAI,EAAE;EACpE,IAAI4D,cAAc,GAAG5D,IAAI,CAAC4D,cAAc;IACtCC,SAAS,GAAG7D,IAAI,CAAC6D,SAAS;IAC1BC,IAAI,GAAG9D,IAAI,CAAC8D,IAAI;IAChB9B,QAAQ,GAAGhC,IAAI,CAACgC,QAAQ;IACxB+B,KAAK,GAAG/D,IAAI,CAAC+D,KAAK;IAClB9B,UAAU,GAAGjC,IAAI,CAACiC,UAAU;IAC5B+B,QAAQ,GAAGhE,IAAI,CAACgE,QAAQ;IACxBxD,YAAY,GAAGR,IAAI,CAACQ,YAAY;IAChCC,UAAU,GAAGT,IAAI,CAACS,UAAU;IAC5BwD,cAAc,GAAGjE,IAAI,CAACiE,cAAc;IACpCtD,YAAY,GAAGX,IAAI,CAACW,YAAY;IAChCuD,MAAM,GAAGlE,IAAI,CAACkE,MAAM;EACtB,IAAI7D,cAAc,GAAGL,IAAI,CAACK,cAAc;EACxC,IAAIuD,cAAc,IAAIC,SAAS,EAAE,OAAO,CAAC,CAAC;EAC1C,IAAIM,cAAc,GAAGJ,KAAK;IACxBK,UAAU;IACVC,aAAa;IACbC,SAAS;EACX,IAAIb,KAAK,GAAG,CAAC,CAAC;IACZc,SAAS,GAAG,CAAC,CAAC;EAChB,IAAIC,WAAW,GAAGxC,QAAQ,GAAG+B,KAAK,GAAGlJ,KAAK,CAACkJ,KAAK,EAAE,CAAC,EAAE9B,UAAU,GAAG,CAAC,CAAC;EACpE,IAAI6B,IAAI,EAAE;IACR,IAAI,CAAC9B,QAAQ,KAAK+B,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI9B,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9D,IAAI8B,KAAK,GAAG,CAAC,EAAE;MACbI,cAAc,GAAGJ,KAAK,GAAG9B,UAAU;IACrC,CAAC,MAAM,IAAI8B,KAAK,IAAI9B,UAAU,EAAE;MAC9BkC,cAAc,GAAGJ,KAAK,GAAG9B,UAAU;IACrC;IACA,IAAI+B,QAAQ,IAAI3D,cAAc,CAACC,OAAO,CAAC6D,cAAc,CAAC,GAAG,CAAC,EAAE;MAC1D9D,cAAc,GAAGA,cAAc,CAACmD,MAAM,CAACW,cAAc,CAAC;IACxD;IACAV,KAAK,GAAG;MACNI,SAAS,EAAE,IAAI;MACfrD,YAAY,EAAE2D,cAAc;MAC5B9D,cAAc,EAAEA,cAAc;MAC9BmE,WAAW,EAAEL;IACf,CAAC;IACDI,SAAS,GAAG;MACVV,SAAS,EAAE,KAAK;MAChBW,WAAW,EAAEL;IACf,CAAC;EACH,CAAC,MAAM;IACLC,UAAU,GAAGD,cAAc;IAC3B,IAAIA,cAAc,GAAG,CAAC,EAAE;MACtBC,UAAU,GAAGD,cAAc,GAAGlC,UAAU;MACxC,IAAI,CAACD,QAAQ,EAAEoC,UAAU,GAAG,CAAC,CAAC,KAAK,IAAInC,UAAU,GAAGgC,cAAc,KAAK,CAAC,EAAEG,UAAU,GAAGnC,UAAU,GAAGA,UAAU,GAAGgC,cAAc;IACjI,CAAC,MAAM,IAAI,CAACrJ,SAAS,CAACoF,IAAI,CAAC,IAAImE,cAAc,GAAG3D,YAAY,EAAE;MAC5D2D,cAAc,GAAGC,UAAU,GAAG5D,YAAY;IAC5C,CAAC,MAAM,IAAIC,UAAU,IAAI0D,cAAc,IAAIlC,UAAU,EAAE;MACrDkC,cAAc,GAAGnC,QAAQ,GAAGC,UAAU,GAAGA,UAAU,GAAG,CAAC;MACvDmC,UAAU,GAAGpC,QAAQ,GAAG,CAAC,GAAGC,UAAU,GAAG,CAAC;IAC5C,CAAC,MAAM,IAAIkC,cAAc,IAAIlC,UAAU,EAAE;MACvCmC,UAAU,GAAGD,cAAc,GAAGlC,UAAU;MACxC,IAAI,CAACD,QAAQ,EAAEoC,UAAU,GAAGnC,UAAU,GAAGtB,YAAY,CAAC,KAAK,IAAIsB,UAAU,GAAGgC,cAAc,KAAK,CAAC,EAAEG,UAAU,GAAG,CAAC;IAClH;IACA,IAAI,CAACpC,QAAQ,IAAImC,cAAc,GAAGxD,YAAY,IAAIsB,UAAU,EAAE;MAC5DmC,UAAU,GAAGnC,UAAU,GAAGtB,YAAY;IACxC;IACA0D,aAAa,GAAGrI,YAAY,CAACmC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MACtEI,UAAU,EAAE+D;IACd,CAAC,CAAC,CAAC;IACHG,SAAS,GAAGtI,YAAY,CAACmC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAClEI,UAAU,EAAEgE;IACd,CAAC,CAAC,CAAC;IACH,IAAI,CAACpC,QAAQ,EAAE;MACb,IAAIqC,aAAa,KAAKC,SAAS,EAAEH,cAAc,GAAGC,UAAU;MAC5DC,aAAa,GAAGC,SAAS;IAC3B;IACA,IAAIN,QAAQ,EAAE;MACZ3D,cAAc,GAAGA,cAAc,CAACmD,MAAM,CAAC/G,qBAAqB,CAAC0B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QACtGQ,YAAY,EAAE2D;MAChB,CAAC,CAAC,CAAC,CAAC;IACN;IACA,IAAI,CAACD,MAAM,EAAE;MACXT,KAAK,GAAG;QACNjD,YAAY,EAAE4D,UAAU;QACxBK,UAAU,EAAExI,WAAW,CAACkC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACjE0E,IAAI,EAAEJ;QACR,CAAC,CAAC,CAAC;QACHjE,cAAc,EAAEA,cAAc;QAC9BmE,WAAW,EAAEA;MACf,CAAC;IACH,CAAC,MAAM;MACLf,KAAK,GAAG;QACNI,SAAS,EAAE,IAAI;QACfrD,YAAY,EAAE4D,UAAU;QACxBK,UAAU,EAAEvI,kBAAkB,CAACiC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACxE0E,IAAI,EAAEL;QACR,CAAC,CAAC,CAAC;QACHhE,cAAc,EAAEA,cAAc;QAC9BmE,WAAW,EAAEA;MACf,CAAC;MACDD,SAAS,GAAG;QACVV,SAAS,EAAE,KAAK;QAChBrD,YAAY,EAAE4D,UAAU;QACxBK,UAAU,EAAExI,WAAW,CAACkC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACjE0E,IAAI,EAAEJ;QACR,CAAC,CAAC,CAAC;QACHK,SAAS,EAAE,IAAI;QACfH,WAAW,EAAEA;MACf,CAAC;IACH;EACF;EACA,OAAO;IACLf,KAAK,EAAEA,KAAK;IACZc,SAAS,EAAEA;EACb,CAAC;AACH,CAAC;AACD,IAAI7J,WAAW,GAAGJ,OAAO,CAACI,WAAW,GAAG,SAASA,WAAWA,CAACsF,IAAI,EAAE4E,OAAO,EAAE;EAC1E,IAAIC,WAAW,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAER,WAAW;EACpE,IAAIP,cAAc,GAAGjE,IAAI,CAACiE,cAAc;IACtCtD,YAAY,GAAGX,IAAI,CAACW,YAAY;IAChCsB,UAAU,GAAGjC,IAAI,CAACiC,UAAU;IAC5BzB,YAAY,GAAGR,IAAI,CAACQ,YAAY;IAChCyE,mBAAmB,GAAGjF,IAAI,CAACwE,WAAW;IACtCR,QAAQ,GAAGhE,IAAI,CAACgE,QAAQ;IACxBhC,QAAQ,GAAGhC,IAAI,CAACgC,QAAQ;EAC1BgD,YAAY,GAAG/C,UAAU,GAAGgC,cAAc,KAAK,CAAC;EAChDY,WAAW,GAAGG,YAAY,GAAG,CAAC,GAAG,CAAC/C,UAAU,GAAGzB,YAAY,IAAIyD,cAAc;EAC7E,IAAIW,OAAO,CAACM,OAAO,KAAK,UAAU,EAAE;IAClCH,WAAW,GAAGF,WAAW,KAAK,CAAC,GAAGZ,cAAc,GAAGtD,YAAY,GAAGkE,WAAW;IAC7EL,WAAW,GAAGhE,YAAY,GAAGuE,WAAW;IACxC,IAAIf,QAAQ,IAAI,CAAChC,QAAQ,EAAE;MACzB8C,WAAW,GAAGtE,YAAY,GAAGuE,WAAW;MACxCP,WAAW,GAAGM,WAAW,KAAK,CAAC,CAAC,GAAG7C,UAAU,GAAG,CAAC,GAAG6C,WAAW;IACjE;IACA,IAAI,CAAC9C,QAAQ,EAAE;MACbwC,WAAW,GAAGS,mBAAmB,GAAGhB,cAAc;IACpD;EACF,CAAC,MAAM,IAAIW,OAAO,CAACM,OAAO,KAAK,MAAM,EAAE;IACrCH,WAAW,GAAGF,WAAW,KAAK,CAAC,GAAGZ,cAAc,GAAGY,WAAW;IAC9DL,WAAW,GAAGhE,YAAY,GAAGuE,WAAW;IACxC,IAAIf,QAAQ,IAAI,CAAChC,QAAQ,EAAE;MACzBwC,WAAW,GAAG,CAAChE,YAAY,GAAGyD,cAAc,IAAIhC,UAAU,GAAG4C,WAAW;IAC1E;IACA,IAAI,CAAC7C,QAAQ,EAAE;MACbwC,WAAW,GAAGS,mBAAmB,GAAGhB,cAAc;IACpD;EACF,CAAC,MAAM,IAAIW,OAAO,CAACM,OAAO,KAAK,MAAM,EAAE;IACrC;IACAV,WAAW,GAAGI,OAAO,CAACb,KAAK,GAAGa,OAAO,CAACX,cAAc;EACtD,CAAC,MAAM,IAAIW,OAAO,CAACM,OAAO,KAAK,UAAU,EAAE;IACzC;IACAV,WAAW,GAAGI,OAAO,CAACb,KAAK;IAC3B,IAAI/B,QAAQ,EAAE;MACZ,IAAImD,SAAS,GAAG5J,gBAAgB,CAAC4C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1EwE,WAAW,EAAEA;MACf,CAAC,CAAC,CAAC;MACH,IAAIA,WAAW,GAAGI,OAAO,CAACpE,YAAY,IAAI2E,SAAS,KAAK,MAAM,EAAE;QAC9DX,WAAW,GAAGA,WAAW,GAAGvC,UAAU;MACxC,CAAC,MAAM,IAAIuC,WAAW,GAAGI,OAAO,CAACpE,YAAY,IAAI2E,SAAS,KAAK,OAAO,EAAE;QACtEX,WAAW,GAAGA,WAAW,GAAGvC,UAAU;MACxC;IACF;EACF,CAAC,MAAM,IAAI2C,OAAO,CAACM,OAAO,KAAK,OAAO,EAAE;IACtCV,WAAW,GAAGpF,MAAM,CAACwF,OAAO,CAACb,KAAK,CAAC;EACrC;EACA,OAAOS,WAAW;AACpB,CAAC;AACD,IAAI3I,UAAU,GAAGvB,OAAO,CAACuB,UAAU,GAAG,SAASA,UAAUA,CAAC4B,CAAC,EAAE2H,aAAa,EAAE9B,GAAG,EAAE;EAC/E,IAAI7F,CAAC,CAAC4H,MAAM,CAACC,OAAO,CAACC,KAAK,CAAC,uBAAuB,CAAC,IAAI,CAACH,aAAa,EAAE,OAAO,EAAE;EAChF,IAAI3H,CAAC,CAAC+H,OAAO,KAAK,EAAE,EAAE,OAAOlC,GAAG,GAAG,MAAM,GAAG,UAAU;EACtD,IAAI7F,CAAC,CAAC+H,OAAO,KAAK,EAAE,EAAE,OAAOlC,GAAG,GAAG,UAAU,GAAG,MAAM;EACtD,OAAO,EAAE;AACX,CAAC;AACD,IAAIrI,UAAU,GAAGX,OAAO,CAACW,UAAU,GAAG,SAASA,UAAUA,CAACwC,CAAC,EAAEgI,KAAK,EAAEC,SAAS,EAAE;EAC7EjI,CAAC,CAAC4H,MAAM,CAACC,OAAO,KAAK,KAAK,IAAI9J,kBAAkB,CAACiC,CAAC,CAAC;EACnD,IAAI,CAACgI,KAAK,IAAI,CAACC,SAAS,IAAIjI,CAAC,CAACkI,IAAI,CAACrF,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,EAAE;EACrE,OAAO;IACLsF,QAAQ,EAAE,IAAI;IACd3E,WAAW,EAAE;MACXM,MAAM,EAAE9D,CAAC,CAACoI,OAAO,GAAGpI,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,GAAGrI,CAAC,CAACsI,OAAO;MAClDtE,MAAM,EAAEhE,CAAC,CAACoI,OAAO,GAAGpI,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,CAACG,KAAK,GAAGvI,CAAC,CAACwI,OAAO;MAClDzE,IAAI,EAAE/D,CAAC,CAACoI,OAAO,GAAGpI,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,GAAGrI,CAAC,CAACsI,OAAO;MAChDrE,IAAI,EAAEjE,CAAC,CAACoI,OAAO,GAAGpI,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,CAACG,KAAK,GAAGvI,CAAC,CAACwI;IAC3C;EACF,CAAC;AACH,CAAC;AACD,IAAI/K,SAAS,GAAGZ,OAAO,CAACY,SAAS,GAAG,SAASA,SAASA,CAACuC,CAAC,EAAEuC,IAAI,EAAE;EAC9D;EACA,IAAIkG,SAAS,GAAGlG,IAAI,CAACkG,SAAS;IAC5BrC,SAAS,GAAG7D,IAAI,CAAC6D,SAAS;IAC1Bd,QAAQ,GAAG/C,IAAI,CAAC+C,QAAQ;IACxBoD,YAAY,GAAGnG,IAAI,CAACmG,YAAY;IAChCjF,eAAe,GAAGlB,IAAI,CAACkB,eAAe;IACtCoC,GAAG,GAAGtD,IAAI,CAACsD,GAAG;IACd9C,YAAY,GAAGR,IAAI,CAACQ,YAAY;IAChC4F,YAAY,GAAGpG,IAAI,CAACoG,YAAY;IAChCC,WAAW,GAAGrG,IAAI,CAACqG,WAAW;IAC9BC,MAAM,GAAGtG,IAAI,CAACsG,MAAM;IACpBC,MAAM,GAAGvG,IAAI,CAACuG,MAAM;IACpBC,OAAO,GAAGxG,IAAI,CAACwG,OAAO;IACtBvE,UAAU,GAAGjC,IAAI,CAACiC,UAAU;IAC5BgC,cAAc,GAAGjE,IAAI,CAACiE,cAAc;IACpCjC,QAAQ,GAAGhC,IAAI,CAACgC,QAAQ;IACxBf,WAAW,GAAGjB,IAAI,CAACiB,WAAW;IAC9BwF,UAAU,GAAGzG,IAAI,CAACyG,UAAU;IAC5BrD,UAAU,GAAGpD,IAAI,CAACoD,UAAU;IAC5BZ,SAAS,GAAGxC,IAAI,CAACwC,SAAS;EAC5B,IAAI0D,SAAS,EAAE;EACf,IAAIrC,SAAS,EAAE,OAAOrI,kBAAkB,CAACiC,CAAC,CAAC;EAC3C,IAAIsF,QAAQ,IAAIoD,YAAY,IAAIjF,eAAe,EAAE1F,kBAAkB,CAACiC,CAAC,CAAC;EACtE,IAAIkH,SAAS;IACXlB,KAAK,GAAG,CAAC,CAAC;EACZ,IAAIiD,OAAO,GAAG1K,YAAY,CAACgE,IAAI,CAAC;EAChCiB,WAAW,CAACO,IAAI,GAAG/D,CAAC,CAACoI,OAAO,GAAGpI,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,GAAGrI,CAAC,CAACsI,OAAO;EAC7D9E,WAAW,CAACS,IAAI,GAAGjE,CAAC,CAACoI,OAAO,GAAGpI,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,CAACG,KAAK,GAAGvI,CAAC,CAACwI,OAAO;EAC7DhF,WAAW,CAAC0F,WAAW,GAAGnH,IAAI,CAACoC,KAAK,CAACpC,IAAI,CAACoH,IAAI,CAACpH,IAAI,CAACqH,GAAG,CAAC5F,WAAW,CAACO,IAAI,GAAGP,WAAW,CAACM,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;EACnG,IAAIuF,mBAAmB,GAAGtH,IAAI,CAACoC,KAAK,CAACpC,IAAI,CAACoH,IAAI,CAACpH,IAAI,CAACqH,GAAG,CAAC5F,WAAW,CAACS,IAAI,GAAGT,WAAW,CAACQ,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;EACnG,IAAI,CAACP,eAAe,IAAI,CAACsF,OAAO,IAAIM,mBAAmB,GAAG,EAAE,EAAE;IAC5D,OAAO;MACLZ,SAAS,EAAE;IACb,CAAC;EACH;EACA,IAAIhF,eAAe,EAAED,WAAW,CAAC0F,WAAW,GAAGG,mBAAmB;EAClE,IAAIC,cAAc,GAAG,CAAC,CAACzD,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAKrC,WAAW,CAACO,IAAI,GAAGP,WAAW,CAACM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACvF,IAAIL,eAAe,EAAE6F,cAAc,GAAG9F,WAAW,CAACS,IAAI,GAAGT,WAAW,CAACQ,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EACpF,IAAIuF,QAAQ,GAAGxH,IAAI,CAACiD,IAAI,CAACR,UAAU,GAAGgC,cAAc,CAAC;EACrD,IAAIgD,cAAc,GAAG7K,iBAAiB,CAAC4D,IAAI,CAACiB,WAAW,EAAEC,eAAe,CAAC;EACzE,IAAIgG,gBAAgB,GAAGjG,WAAW,CAAC0F,WAAW;EAC9C,IAAI,CAAC3E,QAAQ,EAAE;IACb,IAAIxB,YAAY,KAAK,CAAC,KAAKyG,cAAc,KAAK,OAAO,IAAIA,cAAc,KAAK,MAAM,CAAC,IAAIzG,YAAY,GAAG,CAAC,IAAIwG,QAAQ,KAAKC,cAAc,KAAK,MAAM,IAAIA,cAAc,KAAK,IAAI,CAAC,IAAI,CAACrM,SAAS,CAACoF,IAAI,CAAC,KAAKiH,cAAc,KAAK,MAAM,IAAIA,cAAc,KAAK,IAAI,CAAC,EAAE;MAC3PC,gBAAgB,GAAGjG,WAAW,CAAC0F,WAAW,GAAGP,YAAY;MACzD,IAAIC,WAAW,KAAK,KAAK,IAAIC,MAAM,EAAE;QACnCA,MAAM,CAACW,cAAc,CAAC;QACtBxD,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI;MAC7B;IACF;EACF;EACA,IAAI,CAAC8C,MAAM,IAAIE,UAAU,EAAE;IACzBA,UAAU,CAACQ,cAAc,CAAC;IAC1BxD,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI;EACxB;EACA,IAAI,CAACV,QAAQ,EAAE;IACb,IAAI,CAACO,GAAG,EAAE;MACRqB,SAAS,GAAG+B,OAAO,GAAGQ,gBAAgB,GAAGH,cAAc;IACzD,CAAC,MAAM;MACLpC,SAAS,GAAG+B,OAAO,GAAGQ,gBAAgB,GAAGH,cAAc;IACzD;EACF,CAAC,MAAM;IACLpC,SAAS,GAAG+B,OAAO,GAAGQ,gBAAgB,IAAI9D,UAAU,GAAGZ,SAAS,CAAC,GAAGuE,cAAc;EACpF;EACA,IAAI7F,eAAe,EAAE;IACnByD,SAAS,GAAG+B,OAAO,GAAGQ,gBAAgB,GAAGH,cAAc;EACzD;EACAtD,KAAK,GAAGtF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsF,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAClDxC,WAAW,EAAEA,WAAW;IACxB0D,SAAS,EAAEA,SAAS;IACpBF,UAAU,EAAExI,WAAW,CAACkC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MACjE0E,IAAI,EAAEC;IACR,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAInF,IAAI,CAACsC,GAAG,CAACb,WAAW,CAACO,IAAI,GAAGP,WAAW,CAACM,MAAM,CAAC,GAAG/B,IAAI,CAACsC,GAAG,CAACb,WAAW,CAACS,IAAI,GAAGT,WAAW,CAACQ,MAAM,CAAC,GAAG,GAAG,EAAE;IAC3G,OAAOgC,KAAK;EACd;EACA,IAAIxC,WAAW,CAAC0F,WAAW,GAAG,EAAE,EAAE;IAChClD,KAAK,CAAC,SAAS,CAAC,GAAG,IAAI;IACvBjI,kBAAkB,CAACiC,CAAC,CAAC;EACvB;EACA,OAAOgG,KAAK;AACd,CAAC;AACD,IAAItI,QAAQ,GAAGb,OAAO,CAACa,QAAQ,GAAG,SAASA,QAAQA,CAACsC,CAAC,EAAEuC,IAAI,EAAE;EAC3D,IAAI4F,QAAQ,GAAG5F,IAAI,CAAC4F,QAAQ;IAC1BH,KAAK,GAAGzF,IAAI,CAACyF,KAAK;IAClBxE,WAAW,GAAGjB,IAAI,CAACiB,WAAW;IAC9BuB,SAAS,GAAGxC,IAAI,CAACwC,SAAS;IAC1B2E,cAAc,GAAGnH,IAAI,CAACmH,cAAc;IACpCjG,eAAe,GAAGlB,IAAI,CAACkB,eAAe;IACtCkC,UAAU,GAAGpD,IAAI,CAACoD,UAAU;IAC5B+C,YAAY,GAAGnG,IAAI,CAACmG,YAAY;IAChCD,SAAS,GAAGlG,IAAI,CAACkG,SAAS;IAC1BkB,OAAO,GAAGpH,IAAI,CAACoH,OAAO;IACtB5C,WAAW,GAAGxE,IAAI,CAACwE,WAAW;IAC9BhE,YAAY,GAAGR,IAAI,CAACQ,YAAY;IAChCwB,QAAQ,GAAGhC,IAAI,CAACgC,QAAQ;EAC1B,IAAI,CAAC4D,QAAQ,EAAE;IACb,IAAIH,KAAK,EAAEjK,kBAAkB,CAACiC,CAAC,CAAC;IAChC,OAAO,CAAC,CAAC;EACX;EACA,IAAI4J,QAAQ,GAAGnG,eAAe,GAAGkC,UAAU,GAAG+D,cAAc,GAAG3E,SAAS,GAAG2E,cAAc;EACzF,IAAIF,cAAc,GAAG7K,iBAAiB,CAAC6E,WAAW,EAAEC,eAAe,CAAC;EACpE;EACA,IAAIuC,KAAK,GAAG;IACVmC,QAAQ,EAAE,KAAK;IACfS,WAAW,EAAE,KAAK;IAClBH,SAAS,EAAE,KAAK;IAChBM,OAAO,EAAE,KAAK;IACdD,MAAM,EAAE,KAAK;IACb5B,SAAS,EAAE,IAAI;IACf1D,WAAW,EAAE,CAAC;EAChB,CAAC;EACD,IAAIiF,SAAS,EAAE;IACb,OAAOzC,KAAK;EACd;EACA,IAAI,CAACxC,WAAW,CAAC0F,WAAW,EAAE;IAC5B,OAAOlD,KAAK;EACd;EACA,IAAIxC,WAAW,CAAC0F,WAAW,GAAGU,QAAQ,EAAE;IACtC7L,kBAAkB,CAACiC,CAAC,CAAC;IACrB,IAAI2J,OAAO,EAAE;MACXA,OAAO,CAACH,cAAc,CAAC;IACzB;IACA,IAAIhF,UAAU,EAAEqF,QAAQ;IACxB,IAAIC,WAAW,GAAGvF,QAAQ,GAAGxB,YAAY,GAAGgE,WAAW;IACvD,QAAQyC,cAAc;MACpB,KAAK,MAAM;MACX,KAAK,IAAI;QACPK,QAAQ,GAAGC,WAAW,GAAGlL,aAAa,CAAC2D,IAAI,CAAC;QAC5CiC,UAAU,GAAGkE,YAAY,GAAG1L,cAAc,CAACuF,IAAI,EAAEsH,QAAQ,CAAC,GAAGA,QAAQ;QACrE7D,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC;QAC7B;MACF,KAAK,OAAO;MACZ,KAAK,MAAM;QACT6D,QAAQ,GAAGC,WAAW,GAAGlL,aAAa,CAAC2D,IAAI,CAAC;QAC5CiC,UAAU,GAAGkE,YAAY,GAAG1L,cAAc,CAACuF,IAAI,EAAEsH,QAAQ,CAAC,GAAGA,QAAQ;QACrE7D,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC;QAC7B;MACF;QACExB,UAAU,GAAGsF,WAAW;IAC5B;IACA9D,KAAK,CAAC,qBAAqB,CAAC,GAAGxB,UAAU;EAC3C,CAAC,MAAM;IACL;IACA,IAAIuF,WAAW,GAAGxL,YAAY,CAACgE,IAAI,CAAC;IACpCyD,KAAK,CAAC,YAAY,CAAC,GAAGvH,kBAAkB,CAACiC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAClF0E,IAAI,EAAE8C;IACR,CAAC,CAAC,CAAC;EACL;EACA,OAAO/D,KAAK;AACd,CAAC;AACD,IAAI/G,mBAAmB,GAAGpC,OAAO,CAACoC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACsD,IAAI,EAAE;EACzF,IAAIP,GAAG,GAAGO,IAAI,CAACgC,QAAQ,GAAGhC,IAAI,CAACiC,UAAU,GAAG,CAAC,GAAGjC,IAAI,CAACiC,UAAU;EAC/D,IAAIwF,UAAU,GAAGzH,IAAI,CAACgC,QAAQ,GAAGhC,IAAI,CAACW,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;EAC3D,IAAI+G,OAAO,GAAG1H,IAAI,CAACgC,QAAQ,GAAGhC,IAAI,CAACW,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;EACxD,IAAIgH,OAAO,GAAG,EAAE;EAChB,OAAOF,UAAU,GAAGhI,GAAG,EAAE;IACvBkI,OAAO,CAAC1J,IAAI,CAACwJ,UAAU,CAAC;IACxBA,UAAU,GAAGC,OAAO,GAAG1H,IAAI,CAACiE,cAAc;IAC1CyD,OAAO,IAAIlI,IAAI,CAACE,GAAG,CAACM,IAAI,CAACiE,cAAc,EAAEjE,IAAI,CAACW,YAAY,CAAC;EAC7D;EACA,OAAOgH,OAAO;AAChB,CAAC;AACD,IAAIlN,cAAc,GAAGH,OAAO,CAACG,cAAc,GAAG,SAASA,cAAcA,CAACuF,IAAI,EAAE+D,KAAK,EAAE;EACjF,IAAI6D,UAAU,GAAGlL,mBAAmB,CAACsD,IAAI,CAAC;EAC1C,IAAI6H,aAAa,GAAG,CAAC;EACrB,IAAI9D,KAAK,GAAG6D,UAAU,CAACA,UAAU,CAACvJ,MAAM,GAAG,CAAC,CAAC,EAAE;IAC7C0F,KAAK,GAAG6D,UAAU,CAACA,UAAU,CAACvJ,MAAM,GAAG,CAAC,CAAC;EAC3C,CAAC,MAAM;IACL,KAAK,IAAIyJ,CAAC,IAAIF,UAAU,EAAE;MACxB,IAAI7D,KAAK,GAAG6D,UAAU,CAACE,CAAC,CAAC,EAAE;QACzB/D,KAAK,GAAG8D,aAAa;QACrB;MACF;MACAA,aAAa,GAAGD,UAAU,CAACE,CAAC,CAAC;IAC/B;EACF;EACA,OAAO/D,KAAK;AACd,CAAC;AACD,IAAI1H,aAAa,GAAG/B,OAAO,CAAC+B,aAAa,GAAG,SAASA,aAAaA,CAAC2D,IAAI,EAAE;EACvE,IAAI+H,YAAY,GAAG/H,IAAI,CAACS,UAAU,GAAGT,IAAI,CAAC8C,UAAU,GAAGtD,IAAI,CAACkB,KAAK,CAACV,IAAI,CAACW,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;EAC5F,IAAIX,IAAI,CAACmG,YAAY,EAAE;IACrB,IAAI6B,WAAW;IACf,IAAIC,SAAS,GAAGjI,IAAI,CAACuC,OAAO;IAC5B,IAAI2F,MAAM,GAAGD,SAAS,CAACE,gBAAgB,IAAIF,SAAS,CAACE,gBAAgB,CAAC,cAAc,CAAC,IAAI,EAAE;IAC3FC,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,KAAK,CAAC,UAAUC,KAAK,EAAE;MACxC,IAAI,CAACvI,IAAI,CAAC+C,QAAQ,EAAE;QAClB,IAAIwF,KAAK,CAACC,UAAU,GAAGT,YAAY,GAAGhM,QAAQ,CAACwM,KAAK,CAAC,GAAG,CAAC,GAAGvI,IAAI,CAAC2E,SAAS,GAAG,CAAC,CAAC,EAAE;UAC/EqD,WAAW,GAAGO,KAAK;UACnB,OAAO,KAAK;QACd;MACF,CAAC,MAAM;QACL,IAAIA,KAAK,CAACE,SAAS,GAAG9L,SAAS,CAAC4L,KAAK,CAAC,GAAG,CAAC,GAAGvI,IAAI,CAAC2E,SAAS,GAAG,CAAC,CAAC,EAAE;UAChEqD,WAAW,GAAGO,KAAK;UACnB,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb,CAAC,CAAC;IACF,IAAI,CAACP,WAAW,EAAE;MAChB,OAAO,CAAC;IACV;IACA,IAAIU,YAAY,GAAG1I,IAAI,CAACsD,GAAG,KAAK,IAAI,GAAGtD,IAAI,CAACiC,UAAU,GAAGjC,IAAI,CAACQ,YAAY,GAAGR,IAAI,CAACQ,YAAY;IAC9F,IAAImI,eAAe,GAAGnJ,IAAI,CAACsC,GAAG,CAACkG,WAAW,CAACY,OAAO,CAAC7E,KAAK,GAAG2E,YAAY,CAAC,IAAI,CAAC;IAC7E,OAAOC,eAAe;EACxB,CAAC,MAAM;IACL,OAAO3I,IAAI,CAACiE,cAAc;EAC5B;AACF,CAAC;AACD,IAAIzJ,aAAa,GAAGF,OAAO,CAACE,aAAa,GAAG,SAASA,aAAaA,CAACwF,IAAI,EAAE6I,SAAS,EAAE;EAClF,OAAOA,SAAS,CAACC,MAAM,CAAC,UAAUvO,KAAK,EAAEmE,GAAG,EAAE;IAC5C,OAAOnE,KAAK,IAAIyF,IAAI,CAAC+I,cAAc,CAACrK,GAAG,CAAC;EAC1C,CAAC,EAAE,IAAI,CAAC,GAAG,IAAI,GAAGsK,OAAO,CAACC,KAAK,CAAC,eAAe,EAAEjJ,IAAI,CAAC;AACxD,CAAC;AACD,IAAI/D,WAAW,GAAG3B,OAAO,CAAC2B,WAAW,GAAG,SAASA,WAAWA,CAAC+D,IAAI,EAAE;EACjExF,aAAa,CAACwF,IAAI,EAAE,CAAC,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;EAC1F,IAAI6C,UAAU,EAAEqG,WAAW;EAC3B,IAAIC,aAAa,GAAGnJ,IAAI,CAACiC,UAAU,GAAG,CAAC,GAAGjC,IAAI,CAACW,YAAY;EAC3D,IAAI,CAACX,IAAI,CAAC+C,QAAQ,EAAE;IAClBF,UAAU,GAAG1G,cAAc,CAAC6D,IAAI,CAAC,GAAGA,IAAI,CAAC8C,UAAU;EACrD,CAAC,MAAM;IACLoG,WAAW,GAAGC,aAAa,GAAGnJ,IAAI,CAACkD,WAAW;EAChD;EACA,IAAIkG,KAAK,GAAG;IACVC,OAAO,EAAE,CAAC;IACVC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE;EACpB,CAAC;EACD,IAAIvJ,IAAI,CAACwJ,YAAY,EAAE;IACrB,IAAIC,eAAe,GAAG,CAACzJ,IAAI,CAAC+C,QAAQ,GAAG,cAAc,GAAG/C,IAAI,CAAC0E,IAAI,GAAG,eAAe,GAAG,mBAAmB,GAAG1E,IAAI,CAAC0E,IAAI,GAAG,UAAU;IAClI,IAAIgF,SAAS,GAAG,CAAC1J,IAAI,CAAC+C,QAAQ,GAAG,cAAc,GAAG/C,IAAI,CAAC0E,IAAI,GAAG,eAAe,GAAG,mBAAmB,GAAG1E,IAAI,CAAC0E,IAAI,GAAG,UAAU;IAC5H,IAAIiF,WAAW,GAAG,CAAC3J,IAAI,CAAC+C,QAAQ,GAAG,aAAa,GAAG/C,IAAI,CAAC0E,IAAI,GAAG,KAAK,GAAG,aAAa,GAAG1E,IAAI,CAAC0E,IAAI,GAAG,KAAK;IACxG0E,KAAK,GAAGjL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiL,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAClDK,eAAe,EAAEA,eAAe;MAChCC,SAAS,EAAEA,SAAS;MACpBC,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,IAAI3J,IAAI,CAAC+C,QAAQ,EAAE;MACjBqG,KAAK,CAAC,KAAK,CAAC,GAAGpJ,IAAI,CAAC0E,IAAI;IAC1B,CAAC,MAAM;MACL0E,KAAK,CAAC,MAAM,CAAC,GAAGpJ,IAAI,CAAC0E,IAAI;IAC3B;EACF;EACA,IAAI1E,IAAI,CAAC8D,IAAI,EAAEsF,KAAK,GAAG;IACrBC,OAAO,EAAE;EACX,CAAC;EACD,IAAIxG,UAAU,EAAEuG,KAAK,CAACQ,KAAK,GAAG/G,UAAU;EACxC,IAAIqG,WAAW,EAAEE,KAAK,CAACS,MAAM,GAAGX,WAAW;;EAE3C;EACA,IAAIY,MAAM,IAAI,CAACA,MAAM,CAACC,gBAAgB,IAAID,MAAM,CAACE,WAAW,EAAE;IAC5D,IAAI,CAAChK,IAAI,CAAC+C,QAAQ,EAAE;MAClBqG,KAAK,CAACa,UAAU,GAAGjK,IAAI,CAAC0E,IAAI,GAAG,IAAI;IACrC,CAAC,MAAM;MACL0E,KAAK,CAACc,SAAS,GAAGlK,IAAI,CAAC0E,IAAI,GAAG,IAAI;IACpC;EACF;EACA,OAAO0E,KAAK;AACd,CAAC;AACD,IAAIlN,kBAAkB,GAAG5B,OAAO,CAAC4B,kBAAkB,GAAG,SAASA,kBAAkBA,CAAC8D,IAAI,EAAE;EACtFxF,aAAa,CAACwF,IAAI,EAAE,CAAC,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;EAC9G,IAAIoJ,KAAK,GAAGnN,WAAW,CAAC+D,IAAI,CAAC;EAC7B;EACA,IAAIA,IAAI,CAACwJ,YAAY,EAAE;IACrBJ,KAAK,CAACG,gBAAgB,GAAG,oBAAoB,GAAGvJ,IAAI,CAACmK,KAAK,GAAG,KAAK,GAAGnK,IAAI,CAACoK,OAAO;IACjFhB,KAAK,CAACE,UAAU,GAAG,YAAY,GAAGtJ,IAAI,CAACmK,KAAK,GAAG,KAAK,GAAGnK,IAAI,CAACoK,OAAO;EACrE,CAAC,MAAM;IACL,IAAIpK,IAAI,CAAC+C,QAAQ,EAAE;MACjBqG,KAAK,CAACE,UAAU,GAAG,MAAM,GAAGtJ,IAAI,CAACmK,KAAK,GAAG,KAAK,GAAGnK,IAAI,CAACoK,OAAO;IAC/D,CAAC,MAAM;MACLhB,KAAK,CAACE,UAAU,GAAG,OAAO,GAAGtJ,IAAI,CAACmK,KAAK,GAAG,KAAK,GAAGnK,IAAI,CAACoK,OAAO;IAChE;EACF;EACA,OAAOhB,KAAK;AACd,CAAC;AACD,IAAIpN,YAAY,GAAG1B,OAAO,CAAC0B,YAAY,GAAG,SAASA,YAAYA,CAACgE,IAAI,EAAE;EACpE,IAAIA,IAAI,CAACqK,OAAO,EAAE;IAChB,OAAO,CAAC;EACV;EACA7P,aAAa,CAACwF,IAAI,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,WAAW,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;EACpL,IAAII,UAAU,GAAGJ,IAAI,CAACI,UAAU;IAC9BuC,QAAQ,GAAG3C,IAAI,CAAC2C,QAAQ;IACxBX,QAAQ,GAAGhC,IAAI,CAACgC,QAAQ;IACxBvB,UAAU,GAAGT,IAAI,CAACS,UAAU;IAC5BwB,UAAU,GAAGjC,IAAI,CAACiC,UAAU;IAC5BtB,YAAY,GAAGX,IAAI,CAACW,YAAY;IAChCsD,cAAc,GAAGjE,IAAI,CAACiE,cAAc;IACpCnB,UAAU,GAAG9C,IAAI,CAAC8C,UAAU;IAC5BN,SAAS,GAAGxC,IAAI,CAACwC,SAAS;IAC1B8H,aAAa,GAAGtK,IAAI,CAACsK,aAAa;IAClCpH,WAAW,GAAGlD,IAAI,CAACkD,WAAW;IAC9BY,IAAI,GAAG9D,IAAI,CAAC8D,IAAI;IAChBf,QAAQ,GAAG/C,IAAI,CAAC+C,QAAQ;EAC1B,IAAIgC,WAAW,GAAG,CAAC;EACnB,IAAIwF,UAAU;EACd,IAAI/F,WAAW;EACf,IAAIgG,cAAc,GAAG,CAAC;EACtB,IAAI1G,IAAI,IAAI9D,IAAI,CAACiC,UAAU,KAAK,CAAC,EAAE;IACjC,OAAO,CAAC;EACV;EACA,IAAIwI,cAAc,GAAG,CAAC;EACtB,IAAIzI,QAAQ,EAAE;IACZyI,cAAc,GAAG,CAAClO,YAAY,CAACyD,IAAI,CAAC,CAAC,CAAC;IACtC;IACA,IAAIiC,UAAU,GAAGgC,cAAc,KAAK,CAAC,IAAI7D,UAAU,GAAG6D,cAAc,GAAGhC,UAAU,EAAE;MACjFwI,cAAc,GAAG,EAAErK,UAAU,GAAG6B,UAAU,GAAGtB,YAAY,IAAIP,UAAU,GAAG6B,UAAU,CAAC,GAAGA,UAAU,GAAGgC,cAAc,CAAC;IACtH;IACA;IACA,IAAIxD,UAAU,EAAE;MACdgK,cAAc,IAAI7J,QAAQ,CAACD,YAAY,GAAG,CAAC,CAAC;IAC9C;EACF,CAAC,MAAM;IACL,IAAIsB,UAAU,GAAGgC,cAAc,KAAK,CAAC,IAAI7D,UAAU,GAAG6D,cAAc,GAAGhC,UAAU,EAAE;MACjFwI,cAAc,GAAG9J,YAAY,GAAGsB,UAAU,GAAGgC,cAAc;IAC7D;IACA,IAAIxD,UAAU,EAAE;MACdgK,cAAc,GAAG7J,QAAQ,CAACD,YAAY,GAAG,CAAC,CAAC;IAC7C;EACF;EACAoE,WAAW,GAAG0F,cAAc,GAAG3H,UAAU;EACzC0H,cAAc,GAAGC,cAAc,GAAGvH,WAAW;EAC7C,IAAI,CAACH,QAAQ,EAAE;IACbwH,UAAU,GAAGnK,UAAU,GAAG0C,UAAU,GAAG,CAAC,CAAC,GAAGiC,WAAW;EACzD,CAAC,MAAM;IACLwF,UAAU,GAAGnK,UAAU,GAAG8C,WAAW,GAAG,CAAC,CAAC,GAAGsH,cAAc;EAC7D;EACA,IAAIF,aAAa,KAAK,IAAI,EAAE;IAC1B,IAAII,gBAAgB;IACpB,IAAIC,SAAS,GAAGhI,QAAQ,IAAIA,QAAQ,CAACC,IAAI;IACzC8H,gBAAgB,GAAGtK,UAAU,GAAG7D,YAAY,CAACyD,IAAI,CAAC;IAClDwE,WAAW,GAAGmG,SAAS,IAAIA,SAAS,CAACC,UAAU,CAACF,gBAAgB,CAAC;IACjEH,UAAU,GAAG/F,WAAW,GAAGA,WAAW,CAACgE,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;IAC1D,IAAI/H,UAAU,KAAK,IAAI,EAAE;MACvBiK,gBAAgB,GAAG1I,QAAQ,GAAG5B,UAAU,GAAG7D,YAAY,CAACyD,IAAI,CAAC,GAAGI,UAAU;MAC1EoE,WAAW,GAAGmG,SAAS,IAAIA,SAAS,CAACtI,QAAQ,CAACqI,gBAAgB,CAAC;MAC/DH,UAAU,GAAG,CAAC;MACd,KAAK,IAAIhC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGmC,gBAAgB,EAAEnC,KAAK,EAAE,EAAE;QACrDgC,UAAU,IAAII,SAAS,IAAIA,SAAS,CAACtI,QAAQ,CAACkG,KAAK,CAAC,IAAIoC,SAAS,CAACtI,QAAQ,CAACkG,KAAK,CAAC,CAACxH,WAAW;MAC/F;MACAwJ,UAAU,IAAI3J,QAAQ,CAACZ,IAAI,CAACa,aAAa,CAAC;MAC1C0J,UAAU,IAAI/F,WAAW,IAAI,CAAChC,SAAS,GAAGgC,WAAW,CAACzD,WAAW,IAAI,CAAC;IACxE;EACF;EACA,OAAOwJ,UAAU;AACnB,CAAC;AACD,IAAIhO,YAAY,GAAGjC,OAAO,CAACiC,YAAY,GAAG,SAASA,YAAYA,CAACyD,IAAI,EAAE;EACpE,IAAIA,IAAI,CAACqK,OAAO,IAAI,CAACrK,IAAI,CAACgC,QAAQ,EAAE;IAClC,OAAO,CAAC;EACV;EACA,IAAIhC,IAAI,CAACsK,aAAa,EAAE;IACtB,OAAOtK,IAAI,CAACiC,UAAU;EACxB;EACA,OAAOjC,IAAI,CAACW,YAAY,IAAIX,IAAI,CAACS,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;AACtD,CAAC;AACD,IAAIjE,aAAa,GAAGlC,OAAO,CAACkC,aAAa,GAAG,SAASA,aAAaA,CAACwD,IAAI,EAAE;EACvE,IAAIA,IAAI,CAACqK,OAAO,IAAI,CAACrK,IAAI,CAACgC,QAAQ,EAAE;IAClC,OAAO,CAAC;EACV;EACA,OAAOhC,IAAI,CAACiC,UAAU;AACxB,CAAC;AACD,IAAI9F,cAAc,GAAG7B,OAAO,CAAC6B,cAAc,GAAG,SAASA,cAAcA,CAAC6D,IAAI,EAAE;EAC1E,OAAOA,IAAI,CAACiC,UAAU,KAAK,CAAC,GAAG,CAAC,GAAG1F,YAAY,CAACyD,IAAI,CAAC,GAAGA,IAAI,CAACiC,UAAU,GAAGzF,aAAa,CAACwD,IAAI,CAAC;AAC/F,CAAC;AACD,IAAIzE,gBAAgB,GAAGjB,OAAO,CAACiB,gBAAgB,GAAG,SAASA,gBAAgBA,CAACyE,IAAI,EAAE;EAChF,IAAIA,IAAI,CAACwE,WAAW,GAAGxE,IAAI,CAACQ,YAAY,EAAE;IACxC,IAAIR,IAAI,CAACwE,WAAW,GAAGxE,IAAI,CAACQ,YAAY,GAAGpF,aAAa,CAAC4E,IAAI,CAAC,EAAE;MAC9D,OAAO,MAAM;IACf;IACA,OAAO,OAAO;EAChB,CAAC,MAAM;IACL,IAAIA,IAAI,CAACwE,WAAW,GAAGxE,IAAI,CAACQ,YAAY,GAAGnF,YAAY,CAAC2E,IAAI,CAAC,EAAE;MAC7D,OAAO,OAAO;IAChB;IACA,OAAO,MAAM;EACf;AACF,CAAC;AACD,IAAI5E,aAAa,GAAGd,OAAO,CAACc,aAAa,GAAG,SAASA,aAAaA,CAACyP,IAAI,EAAE;EACvE,IAAIlK,YAAY,GAAGkK,IAAI,CAAClK,YAAY;IAClCF,UAAU,GAAGoK,IAAI,CAACpK,UAAU;IAC5B6C,GAAG,GAAGuH,IAAI,CAACvH,GAAG;IACdzC,aAAa,GAAGgK,IAAI,CAAChK,aAAa;EACpC;EACA,IAAIJ,UAAU,EAAE;IACd,IAAIqK,KAAK,GAAG,CAACnK,YAAY,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;IACtC,IAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,EAAEiK,KAAK,IAAI,CAAC;IAC3C,IAAIxH,GAAG,IAAI3C,YAAY,GAAG,CAAC,KAAK,CAAC,EAAEmK,KAAK,IAAI,CAAC;IAC7C,OAAOA,KAAK;EACd;EACA,IAAIxH,GAAG,EAAE;IACP,OAAO,CAAC;EACV;EACA,OAAO3C,YAAY,GAAG,CAAC;AACzB,CAAC;AACD,IAAItF,YAAY,GAAGf,OAAO,CAACe,YAAY,GAAG,SAASA,YAAYA,CAAC0P,KAAK,EAAE;EACrE,IAAIpK,YAAY,GAAGoK,KAAK,CAACpK,YAAY;IACnCF,UAAU,GAAGsK,KAAK,CAACtK,UAAU;IAC7B6C,GAAG,GAAGyH,KAAK,CAACzH,GAAG;IACfzC,aAAa,GAAGkK,KAAK,CAAClK,aAAa;EACrC;EACA,IAAIJ,UAAU,EAAE;IACd,IAAIiE,IAAI,GAAG,CAAC/D,YAAY,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;IACrC,IAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,EAAE6D,IAAI,IAAI,CAAC;IAC1C,IAAI,CAACpB,GAAG,IAAI3C,YAAY,GAAG,CAAC,KAAK,CAAC,EAAE+D,IAAI,IAAI,CAAC;IAC7C,OAAOA,IAAI;EACb;EACA,IAAIpB,GAAG,EAAE;IACP,OAAO3C,YAAY,GAAG,CAAC;EACzB;EACA,OAAO,CAAC;AACV,CAAC;AACD,IAAIhG,SAAS,GAAGL,OAAO,CAACK,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;EACvD,OAAO,CAAC,EAAE,OAAOmP,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACkB,QAAQ,IAAIlB,MAAM,CAACkB,QAAQ,CAACC,aAAa,CAAC;AAC9F,CAAC;AACD,IAAIjQ,aAAa,GAAGV,OAAO,CAACU,aAAa,GAAGZ,MAAM,CAACwD,IAAI,CAACb,aAAa,CAAC,SAAS,CAAC,CAAC;AACjF,SAAShC,cAAcA,CAACmQ,QAAQ,EAAE;EAChC,OAAOlQ,aAAa,CAAC8N,MAAM,CAAC,UAAUqC,GAAG,EAAEC,WAAW,EAAE;IACtD,IAAIF,QAAQ,CAACnC,cAAc,CAACqC,WAAW,CAAC,EAAE;MACxCD,GAAG,CAACC,WAAW,CAAC,GAAGF,QAAQ,CAACE,WAAW,CAAC;IAC1C;IACA,OAAOD,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}